package cn.yizhoucp.ump.api.vo.activity.doubleEgg2023;

import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.activity.base.GiftVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class SceneVO implements Serializable {

    private static final long serialVersionUID = 4747658512065852111L;

    private Integer sceneIndex;
    private UserVO user;
    private UserVO partener;
    private Integer sceneCpValue;
    private List<GiftVO> scenePrize;

}
