package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 520 活动
 *
 * @author: lianghu
 */
@FeignClient(value = "ump-services", contextId = "richmanActivity")
public interface RichmanFeignService {

    /**
     * 开启 IM 页处理（更新浮标数字）
     *
     * @param toUid
     * @return cn.yizhoucp.ms.core.base.Result<java.lang.Boolean>
     */
    @RequestMapping("/api/inner/activity/richman/open-im-chat-handle")
    Result<Boolean> openImChatHandle(@RequestParam("toUid") Long toUid);

    /**
     * 获取骰子数量
     *
     * @param toUid
     * @return cn.yizhoucp.ms.core.base.Result<java.lang.Long>
     */
    @RequestMapping("/api/inner/activity/richman/get-dice-num")
    Result<Integer> getDiceNumber(@RequestParam("toUid") Long toUid);


    /**
     * 根据是否有亲密对象获取蒙层类别
     * 客户端使用
     *
     * @param userIds
     * @return
     */
    @GetMapping("/api/inner/activity/richman/hasIntimacy")
    Result hasIntimacy(@RequestParam("userIds") String userIds);


    /**
     * 获取活动人员列表
     *
     * @return
     */
    @GetMapping("/api/inner/activity/richman/getUser")
    Result getRichManUser(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize);

    /**
     * H5初始化页面
     *
     * @param toUid
     * @param chatId
     * @return
     */
    @GetMapping("/api/inner/activity/richMan/pageInfo")
    Result pageInfo(@RequestParam("toUid") Long toUid, @RequestParam("chatId") String chatId);

    /**
     * 投掷骰子
     */
    @GetMapping("/api/inner/activity/richMan/play")
    Result playDice(@RequestParam("toUid") Long toUid);


    /**
     * 基金发放
     *
     * @param toUid
     * @param type
     * @return
     */
    @GetMapping("/api/inner/activity/richMan/reward")
    Result<Integer> reward(@RequestParam("toUid") Long toUid, @RequestParam("type") String type);


    /**
     * 奔赴记录
     *
     * @return
     */
    @GetMapping("/api/inner/activity/richMan/get-record")
    Result getUserSuccess();

    /**
     * 抽取倍率
     * @param toUid
     * @return
     */
    @GetMapping("/api/inner/activity/richMan/magnification")
    Result<String> magnification(@RequestParam("toUid") Long toUid);

}
