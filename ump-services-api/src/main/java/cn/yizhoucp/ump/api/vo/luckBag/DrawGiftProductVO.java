package cn.yizhoucp.ump.api.vo.luckBag;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 抽奖礼物展示
 * @date 2022-09-04 11:42
 */
@Data
public class DrawGiftProductVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String productType;

    private String about;

    private String giftKey;

    private String icon;

    private String name;

    private Long needCoin;

    private String status;

    private Long toPoint;

    private Long appId;

    /**
     * 礼物角标
     */
    private String topLeftIcon;

    /**
     * 右上角角标
     */
    private String topRightMark;

    /**
     * 中间顶部角标
     */
    private String topCenterMark;

    /**
     * 礼物排序序列号
     */
    private Integer serialNumber;

    /**
     * 礼物动效图
     */
    private String dynamic;

    /**
     * 礼物动效hash值
     */
    private String dynamicHash;

    /**
     * 上架时间戳
     */
    private Long onShelfTimestamp;

    /**
     * 下架时间戳
     */
    private Long offShelfTimestamp;

    /**
     * 上架状态 ON - 已上架 ONING - 上架中  OFFING - 下架中 OFF - 已下架
     */
    private String onShelfStatus;

    /**
     * 礼物类型
     */
    private String giftType;

    /**
     * 礼物 banner 配置
     */
    private String giftBanner;

    /**
     * 是否在礼物墙显示 SHOW - 显示  NOT_SHOW - 不显示
     */
    private String wallShow;

    /**
     * 能否在礼物墙赠送  CAN - 能 CAN_NOT - 不能
     */
    private String wallSend;

    /**
     * 礼物获取方式
     */
    private String obtainWay;

    /**
     * 礼物页号
     */
    private Integer giftPage;

    /**
     * 礼物位置
     */
    private Integer giftIndex;

    /**
     * 是否上架过
     */
    private Boolean hasApply;

    private Date createTime;

    private Date updateTime;

    /**
     * 上架时间（yyyy-MM-dd HH:mm:ss）
     */
    private String onShelfTimestampFormat;

    /**
     * 下架时间（yyyy-MM-dd HH:mm:ss）
     */
    private String offShelfTimestampFormat;

    /**
     * uuid
     */
    private String uuid;

    /** 字体颜色 */
    private String textColor;

    /** 背景图（边框） */
    private String background;

    /** 背景图（文字） */
    private String textBackground;

    /** 角标 */
    private String cornerMark;

    /** 排序 */
    private Integer sort;

    /** 礼物标签 ["ordinary"] */
    private String giftLabel;

    private List<DrawGiftProductVO> coinGiftProductList = new ArrayList<>();

}