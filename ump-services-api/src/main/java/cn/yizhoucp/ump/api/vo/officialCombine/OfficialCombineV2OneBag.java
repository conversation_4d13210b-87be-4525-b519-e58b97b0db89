package cn.yizhoucp.ump.api.vo.officialCombine;

import cn.yizhoucp.ump.api.vo.officialCombine.inner.GiftItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 单个礼包的信息
 * @date 2024-09-03 15:08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfficialCombineV2OneBag {
    private Long id;
    /** 礼包名称 */
    private String  name;
    /** 类型 */
    private String  type;
    /** 礼包价格 */
    private Long  totalCoin;
    /** 总价值 */
    private String  totalValueText;
    /** 礼包礼物 */
    private List<GiftItem> giftList;
    /** 限定内容 */
    private List<GiftItem> limitList;
    /** 礼物时长 */
    private String limitTime;
}
