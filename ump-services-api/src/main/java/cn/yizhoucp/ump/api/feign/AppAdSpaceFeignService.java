package cn.yizhoucp.ump.api.feign;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.appAdSpace.AppAdSpaceVO;
import cn.yizhoucp.ump.api.vo.appAdSpace.SingleChatBuoyVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 广告位资源
 *
 * @author: lianghu
 */
@FeignClient(value = "ump-services", contextId = "app-ad-space")
public interface AppAdSpaceFeignService {

    /**
     * 获取资源位列表（已过期）
     *
     * @param appId
     * @param unionId
     * @param type
     * @return
     */
    @Deprecated
    @GetMapping("/api/inner/app-ad-space/get-list-by-type")
    Result<List<JSONObject>> getAppAdSpaceList(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("type") String type);

    /**
     * 获取资源位列表
     *
     * @param appId
     * @param unionId
     * @param uid
     * @return
     */
    @GetMapping("/api/inner/app-ad-space/get-all")
    Result<List<AppAdSpaceVO>> getAll(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    /**
     * 根据类型获取
     *
     * @param appId
     * @param unionId
     * @param uid
     * @param type
     * @param roomId
     * @return
     */
    @GetMapping("/api/inner/app-ad-space/get-by-type")
    Result<List<AppAdSpaceVO>> getByType(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("type") String type, @RequestParam("toUid") Long toUid, @RequestParam("roomId") Long roomId);

    /**
     * 获取私聊浮标
     *
     * @param uid      用户id
     * @param otherUid 对方id
     * @return SingleChatBuoyVO
     */
    @GetMapping("/api/inner/app-ad-space/get-single-chat-buoy")
    Result<SingleChatBuoyVO> getSingleChatBuoy(@RequestParam("vestChannel") String vestChannel, @RequestParam("uid") Long uid, @RequestParam("otherUid") Long otherUid, @RequestParam("versionName") String versionName);

    @GetMapping("/api/inner/app-ad-space/get-by-belong-activity-and-type")
    Result<List<AppAdSpaceVO>> getByBelongActivityAndType(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("type") String type,@RequestParam("activityCode") String activityCode, @RequestParam("toUid") Long toUid, @RequestParam("roomId") Long roomId);

}
