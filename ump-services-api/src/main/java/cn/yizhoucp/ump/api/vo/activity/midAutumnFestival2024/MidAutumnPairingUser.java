package cn.yizhoucp.ump.api.vo.activity.midAutumnFestival2024;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MidAutumnPairingUser {
    private Long userId;
    private String userName;
    private String userAvatar;
    private Boolean isPaired;
    private String status;
    private Long moonValue;
    private String pairingTime;
    private Date lastChatTime;
}
