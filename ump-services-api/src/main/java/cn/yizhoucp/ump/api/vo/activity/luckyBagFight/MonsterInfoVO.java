package cn.yizhoucp.ump.api.vo.activity.luckyBagFight;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 怪兽VO
 * @date 2022-12-12 11:53
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonsterInfoVO {
    /** 等级 缓存存储 */
    private Integer level;
    /** 怪兽码 缓存存储 */
    private String code;
    /** 名称 */
    private String name;
    /** 图片 */
    private String icon;
    /** image 客户端展示用这个 */
    private String image;
    /** 状态 未解锁/攻击中/已击杀 LuckBagFightMonsterStatusEnum 缓存存储 */
    private String status;
    /** 当前血量 */
    private Integer currentHp;
    /** 最大血量 */
    private Integer maxHp;
    /** 血量百分比 */
    private Double progress;
    /** 宝箱规则展示的提示内容 */
    private String showRewardRuleContent;

    /** 选中数据 缓存存储 */
    private Boolean selected;

    /** 已经参与 */
//    private Boolean hadJoin;

    /** 已经领取奖励 */
    private Boolean hadReward;

    /** 奖励内容 */
    private MonsterRewardVO reward;

}
