package cn.yizhoucp.ump.api.vo.activity.rushSky.inner;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 任务信息
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskItem implements Serializable {

    /** 任务 code */
    private String taskCode;
    /** 任务路由 */
    private String taskRoute;
    /** 任务标题 */
    private String taskTitle;
    /** 任务描述 */
    private String taskDesc;
    /** 任务状态 */
    private Integer taskStatus;
    /** 任务拓展信息 */
    private List<Integer> extData;

}
