package cn.yizhoucp.ump.api.vo.luckBag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityInfoVO {

    /** 全服福气值进度 （0~100） */
    private Integer luckyProgress;
    /** 活动文案 （全服福气值：23418/77777） */
    private String text;
    /** 用户参与活动文字描述 */
    private String userJoinText;
    /** 活动文案颜色 */
    private String textColor;
    /** 上一轮获胜者头像 */
    private String lastUserAvatar;
    /** 上一轮奖励 icon */
    private String lastPrizeIcon;
    /** 开始时间 */
    private Long startTime;
    /** 结束时间 */
    private Long endTime;

}
