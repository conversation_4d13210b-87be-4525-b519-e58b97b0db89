package cn.yizhoucp.ump.api.feign;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ump.api.vo.AstrologyGuideVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "ump-services", contextId = "astrology-guide")
public interface AstrologyGuideFeignService {

    @GetMapping("/api/inner/ump/astrology-guide/get-user-show-guide-status")
    Result<AstrologyGuideVO> getUserShowGuideStatus(@RequestParam("uid") Long uid, @RequestParam("create") Boolean create);

    @GetMapping("/api/inner/ump/astrology-guide/process-next")
    Result<Boolean> processNext(@RequestParam("uid") Long uid, @RequestParam("curStep") Integer curStep);
}
