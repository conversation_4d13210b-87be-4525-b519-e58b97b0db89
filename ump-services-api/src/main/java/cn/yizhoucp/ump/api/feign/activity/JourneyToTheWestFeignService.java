package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.JourneyToTheWestExchangeGift;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.JourneyToTheWestIndexVO;
import cn.yizhoucp.ump.api.vo.activity.journeyToTheWest.ThemeGiftInfo;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "ump-services",
        contextId = "journey-to-the-West"
)
public interface JourneyToTheWestFeignService {

    /**
     * 获取主题信息
     *
     * @param themeId
     * @return
     */
    @GetMapping("/api/inner/activity/journey_to_the_west/theme_reward")
    Result<ThemeGiftInfo> getTheme(@RequestParam("themeId") Long themeId);

    /**
     * 升级
     *
     * @return
     */
    @GetMapping("/api/inner/activity/journey_to_the_west/upgrade")
    Result<Boolean> upgrade();


    @GetMapping("/api/inner/activity/journey_to_the_west/claim_reward")
    Result<Object> claimReward(@RequestParam("scene") String scene, @RequestParam(value = "rewardKey",required = false) String rewardKey, @RequestParam(value = "extData", required = false) String extData, @RequestParam(value = "toUid", required = false) Long toUid);

    @GetMapping("/api/inner/activity/journey_to_the_west/get-friend-list")
    Result<List<JourneyToTheWestIndexVO.UserVO>> getFriends();

    /**
     * 绑定密友
     *
     * @param toUid
     * @return
     */
    @GetMapping("/api/inner/activity/journey_to_the_west/bind-friend")
    Result<Boolean> bindFriend(@RequestParam("toUid") Long toUid);

    @GetMapping("/api/inner/activity/journey_to_the_west/get-exchange-gift")
    Result<List<JourneyToTheWestExchangeGift>> getExchangeGift();

    @GetMapping("/api/inner/activity/journey_to_the_west/exchange")
    Result<Boolean> exchange(@RequestParam("giftKey") String giftKey);

    @GetMapping("/api/inner/activity/journey_to_the_west/draw-log")
    Result<Object> getDrawLog(@RequestParam("activityCode") String activityCode, @RequestParam("poolCode") String poolCode);

    @GetMapping("/api/inner/activity/journey_to_the_west/draw")
    Result<DrawReturn> draw(@RequestParam(value = "type", required = false) String type,@RequestParam(value = "poolCode",required = false) String poolCode,@RequestParam(value = "times",required = false) Integer times,@RequestParam(value = "extValue",required = false) String extValue);

    @GetMapping("/api/inner/activity/journey_to_the_west/get_rank")
     Result<RankVO> getRank(@RequestParam(value = "toUid", required = false) Long toUid);

}
