package cn.yizhoucp.ump.api.vo.officialCombine.inner;

import cn.yizhoucp.ms.core.vo.landingservices.family.FamilyVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 官宣墙
 * <p>
 * 字段名与旧版保持相同，降低 h5 兼容工作量
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfficialCombineWallItem implements Serializable {

    private Long id;

    /**
     * 应用id
     */
    private Long appId;

    /**
     * 官宣开始时间
     */
    private String propagandaStartTime;

    /**
     * 官宣结束时间
     */
    private String propagandaEndTime;

    /**
     * 男生信息
     */
    private UserBaseVO manUser;

    /**
     * 女生信息
     */
    private UserBaseVO womanUser;

    /**
     * 家族id
     */
    private FamilyVO familyInfo;

    /**
     * 语音房id
     */
    private RoomVO voiceRoomInfo;

    /**
     * 戒指图标
     */
    private String ringIcon;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
