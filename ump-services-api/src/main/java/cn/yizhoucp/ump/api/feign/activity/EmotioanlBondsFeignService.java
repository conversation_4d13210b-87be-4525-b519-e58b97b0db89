package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.emotional_bonds.CoupleEffectVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "ump-services",
        contextId = "emotional-bonds"
)
public interface EmotioanlBondsFeignService {

    @GetMapping("/api/inner/activity/emotional-bonds/couple-effects")
    public Result<List<CoupleEffectVO>> getCoupleEffects(BaseParam param);

    /**
     * 根据特效代码获取情侣特效
     *
     * @param uid        用户ID
     * @param effectCode 特效代码
     * @return 情侣特效
     */
    @GetMapping("/api/inner/activity/emotional-bonds/couple-effect")
    public Result<CoupleEffectVO> getCoupleEffectByCode(@RequestParam("uid") Long uid, @RequestParam("effectCode") String effectCode);

    @GetMapping("/api/inner/activity/emotional-bonds/version-verify")
    public Result<Boolean> emotionalBondsVersionVerify(@RequestParam("uid") Long uid, @RequestParam("appId") Long appId);

    @GetMapping("/api/inner/activity/emotional-bonds/clear-costume-cache")
    public Result<Boolean> clearCostumeCache(@RequestParam("uid") Long uid);
}
