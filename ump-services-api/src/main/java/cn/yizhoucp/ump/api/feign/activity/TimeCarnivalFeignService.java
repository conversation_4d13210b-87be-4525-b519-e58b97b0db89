package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.DrawReturn;
import cn.yizhoucp.ump.api.vo.activity.timeCarnival.TimeCarnivalIndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ump-services", contextId = "time-carnival")
public interface TimeCarnivalFeignService {

    @GetMapping("/api/inner/activity/time_carnival/draw")
    Result<DrawReturn> draw(@RequestParam("type") String type, @RequestParam("poolCode") String poolCode, @RequestParam("times") Integer times, @RequestParam("extValue") String extValue);


    @GetMapping("/api/inner/activity/time_carnival/select-faction")
    Result<Boolean> selectFaction(@RequestParam(value = "factionId", required = false) String factionId);


    @GetMapping("/api/inner/activity/time_carnival/get-friend-list")
    Result<List<TimeCarnivalIndexVO.UserVO>> getFriends();


    /**
     * 绑定密友
     *
     * @param toUid
     * @return
     */
    @GetMapping("/api/inner/activity/time_carnival/bind-friend")
    Result<Boolean> bindFriend(@RequestParam("toUid") Long toUid);

    /**
     * 兑换奖品
     *
     * @param prizeKey
     * @param prizeNum
     * @return
     */
    @GetMapping("/api/inner/activity/time_carnival/exchange-prize")
    Result<Boolean> exchangePrize(@RequestParam("prizeKey") String prizeKey, @RequestParam("prizeNum") Long prizeNum, @RequestParam(value = "toUid", required = false) Long toUid);

    @GetMapping("/api/inner/activity/time_carnival/claim-reward")
    Result<Boolean> claimReward(@RequestParam("sceneCode") String sceneCode, @RequestParam("rewardId") String rewardId, @RequestParam("toUid") Long toUid);

    /**
     * 创建乐队
     *
     * @param bandName
     * @param members
     * @return
     */
    @GetMapping("/api/inner/activity/time_carnival/create-band")
    Result<Boolean> createBand(@RequestParam("bandName") String bandName, @RequestParam("members") String members);

    /**
     * 解散乐队
     */
    @GetMapping("/api/inner/activity/time_carnival/dismiss-band")
    Result<Boolean> dismissBand(@RequestParam("bandId") String bandId);

    @GetMapping("/api/inner/activity/time_carnival/song-lit")
    Result<Boolean> songLit(@RequestParam("songId") String songId);

    @GetMapping("/api/inner/activity/time_carnival/get-rank")
    Result<Object> getRank(@RequestParam("rankKey") String rankKey,@RequestParam(value = "toUid",required = false) Long toUid);

    @GetMapping("/api/inner/activity/time_carnival/sendReminder")
    Result<Boolean> sendReminder();

    @GetMapping("/api/inner/activity/time_carnival/invite-friend")
    Result<Boolean> inviteFriend(@RequestParam("toUid") Long toUid,@RequestParam(value = "bandId",required = false) String bandId);

    @GetMapping("/api/inner/activity/time_carnival/band-list")
    Result<List<TimeCarnivalIndexVO.Band>> bandList(@RequestParam("page") Integer page, @RequestParam("size") Integer size);

    @GetMapping("/api/inner/activity/time_carnival/draw_log")
    Result<Object> drawLog(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("activityCode") String activityCode, @RequestParam("poolCode") String poolCode);

    @GetMapping("/api/inner/activity/time_carnival/applications/bands")
    Result<Boolean> applicationsBands(@RequestParam("bandId") String bandId);

    @GetMapping("/api/inner/activity/time_carnival/approve-application")
    Result<Boolean> approveApplication(@RequestParam(value = "bandId",required = false) String bandId, @RequestParam(value = "uid",required = false) Long uid, @RequestParam("isInvite") Boolean isInvite, @RequestParam("approve") Boolean approve);


}
