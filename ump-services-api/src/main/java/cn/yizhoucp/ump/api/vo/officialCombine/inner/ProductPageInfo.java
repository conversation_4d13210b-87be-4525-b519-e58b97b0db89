package cn.yizhoucp.ump.api.vo.officialCombine.inner;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 商品列表页信息
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductPageInfo implements Serializable {

    /** 倒计时 */
    private Long countDownDays;
    /** 商品 ID 列表 */
    private List<Long> productIdList;

}
