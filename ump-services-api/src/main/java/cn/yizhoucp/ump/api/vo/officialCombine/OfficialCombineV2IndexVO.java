package cn.yizhoucp.ump.api.vo.officialCombine;

import cn.yizhoucp.ms.core.vo.imservices.CountDownVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 官宣改版首页信息
 * @date 2024-09-03 15:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfficialCombineV2IndexVO {


    private Long otherUid;
    private Long leftUserId;
    private Long rightUserId;
    private String leftUserAvater;
    private String leftUserName;
    private String rightUserAvater;
    private String rightUserName;
    private Boolean toRulePage = false;
    /** 当前用户是否需要进行引导展示 */
    private Boolean guideFlag;
    /** 当前用户是否需解锁小屋 */
    private Boolean loveCabin;
    /** 礼包信息 */
    private List<OfficialCombineV2OneBag> giftBags;
    /** 是否展示证书 */
    private Boolean showCertificate;
    /** 证书信息 */
    private OfficialCombineV2Certificate certificateData;
    /** 是否能取消 */
    private Boolean cancelEnabled;
    /** "init/doing/done",//冷静期状态 doing的时候可以倒计时 不是冷静期可以进行取消 前提是展示证书 */
    private String calmStatus;
    /** 倒计时 */
    private CountDownVO calmCountDown;
    /** 分组标识 */
    private String abTestGroup;
}
