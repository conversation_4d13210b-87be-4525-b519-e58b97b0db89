package cn.yizhoucp.ump.api.vo.activity.galaxyAdventure;

import cn.yizhoucp.ump.api.vo.officialCombine.inner.GiftItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StarTreasureIndexVO {

    /**
     * 子活动码
     */
    private String subActivityCode;

    /**
     * 宝箱列列表
     */
    private List<TreasureVO> treasureList;

    /**
     * 背包占星礼物列表
     */
    private List<GiftItem> packageGiftList;

    /**
     * 积分
     */
    private Long point;

}
