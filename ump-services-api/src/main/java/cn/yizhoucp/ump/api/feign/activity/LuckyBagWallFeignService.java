package cn.yizhoucp.ump.api.feign.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.luckyBagWall.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.luckyBagWall.TradeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(value = "ump-services", contextId = "luckyBagWall")
public interface LuckyBagWallFeignService {


    @RequestMapping("/api/inner/activity/lucky-bag-wall/index")
    Result<IndexVO> index(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid);

    @RequestMapping("/api/inner/activity/lucky-bag-wall/trade")
    Result<TradeVO> trade(@RequestParam("appId") Long appId, @RequestParam("unionId") String unionId, @RequestParam("uid") Long uid, @RequestParam("code") String code);
}
