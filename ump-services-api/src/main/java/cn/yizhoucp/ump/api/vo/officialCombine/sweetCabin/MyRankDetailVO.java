package cn.yizhoucp.ump.api.vo.officialCombine.sweetCabin;

import cn.yizhoucp.ms.core.vo.userservices.UserBasicVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 排行榜详情
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyRankDetailVO {

    /** 男生 */
    private UserBasicVO maleUser;
    /** 女生 */
    private UserBasicVO femaleUser;
    /** 排名	*/
    private Integer ranking;
    /** 增长恋屋值 */
    private Long incrLhCoin;
    /** 与上一名差距	 */
    private String diff;

}
