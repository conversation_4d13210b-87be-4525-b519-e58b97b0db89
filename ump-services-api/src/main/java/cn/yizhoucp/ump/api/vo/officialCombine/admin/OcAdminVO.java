package cn.yizhoucp.ump.api.vo.officialCombine.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 官宣管理后台
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OcAdminVO implements Serializable {

    private Long appId;
    private String unionId;
    private String startTime;
    private String endTime;
    private Long maleId;
    private String maleName;
    private Long femaleId;
    private String femaleName;
    private String maleFamily;
    private String femaleFamily;
    private String orderDetailDesc;
    private String status;
    private String orderPayPrice;
    private String ocStartTime;
    private String ocEndTime;

}
