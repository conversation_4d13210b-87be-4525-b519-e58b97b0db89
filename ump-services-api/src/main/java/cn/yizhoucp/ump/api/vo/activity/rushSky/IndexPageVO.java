package cn.yizhoucp.ump.api.vo.activity.rushSky;

import cn.yizhoucp.ump.api.vo.activity.rushSky.inner.FamilyPkInfo;
import cn.yizhoucp.ump.api.vo.activity.rushSky.inner.TaskItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 首页信息
 *
 * @author: lianghu
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndexPageVO implements Serializable {

    /** 任务列表 */
    private List<TaskItem> taskList;
    /** 弹幕列表 */
    private List<String> barrageList;
    /** 家族 PK 信息 */
    private FamilyPkInfo familyPkInfo;
    /** 个人勋章数 */
    private Long personalMedalNum;
    /** 个人荣誉券数 */
    private Long personalCouponNum;

}
