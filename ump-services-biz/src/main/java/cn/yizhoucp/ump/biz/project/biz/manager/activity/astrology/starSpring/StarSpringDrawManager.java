package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSpring;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.ASTROLOGY_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSpringConstant.RED_ENVELOPE_OPEN;

@Service
@Slf4j
public class StarSpringDrawManager extends AbstractDrawTemplate {

    @Resource
    private RedisManager redisManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private StarSpringTrackManager starSpringTrackManager;

    @Override
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        drawParam.setTimes(1);

        int astrologyTimes = Optional.ofNullable(redisManager.getInteger(String.format(ASTROLOGY_TIMES, drawParam.getUid(), DateUtil.getNowYyyyMMdd()))).orElse(0);
        for (StarSpringConstant.RedEnvelope redEnvelope : StarSpringConstant.RedEnvelope.values()) {
            if (redEnvelope.name().equals(drawParam.getPoolCode())) {
                if (astrologyTimes < redEnvelope.getAstrologyTimes()) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您的占星次数不足，请先占星吧～");
                }

                // TODO 事务
                if (!Boolean.TRUE.equals(redisManager.setnx(String.format(RED_ENVELOPE_OPEN, drawParam.getUid(), DateUtil.getNowYyyyMMdd(), redEnvelope.name()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您今日已开启此红包，看看其他红包吧～");
                }

                return;
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void deductResource(DrawContext context) {
    }

    @Override
    protected void doCallback(DrawContext context) {
        starSpringTrackManager.allActivityReceiveAward("red_packet", context.getDrawParam().getPoolCode(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemKey(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemValueGold(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getTargetTimes(), context.getDrawParam().getUid());
    }

}
