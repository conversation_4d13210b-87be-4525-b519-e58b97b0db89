package cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class StarSeaTourConstant {

    public static final String ACTIVITY_CODE = "star_sea_tour";

    /** uid yyyyMMdd */
    public static final String SUPPLY = "ump:star_sea_tour2:supply:%s:%s";
    public static final String SUPPLY_LEFT = "ump:star_sea_tour2:supply_left:%s:%s";

    /** uid yyyyMMdd star.name() */
    public static final String STAR_EXPLORE = "ump:star_sea_tour2:star_explore:%s:%s:%s";
    /** uid star.name() */
    public static final String STAR_EXPLORE_TIMES = "ump:star_sea_tour2:star_explore:%s:%s";

    /** star.name() */
    public static final String TASK_STAR_SEA_RECEIVE_LIMIT = "ump:star_sea_tour2:task_star_sea_receive_limit:%s";
    /** uid star.name() */
    public static final String TASK_STAR_SEA_RECEIVE = "ump:star_sea_tour2:task_star_sea_receive:%s:%s";

    /** uid yyyyMMdd */
    public static final String REPLACE_GIFT = "ump:star_sea_tour2:replace_gift:%s:%s";

    /** uid yyyyMMdd */
    public static final String CRYSTAL_STAR = "ump:star_sea_tour2:crystal_star:%s:%s";
    public static final String CRYSTAL_NUM = "ump:star_sea_tour2:crystal_num:%s:%s";
    public static final String STAR_POOL_CODE = "%s_STAR";

    public static final int[] ORDINARY_GUIDE_SUCCESS_PROB = new int[] {0, 2000, 1500, 1000, 900, 800, 750, 500, 300, 200, 0};
    public static final int[] GODS_GUIDE_SUCCESS_PROB = new int[] {0, 3300, 3300, 3300, 3300, 3300, 3300, 3300, 3300, 3300, 0};
    public static final int[] ORDINARY_GUIDE_CONSUME = new int[] {0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0};
    public static final int[] GODS_GUIDE_CONSUME = new int[] {0, 100, 120, 140, 160, 180, 220, 260, 300, 340, 0};
    public static final String GODS_GUIDE_TIMES = "ump:star_sea_tour2:gods_guide_times:%s:%s";

    /** yyyyMMdd */
    public static final String TOUR_BOARD = "ump:star_sea_tour2:tour_board:%s";
    public static final String TOUR_BOARD_OVERALL = "ump:star_sea_tour2:tour_board_overall";
    public static final String GUIDE_BOARD = "ump:star_sea_tour2:guide_board";

    /** uid yyyyMMdd taskCode */
    public static final String STAR_TASK_STATUS = "ump:star_sea_tour2:star_task_status:%s:%s:%s";
    public static final String TASK_STAR_RECEIVE = "ump:star_sea_tour2:task_star_receive:%s:%s:%s";
    /** uid yyyyMMdd */
    public static final String STAR_TASK_COMPLETE = "ump:star_sea_tour2:star_task_complete:%s:%s";

    @AllArgsConstructor
    @Getter
    public enum Star {
        VENUS(50, "粗糙", "BQSF_GIFT", "star_sea_tour_VENUS", 200, 10),
        JUPITER(300, "普通", null, "", 0, 0),
        MERCURY(1288, "精致", null, "", 0, 0),
        MARS(2588, "珍贵", "QNZL_GIFT", "star_sea_tour_MARS", 50, 10),
        SATURN(5999, "华丽", "TJQY_GIFT", "star_sea_tour_SATURN", 5, 10),
        ;

        private Integer supply;
        private String desc;
        private String giftKey;
        private String taskCode;
        private Integer taskReceiveLimit;
        private int maxFinishTimes;
    }

    @AllArgsConstructor
    @Getter
    public enum StarTask {
        STAR_TASK_1314("star_task_1314"),
        STAR_TASK_3344("star_task_3344"),
        STAR_TASK_9999("star_task_9999"),
        ;

        private String taskCode;
    }

}
