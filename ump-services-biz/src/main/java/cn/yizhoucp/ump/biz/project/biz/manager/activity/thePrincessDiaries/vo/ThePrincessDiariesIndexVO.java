package cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThePrincessDiariesIndexVO {

    private TaskInfo tasksInfo;
    private PrizePoolInfo prizePoolInfo;
    private ShopInfo shopInfo;
    private PairTaskInfo pairTaskInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskInfo {
        private String userSex;
        private List<Task> tasks;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Task {
        private String taskId;
        private String title;
        private String content;
        private Integer rewardAmount;
        private GiftInfo giftInfo;
        private Integer taskStatus;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GiftInfo {
        private String giftCode;
        private String giftIcon;
        private String giftName;
        private Long giftValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizePoolInfo {
        private List<GiftInfo> gifts;
        private Long drawItemCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShopInfo {
        private List<Item> items;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        private String itemCode;
        private String itemIcon;
        private String itemName;
        private String description;
        private Long originalPrice;
        private String discountPrice;
        private Long remainingCount;
        private String discountRate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PairTaskInfo {
        private Integer rewardStatus;
        private Long pairValue;
        private UserInfo currentUser;
        private UserInfo pairedUser;
        private List<LightTask> lightTasks;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        private Long userId;
        private String username;
        private String avatar;
        private String sex;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LightTask {
        private String taskId;
        private GiftInfo giftInfo;
        private Integer lightStatus;
        private Long requiredValue;
    }
}