package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.mission.MissionTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.MissionProcessManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.springFestival2024.SpringFestival2024BizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_IM_CHAT_CREATE_DIALOG;
import static cn.yizhoucp.ms.core.base.TopicTagEnum.TOPIC_IM_CHAT_SEND_CONFESS_COPY;

/**
 * 数据业务相关 mq
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "im_chat_topic", consumerGroup = "IM_CHAT_UMP_SERVICE_GROUP")
public class ImChatConsumer extends RocketmqAbstractConsumer {

    @Resource
    private SpringFestival2024BizManager springFestival2024BizManager;
    @Resource
    private MissionProcessManager missionProcessManager;

    /** 关注 tag 列表 */
    private static final Set<String> LISTEN_TAG_SET = Sets.newHashSet(TOPIC_IM_CHAT_CREATE_DIALOG.getTagKey(), TOPIC_IM_CHAT_SEND_CONFESS_COPY.getTagKey());

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_IM_CHAT.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return LISTEN_TAG_SET;
    }

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String param) {
        if (TOPIC_IM_CHAT_CREATE_DIALOG.getTagKey().equals(tag)) {
            return this.createDialog(BaseParam.builder().appId(MDCUtil.getCurAppIdByMdc()).unionId(unionId).build(), param);
        } else if (TOPIC_IM_CHAT_SEND_CONFESS_COPY.getTagKey().equals(tag)) {
            return this.sendConfessCopy(BaseParam.builder().appId(MDCUtil.getCurAppIdByMdc()).unionId(unionId).uid(userId).build(), param);
        }
        return Boolean.FALSE;
    }

    /**
     * 创建一个新对话
     *
     * @param param 消息内容
     * @return boolean
     */
    private boolean createDialog(BaseParam baseParam, String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        missionProcessManager.process(MissionParam.builder()
                .appId(baseParam.getAppId())
                .unionId(baseParam.getUnionId())
                .uid(jsonObject.getLong("fromUserId"))
                .type(MissionTypeEnum.NEW_DIALOG)
                .bizParam(jsonObject).build());
//        JSONObject jsonObject = JSONObject.parseObject(param);
//        Long uid = jsonObject.getLong("fromUserId");
//        Long otherUid = jsonObject.getLong("toUserId");
//        baseParam.setUid(uid);
//        newYear2022MissionManager.chatHandle(baseParam, jsonObject);
//        lovePostcardManager.createDialogTask(uid, otherUid);
        return Boolean.FALSE;
    }

    private Boolean sendConfessCopy(BaseParam baseParam, String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        return springFestival2024BizManager.sendConfessCopy(baseParam, jsonObject.getLong("uid"), jsonObject.getString("content"));
    }

}
