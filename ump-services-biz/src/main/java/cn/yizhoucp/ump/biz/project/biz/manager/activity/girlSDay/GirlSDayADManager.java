package cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay;

import cn.yizhoucp.ms.core.base.enums.DeployEnvEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.FunctionUtil;
import cn.yizhoucp.ump.api.vo.girlSDay.PkInfoVO;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.LanlingActivityUtil;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BuoyInfoHandler;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.function.Function;

@Slf4j
@Service
public class GirlSDayADManager implements BuoyInfoHandler {

    @Resource
    private GirlSDayPkManager girlSDayPkManager;

    @Resource
    private GirlSDayHelper girlSDayHelper;

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private Environment environment;

    @Override
    public String getActivityCode() {
        return GirlSDayHelper.ACTIVITY_CODE;
    }

    @Override
    public Function<HandlerContext, BuoyInfoDTO> getBuoyInfoHandler() {
        return handlerContext -> {
            PkInfoVO pkInfo = girlSDayPkManager.getPkInfo(handlerContext.getUid(), Boolean.FALSE);
            String routerUrl = ActivityUrlUtil.getH5BaseUrl(handlerContext.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "girl-day" + "?from=chat_buoy&toUid=" + FunctionUtil.orDefault(handlerContext.getToUid(), "");
            if (DeployEnvEnum.test()) {
                routerUrl = "https://test-kaiyuan022915-nuanliao-vite.f-test.myrightone.com/girl-day" + "?from=chat_buoy&toUid=" + FunctionUtil.orDefault(handlerContext.getToUid(), "");
            }
            return BuoyInfoDTO.builder()
                    .countDownTime(pkInfo == null ? null : getEndTimestamp())
                    .routerUrl(routerUrl)
                    .build();
        };
    }

    private Long getEndTimestamp() {
        LocalDateTime now = girlSDayHelper.getNow();
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        return endOfDay.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    @Override
    public Function<HandlerContext, JSONObject> getPayloadInfo() {
        return handlerContext -> {
            JSONObject jsonObject = new JSONObject();
            PkInfoVO pkInfo = girlSDayPkManager.getPkInfo(handlerContext.getUid(), Boolean.FALSE);
            if (pkInfo == null) {
                return jsonObject;
            }
            jsonObject.put("mySelf", Boolean.FALSE);
            jsonObject.put("myHigher", pkInfo.getMyValue() > pkInfo.getToValue());
            jsonObject.put("myValue", pkInfo.getMyValue());
            jsonObject.put("otherValue", pkInfo.getToValue());
            jsonObject.put("picUrl", "https://res-cdn.nuan.chat/res/common/image/65e81e5da2ccf6e8.png");
            return jsonObject;
        };
    }
}
