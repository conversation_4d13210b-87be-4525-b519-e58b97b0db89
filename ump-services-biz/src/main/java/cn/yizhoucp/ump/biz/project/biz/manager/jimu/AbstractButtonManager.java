package cn.yizhoucp.ump.biz.project.biz.manager.jimu;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.api.param.jimu.ButtonFriendPaream;
import cn.yizhoucp.ump.api.param.jimu.ButtonRankListParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.sweetLoveCupid.SweetLoveCupidRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class AbstractButtonManager  implements ButtonManager{

    @Resource
    private SweetLoveCupidRankManager sweetLoveCupidRankManager;

    @Override
    public Object friendList(ButtonFriendPaream buttonFriendPaream) {
        // TODO 未实现
        return null;
    }


    /**
     * 通用获取排行榜【 List 填充到 rankLen 】
     * 用户线程信息为 null (比如定时任务，使用 AbstractRankManager 实现子类 自己 getRank())
     *
     * @return RankVO
     */
    @Override
    public RankVO rankList(ButtonRankListParam buttonFriendPaream) {
        // 这里做参数校验
        if (!buttonFriendPaream.getBaseParam().check()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long rankLen = buttonFriendPaream.getRankLen();
        RankContext.RankType type = RankContext.RankType.convert(buttonFriendPaream.getListType());
        RankVO rank = sweetLoveCupidRankManager.getRank(RankContext.builder()
                .param(buttonFriendPaream.getBaseParam())
                .activityCode(buttonFriendPaream.getActivityCode())
                .rankKey(buttonFriendPaream.getRankKey())
                .type(type)
                .otherUid(buttonFriendPaream.getOtherUid())
                .supportDiff(buttonFriendPaream.getSupportDiff())
                .rankLen(rankLen)
                .build());
        log.info("AbstractButtonManager getRank result:{}", JSONObject.toJSONString(rank));
        //数据为空时填充信息
        initializeRankList(rank, type, rankLen);
        //隐藏头像

        return rank;
    }

    private void initializeRankList(RankVO rank, RankContext.RankType type, Long rankLen) {
        if (type == RankContext.RankType.cp) {
            List<CpRankItem> cpRankItemList = rank.getCpRankItemList();
            if (cpRankItemList == null) {
                cpRankItemList = new ArrayList<>();
            }
            int currentSize = cpRankItemList.size();
            for (int i = currentSize; i < rankLen; i++) {
                cpRankItemList.add(CpRankItem.builder()
                        .rank((long) i + 1)
                        .value(0L)
                        .build());
            }
            rank.setCpRankItemList(cpRankItemList);
        } else {
            List<RankItem> rankList = rank.getRankList();
            if (rankList == null) {
                rankList = new ArrayList<>();
            }
            int currentSize = rankList.size();
            Long lastRank = currentSize > 0 ? rankList.get(currentSize - 1).getRank() : 0L;
            for (int i = currentSize; i < rankLen; i++) {
                RankItem newItem = new RankItem();
                newItem.setRank(lastRank + 1);
                newItem.setValue(0L);
                rankList.add(newItem);
                lastRank = newItem.getRank();
            }
            rank.setRankList(rankList);
        }
    }
}
