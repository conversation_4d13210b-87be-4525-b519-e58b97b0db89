package cn.yizhoucp.ump.biz.project.dal.mp.dao;

import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.EvaluateRewardRecordDO;
import cn.yizhoucp.ump.biz.project.dal.mp.mapper.EvaluateRewardRecordMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Date;

@Repository
public class EvaluateRewardRecordService extends ServiceImpl<EvaluateRewardRecordMapper, EvaluateRewardRecordDO> {

    public void updateStatusAndScoreById(Long id, String updateStatus, Integer score) {
        this.lambdaUpdate()
                .eq(EvaluateRewardRecordDO::getId, id)
                .set(EvaluateRewardRecordDO::getStatus, updateStatus)
                .set(EvaluateRewardRecordDO::getPreMarkScore, score).update();
    }

    public void updateStatusAndImgById(Long id, String updateStatus, String img) {
        this.lambdaUpdate()
                .eq(EvaluateRewardRecordDO::getId, id)
                .set(EvaluateRewardRecordDO::getStatus, updateStatus)
                .set(EvaluateRewardRecordDO::getUserUploadImg, img)
                .set(EvaluateRewardRecordDO::getApplyTime, new Date()).update();
    }

    public void updateStatusById(Long id, String updateStatus) {
        this.lambdaUpdate()
                .eq(EvaluateRewardRecordDO::getId, id)
                .set(EvaluateRewardRecordDO::getStatus, updateStatus).update();
    }

    public void page(String status, String vestChannel, String startTime, String endTime, Page<EvaluateRewardRecordDO> page, String deviceBrand) {
        this.lambdaQuery()
                .eq(!StringUtils.isEmpty(status), EvaluateRewardRecordDO::getStatus, status)
                .eq(!StringUtils.isEmpty(vestChannel), EvaluateRewardRecordDO::getVestChannel, vestChannel)
                .eq(!StringUtils.isEmpty(deviceBrand), EvaluateRewardRecordDO::getDeviceBrand, deviceBrand)
                .ge(!StringUtils.isEmpty(startTime), EvaluateRewardRecordDO::getApplyTime, startTime)
                .le(!StringUtils.isEmpty(endTime), EvaluateRewardRecordDO::getApplyTime, endTime).page(page);
    }

    public EvaluateRewardRecordDO getByDeviceId(String deviceId) {
        if (deviceId == null) {
            return null;
        }
        return this.lambdaQuery()
                .eq(EvaluateRewardRecordDO::getDeviceId, deviceId)
                .last("limit 1").one();
    }

    public EvaluateRewardRecordDO getByUserId(Long uid) {
        if (uid == null) {
            return null;
        }
        return this.lambdaQuery()
                .eq(EvaluateRewardRecordDO::getUserId, uid)
                .last("limit 1").one();
    }
}
