package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.annualCarnival.AnnualCarnivalBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.UserActivityManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.*;

/**
 * 充值事件消费
 *
 * @author: lianghu
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "recharge_topic", consumerGroup = "RECHARGE_UMP_SERVICE_GROUP")
public class RechargeConsumer extends RocketmqAbstractConsumer {

    @Resource
    private UserActivityManager userActivityManager;
    @Resource
    private AnnualCarnivalBizManager annualCarnivalBizManager;

    /**
     * 关注 tag 列表
     */
    private static final Set<String> LISTEN_TAG_SET = Sets.newHashSet(TOPIC_WECHAT_OFFICIAL_ACCOUNT_RECHARGE.getTagKey(),
            TOPIC_PAY_SUCCESS_EVENT.getTagKey(),
            TOPIC_RECHARGE_SUCCESS_EVENT.getTagKey());

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String param) {
        if (TOPIC_WECHAT_OFFICIAL_ACCOUNT_RECHARGE.getTagKey().equals(tag)) {
            return wechatOfficialRechargeHandle(MDCUtil.getCurAppIdByMdc(), unionId, userId, JSON.parseObject(param));
        } else if (TOPIC_PAY_SUCCESS_EVENT.getTagKey().equals(tag) || TOPIC_RECHARGE_SUCCESS_EVENT.getTagKey().equals(tag)) {
            return handleRechargeCallbackEvent(MDCUtil.getCurAppIdByMdc(), unionId, userId, JSON.parseObject(param));
        }
        return Boolean.FALSE;
    }

    private Boolean handleRechargeCallbackEvent(Long appId, String unionId, Long userId, JSONObject param) {
        //庆典嘉年华充值
        annualCarnivalBizManager.handleRechargeEvent(appId, unionId, param.getLong("uid"), param);
        return Boolean.TRUE;
    }

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_RECHARGE_EVENT.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return LISTEN_TAG_SET;
    }

    private Boolean wechatOfficialRechargeHandle(Long appId, String unionId, Long userId, JSONObject param) {
        // 上下文处理
        SecurityUser securityUser = new SecurityUser();
        securityUser.setAppId(appId);
        securityUser.setUnionId(unionId);
        securityUser.setUserId(param.getLong("uid"));
        MDC.put("securityUser", JSONObject.toJSONString(securityUser));

        // iOS 充值引导退出活动处理
        userActivityManager.exitUserActivity(appId, unionId, param.getLong("uid"), ActivityCheckListEnum.iOS_RECHARGE_GUIDE.getCode());
        return Boolean.TRUE;
    }
}
