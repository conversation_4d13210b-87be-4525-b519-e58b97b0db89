package cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FoolsDay2025IndexVO {

    private List<Node> nodes;
    private Integer currentNodeId;
    private List<TaskInfo> taskInfo;
    private Integer value;
    private String avatar;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Node {
        private Integer id;
        private String nodeId;
        private Boolean hasReward;
        private Reward reward;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Reward {
        private String giftCode;
        private String giftIcon;
        private Integer giftNum;
        private String giftName;
        private Integer giftValue;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TaskInfo {
        private String taskId;
        private GiftInfo giftInfo;
        private Integer status;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class GiftInfo {
        private String giftCode;
        private String giftIcon;
        private String giftValue;
        private String giftName;
    }
}