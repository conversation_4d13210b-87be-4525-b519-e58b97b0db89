package cn.yizhoucp.ump.biz.project.biz.manager.family;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.SakaFamilyDrawManager;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> pepper
 * @Classname FamilyLuckManager
 * @Description
 * @Date 2022/7/20 20:20
 */
@Slf4j
@Service
public class FamilyLuckManager {


    @Resource
    private SakaFamilyDrawManager sakaFamilyDrawManager;

    public Integer getFamilyLuckValue() {
        Long uid = MDCUtil.getCurUserIdByMdc();
        return sakaFamilyDrawManager.getLuckValue(uid.toString());
    }
}
