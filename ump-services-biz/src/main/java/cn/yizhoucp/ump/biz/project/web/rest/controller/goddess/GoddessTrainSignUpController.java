package cn.yizhoucp.ump.biz.project.web.rest.controller.goddess;

import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.AppointmentIndexVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.SignUpCheckResultVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.SignUpResultVO;
import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainAppointmentManager;
import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainSignUpManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ump.api.vo.goddessTrain.SignUpRequestVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 女神培训-报名
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/inner/activity/goddess-train/sign-up")
public class GoddessTrainSignUpController {

    @Resource
    GoddessTrainSignUpManager goddessTrainSignUpManager;

    @Resource
    GoddessTrainAppointmentManager goddessTrainAppointmentManager;

    /**
     * 获取当前可用的培训群二维码
     *
     * @return String
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/get-wechat-qr-code")
    public Result<String> getWechatQrCode(String unionId, Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.getWechatQrCode(unionId, appId));
    }

    /**
     * 获取学习内容
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @return CommonListVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/learn-content-list")
    public Result<CommonListVO> getLearnContentList(String unionId, Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.getLearnContentList(unionId, appId));
    }

    /**
     * 用户报名
     *
     * @param signUpRequest 报名请求
     * @return SignUpResultVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @PostMapping("/sign-up")
    public Result<SignUpResultVO> signUp(@RequestBody SignUpRequestVO signUpRequest) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.signUp(signUpRequest));
    }

    /**
     * 校验用户是否报名正常进行的培训
     *
     * @param uid 用户id
     * @return Boolean
     */
    @GetMapping("/check-user-sign-up")
    public Result<Boolean> checkUserSignUp(String unionId, Long appId, Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.checkUserSignUp(unionId, appId, uid));
    }

    /**
     * 预约
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @return Boolean
     */
    @GetMapping("/make-appointment")
    public Result<CommonResultVO> makeAppointment(String unionId, Long appId, Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainAppointmentManager.userMakeAppointment(unionId, appId, uid));
    }

    /**
     * 获取预约页信息
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @return AppointmentIndexVO
     */
    @GetMapping("/appointment-index")
    public Result<AppointmentIndexVO> getAppointmentIndex(String unionId, Long appId, Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainAppointmentManager.getAppointmentIndex(unionId, appId, uid));
    }

    /**
     * 校验用户报名状态
     *
     * @param unionId   应用唯一标识
     * @param appId     应用id
     * @param uid       用户id
     * @param trainCode 活动 code
     * @return SignUpCheckResultVO
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/check-user-sign-up-status")
    public Result<SignUpCheckResultVO> checkUserSignUpStatus(String unionId, Long appId, Long uid, String trainCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainSignUpManager.checkUserSignUpStatus(unionId, appId, uid, trainCode));
    }

    /**
     * 预约弹窗
     *
     * @param unionId 应用唯一标识
     * @param appId   应用id
     * @param uid     用户id
     * @return Boolean
     */
    @GetMapping("/check-make-appointment-pop")
    public Result<Boolean> checkMakeAppointmentPop(String unionId, Long appId, Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> goddessTrainAppointmentManager.checkMakeAppointmentPop(unionId, appId, uid));
    }

}
