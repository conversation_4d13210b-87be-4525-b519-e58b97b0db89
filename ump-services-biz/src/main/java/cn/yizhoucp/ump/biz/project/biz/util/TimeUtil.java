package cn.yizhoucp.ump.biz.project.biz.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: clong
 * @Date: 2020/10/20 2:47 下午
 * 时间管理工具类
 */
public class TimeUtil {

    public static String coverToShowTime(Date updateTime) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        long time = (System.currentTimeMillis() - updateTime.getTime()) / 1000;
        String result = sf.format(updateTime);
        long temp = 0;
        if (time < 60) { // 小于60 秒显示刚刚
            result = "刚刚";
        }else if( (temp = time/60) < 60){ // 小于60分钟直接显示几分钟前
            result = temp + "分钟前";
        }else if((temp = time/(60*60)) < 24){ // 小于24小时直接显示几小时前
            result = temp + "小时前";
        }else if((temp = time/(86400)) < 7){ // 小于7天显示几天前
            result = temp + "天前";
        }else if((temp = time/(86400*7)) < 2){ // 大于7天小于14天显示1周前
            result = temp + "周前";
        }
        return result;
    }

    public static String Date2Str(Date date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(date);
        return dateString;
    }

    public static Date Str2Date(String text){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = new String(text);
        try {
            Date d1 = df.parse(date);
            return d1;
        } catch (Exception e) {

        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(TimeUtil.coverToShowTime(Str2Date("2020-09-10 10:30:00")));
    }

}
