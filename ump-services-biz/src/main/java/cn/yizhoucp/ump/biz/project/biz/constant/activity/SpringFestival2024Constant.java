package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.rank.RewardVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class SpringFestival2024Constant {

    public static final String ACTIVITY_CODE = "spring_festival_2024";
    public static final List<String> HEART_BEAT_DRAW_POOL_CODE_MAN = Arrays.asList("HEART_BEAT_DRAW_POOL_MAN_1","HEART_BEAT_DRAW_POOL_MAN_2","HEART_BEAT_DRAW_POOL_MAN_3","HEART_BEAT_DRAW_POOL_MAN_4","HEART_BEAT_DRAW_POOL_MAN_5");
    public static final List<String> HEART_BEAT_DRAW_POOL_CODE_WOMAN = Arrays.asList("HEART_BEAT_DRAW_POOL_WOMAN_1","HEART_BEAT_DRAW_POOL_WOMAN_2","HEART_BEAT_DRAW_POOL_WOMAN_3","HEART_BEAT_DRAW_POOL_WOMAN_4","HEART_BEAT_DRAW_POOL_WOMAN_5");

    /** uid yyyyMMdd */
    public static final String HEART_BEAT_VALUE = "ump:spring_festival_2024:heart_beat_value:%s:%s";
    /** yyyyMMdd */
    public static final String HEART_BEAT_BOARD = "ump:spring_festival_2024:heart_beat_board:%s";
    /** uid toUid */
    public static final String DRAW_TIMES = "ump:spring_festival_2024:draw_times:%s:%s";
    /** uid toUid */
    public static final String HEART_BEAT_DRAW_POOL_CODE = "ump:spring_festival_2024:heart_beat_draw_pool:code:%s:%s";
    /** uid */
    public static final String HEART_BEAT_DRAW_POOL_REFRESH_TIMES = "ump:spring_festival_2024:heart_beat_draw_pool:refresh_times:%s";
    /** splicUserId */
    public static final String LOVE_DRAW_POOL_ITEM = "ump:magpie2023:loveDrawPoolItem_%s";

    public static final String GLOBAL_PRAY_TIMES = "ump:spring_festival_2024:global_pray_times";
    public static final String PRAY_BOARD = "ump:spring_festival_2024:pray_board";
    /** splicUserId */
    public static final String SECRET_MASK = "ump:spring_festival_2024:secret_mask:%s";

    /** uid */
    public static final String LUCK_CARD_NUM = "ump:spring_festival_2024:luck_card_num:%s";
    @Getter
    @AllArgsConstructor
    public enum LUCK_CARD {
        FU("FU_GIFT", "福"),
        LU("LU_GIFT", "禄"),
        CAI("CAI_GIFT", "财"),
        LE("LE_GIFT", "乐"),
        AN("AN_GIFT", "安"),
        KANG("KANG_GIFT", "康"),
        SHOU("SHOU_GIFT", "寿");

        private String giftKey;
        private String name;

        public static String getNameByGiftKey(String giftKey) {
            if (StringUtils.isBlank(giftKey)) {
                return null;
            }

            for (LUCK_CARD luckCard : values()) {
                if (luckCard.giftKey.equals(giftKey)) {
                    return luckCard.getName();
                }
            }

            return null;
        }
    }

    /** splicUserId */
    public static final String SIGN_STATUS = "ump:spring_festival_2024:sign_status:%s";
    public static final List<String> ACTIVITY_DATE = Arrays.asList("20240206", "20240207", "20240208", "20240209", "20240210", "20240211", "20240212",
            "20240213", "20240214", "20240215", "20240216", "20240217", "20240218", "20240219");
//    public static final List<String> ACTIVITY_DATE = Arrays.asList("20240128", "20240129", "20240130", "20240131", "20240201", "20240202", "20240203",
//            "20240204", "20240205", "20240206", "20240207", "20240208", "20240209", "20240210");

    /**
     * 发起每日限制一次（时间、type、userId）
     */
    public static final String INITIATE_EVERY_DAY_LIMIT_1 = "ump:magpie2023:initiateEveryLimit1_%s_%s_%s";

    /**
     * 爱意值（时间、userId）
     */
    public static final String LOVE_VALUE = "ump:magpie2023:loveValue_%s_%s";

    /**
     * 爱意奖池抽奖次数（userId、toUserId）
     */
    public static final String LOVE_POOL_DRAW_TIME = "ump:magpie2023:lovePoolDrawTime_%s_%s";

    /**
     * 爱意奖池（userId、toUserId）
     */
    public static final String LOVE_DRAW_POOL_CODE = "ump:magpie2023:loveDrawPoolCode_%s_%s";

    public static final List<String> LOVE_DRAW_POOL_CODE_MAN = Arrays.asList("LOVE_DRAW_POOL_1_MAN","LOVE_DRAW_POOL_2_MAN","LOVE_DRAW_POOL_3_MAN","LOVE_DRAW_POOL_4_MAN","LOVE_DRAW_POOL_5_MAN");
    public static final List<String> LOVE_DRAW_POOL_CODE_WOMAN = Arrays.asList("LOVE_DRAW_POOL_1_WOMAN","LOVE_DRAW_POOL_2_WOMAN","LOVE_DRAW_POOL_3_WOMAN","LOVE_DRAW_POOL_4_WOMAN","LOVE_DRAW_POOL_5_WOMAN");

    public static final List<String> LOVE_DRAW_POOL_399_GIFT = Arrays.asList("AXFS_GIFT", "LSSQ_GIFT", "THF_GIFT");
    public static final List<String> LOVE_DRAW_POOL_520_GIFT = Arrays.asList("XDPF_GIFT", "XRNYZS_GIFT", "ZNDGZ_GIFT", "BQSF_GIFT", "LLZ_GIFT", "AQG_GIFT", "SXXB_GIFT", "NSHG_GIFT", "LAXJ_GIFT", "QQQ_GIFT");
    public static final List<String> LOVE_DRAW_POOL_888_GIFT = Arrays.asList("XYHT_GIFT", "TMBJ_GIFT", "BYSN_GIFT", "HCXY_GIFT", "520MGH_GIFT");
    public static final List<String> LOVE_DRAW_POOL_1314_GIFT = Arrays.asList("YSCA_GIFT", "XYLY_GIFT", "LMTC_GIFT", "TMYH_GIFT", "QHZZJ_GIFT");
    public static final List<String> LOVE_DRAW_POOL_1999_GIFT = Arrays.asList("MGLC_GIFT", "CLHS_GIFT");
    public static final List<String> LOVE_DRAW_POOL_5200_GIFT = Arrays.asList("Romantic_Galaxy_GIFT", "DNHJ_GIFT", "YYJR_GIFT", "PC_GIFT", "CBHL_GIFT");
    public static final List<String> LOVE_DRAW_POOL_HEAD_FRAME = Arrays.asList("qixiguizutouxiangkuang_head_frame", "enaiqinglv_head_frame", "yonghengzhilian_head_frame", "tianmiqinglv_head_frame", "tianmixiaotu_head_frame", "aiqingshu_head_frame");
    public static final List<String> LOVE_DRAW_POOL_MOUNT = Arrays.asList("huachuan_mount", "nanguamache_mount", "gaobaichaopao_mount", "yunxiaoxianlu_mount", "zhuanshudingshehuachuan_mount");
    public static final List<String> LOVE_DRAW_POOL_ENTER_SPECIAL_EFFECT = Arrays.asList("tianzuozhihe_entry_special_effect", "langmanxinghe_entry_special_effect", "wozhixihuanni_entry_special_effect");

    /**
     * 爱意榜（时间）
     */
    public static final String LOVE_BOARD = "ump:magpie2023:loveBoard_%s";

    /**
     * 喜鹊数量（userId）
     */
    public static final String MAGPIE_COUNT = "ump:magpie2023:magpieCount_%s";

    /**
     * 喜上眉梢奖池抽奖次数（userId）
     */
    public static final String XSMS_POOL_DRAW_TIME = "ump:magpie2023:xsmsPoolDrawTime_%s";

    /**
     * 发送表白文案（时间、userId）
     */
    public static final String SEND_CONFESS_COPY = "ump:magpie2023:sendConfessCopy_%s_%s";

    /**
     * 发送表白文案间隔（时间、userId）
     */
    public static final String SEND_CONFESS_COPY_INTERVAL = "ump:magpie2023:sendConfessCopyInterval_%s_%s";

    /**
     * 甜蜜连线次数（时间、userId）
     */
    public static final String SWEET_CONNECT_TIME = "ump:magpie2023:sweetConnectTime_%s_%s";

    /**
     * 甜蜜连线间隔（时间、userId）
     */
    public static final String SWEET_CONNECT_INTERVAL = "ump:magpie2023:sweetConnectInterval_%s_%s";

    /**
     * 为爱祝福设备 ID
     */
    public static final String BLESSING_FOR_LOVE_DEVICE_ID = "ump:magpie2023:blessingForLoveDeviceId";

    /**
     * 为爱祝福用户 ID
     */
    public static final String BLESSING_FOR_LOVE_USER_ID = "ump:magpie2023:blessingForLoveUserId";

    /**
     * 全服发起表白次数
     */
    public static final String CONFESS_TIME_TOTAL = "ump:magpie2023:confessTimeTotal";

    /**
     * 眷侣榜
     */
    public static final String COUPLE_BOARD = "ump:magpie2023:coupleBoard";

    /**
     * 爱意榜奖励列表
     */
    public static final Map<Long, List<RewardVO>> LOVE_RANK_REWARD = Maps.newHashMap();

    static {
        LOVE_RANK_REWARD.put(1L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("鹊桥之恋")
                        .giftKey("QQZL_GIFT")
                        .type("gift")
                        .value(2888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563682274079.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(2L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("纯恋婚纱")
                        .giftKey("CLHS_GIFT")
                        .type("gift")
                        .value(1999L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2021-06/1624619364763738.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(3L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("爱情海")
                        .giftKey("AQH_GIFT")
                        .type("gift")
                        .value(1500L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563192428730.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(4L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("星空瓶")
                        .giftKey("XKP_GIFT")
                        .type("gift")
                        .value(1200L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691564046087055.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(5L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("白羊少女")
                        .giftKey("BYSN_GIFT")
                        .type("gift")
                        .value(888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-02/1645710123307790.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(6L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("连理枝")
                        .giftKey("LLZ_GIFT")
                        .type("gift")
                        .value(520L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563412734019.png")
                        .times("2")
                        .build()));
        LOVE_RANK_REWARD.put(7L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("爱心发射")
                        .giftKey("AXFS_GIFT")
                        .type("gift")
                        .value(399L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-08/1691563292601018.png")
                        .times("2")
                        .build()));
    }

    /**
     * 眷侣榜奖励列表
     */
    public static final Map<Long, List<RewardVO>> COUPLE_RANK_REWARD = Maps.newHashMap();
    static {
        COUPLE_RANK_REWARD.put(1L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("神都夜宴")
                        .giftKey("SDYY_GIFT")
                        .type("gift")
                        .value(10000L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1678422151487847.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("mount")
                        .value(5200L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(2L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("心动小酒馆")
                        .giftKey("XDXJG_GIFT")
                        .type("gift")
                        .value(8888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-05/1684649708473155.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(3344L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(3L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("悠悠假日")
                        .giftKey("YYJR_GIFT")
                        .type("gift")
                        .value(5200L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-03/1679890525794682.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(2000L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(4L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("云端乐园")
                        .giftKey("YDLY_GIFT")
                        .type("gift")
                        .value(3344L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-04/1681097257992745.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(1200L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(5L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("一世长安")
                        .giftKey("YSCA_GIFT")
                        .type("gift")
                        .value(1314L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1668581226604341.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(999L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(6L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("花车巡游")
                        .giftKey("HCXY_GIFT")
                        .type("gift")
                        .value(888L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1667904805831492.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(777L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
        COUPLE_RANK_REWARD.put(7L, Lists.newArrayList(
                RewardVO.builder()
                        .rewardName("爱情果")
                        .giftKey("AQG_GIFT")
                        .type("gift")
                        .value(520L)
                        .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2022-11/1669367511989231.png")
                        .build(),
                RewardVO.builder()
                        .rewardName("金币")
                        .giftKey("coin")
                        .type("coin")
                        .value(520L)
                        .icon("https://res-cdn.nuan.chat/res/activity/magpie2023-coin-img.png")
                        .build()));
    }

}
