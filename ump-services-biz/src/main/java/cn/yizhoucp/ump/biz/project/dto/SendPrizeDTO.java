package cn.yizhoucp.ump.biz.project.dto;

import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.sendPrzie.SendPrizeAdminVO;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.ExpiredTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PrizeItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendPrizeDTO {

    /** 应用 code */
    private Long appId;
    /** 奖品图标 */
    private String prizeIcon;
    /** 奖品名称 */
    private String prizeName;
    /** 奖品类型 */
    private String prizeType;
    /** 奖品子类型 */
    private String prizeSubType;
    /** 值（金币使用） */
    private String prizeValue;
    /** 过期类型 */
    private String expiredType;
    /** 有效天数 */
    private Integer prizeEffectiveDay;
    /** 有效期截止时间 */
    private Long limitTime;
    /** 奖品数量 */
    private Integer prizeNum;
    /** 扩展参数 */
    private String extData;
    /** 场景 */
    private String scene;
    /** 价值金币 */
    private Long valueGold;
    /** toUid */
    private Long toUid;
    /** 是否付费（针对礼物类型奖励） */
    private Boolean fee;

    public static SendPrizeDTO of(PrizeItem prize, String activityCode) {
        return SendPrizeDTO.builder()
                .appId(MDCUtil.getCurAppIdByMdc())
                .prizeValue(prize.getPrizeKey())
                .prizeType(prize.getPrizeType())
                .prizeSubType(prize.getPrizeSubType())
                .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                .prizeEffectiveDay(Optional.ofNullable(prize.getEffectiveDays()).orElse(15))
                .prizeNum(Optional.ofNullable(prize.getPrizeNum()).orElse(1))
                .fee(prize.getFee())
                .prizeName(prize.getPrizeName())
                .valueGold(prize.getValueGold() != null ? prize.getValueGold().longValue() : null)
                .scene(activityCode).build();
    }

    public static SendPrizeDTO of(PrizeItemDO prize, String activityCode) {
        return SendPrizeDTO.builder()
                .appId(MDCUtil.getCurAppIdByMdc())
                .prizeValue(prize.getPrizeKey())
                .prizeType(prize.getPrizeType())
                .prizeSubType(prize.getPrizeSubType())
                .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                .prizeEffectiveDay(Optional.ofNullable(prize.getEffectiveDays()).orElse(15))
                .prizeNum(Optional.ofNullable(prize.getPrizeNum()).orElse(1))
                .fee(prize.getFee())
                .scene(activityCode).build();
    }

    public static SendPrizeDTO of(DrawPoolItemDTO drawPoolItemDTO, String activityCode) {
        drawPoolItemDTO.getDrawPoolItemDO().setScene(activityCode);
        return of(drawPoolItemDTO);
    }

    public static SendPrizeDTO of(DrawPoolItemDTO drawPoolItemDTO) {
        if (Objects.isNull(drawPoolItemDTO)) {
            return null;
        }
        DrawPoolItemDO prize = drawPoolItemDTO.getDrawPoolItemDO();
        if (Objects.isNull(prize)) {
            return SendPrizeDTO.builder().build();
        }
        Integer num = Objects.isNull(drawPoolItemDTO.getTargetTimes()) ? prize.getItemNum() : prize.getItemNum() * drawPoolItemDTO.getTargetTimes();
        return SendPrizeDTO.builder()
                .appId(MDCUtil.getCurAppIdByMdc())
                .prizeValue(prize.getItemKey())
                .prizeType(prize.getItemType())
                .prizeSubType(prize.getItemSubType())
                .expiredType(StringUtils.isBlank(prize.getExpiredType()) ? ExpiredTypeEnum.EFFECTIVE_DAYS.getCode() : prize.getExpiredType())
                .prizeEffectiveDay(prize.getItemEffectiveDay())
                .prizeNum(num)
                .scene(prize.getScene())
                .valueGold(prize.getItemValueGold())
                .prizeName(prize.getItemName())
                .prizeIcon(prize.getItemIcon())
                .limitTime(prize.getTimeLimit())
                .extData(prize.getExtData()).build();
    }

    public static SendPrizeDTO of(SendPrizeAdminVO sendPrizeAdminVO, Long prizeValueGold) {
        if (Objects.isNull(sendPrizeAdminVO)) {
            return null;
        }
        return SendPrizeDTO.builder()
                .appId(MDCUtil.getCurAppIdByMdc())
                .prizeValue(sendPrizeAdminVO.getPrizeValue())
                .prizeType(sendPrizeAdminVO.getPrizeType())
                .prizeSubType(sendPrizeAdminVO.getPrizeSubType())
                .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                .prizeEffectiveDay(sendPrizeAdminVO.getEffectiveDays())
                .prizeNum(sendPrizeAdminVO.getNum())
                .scene("ACTIVITY_DEFAULT")
                .valueGold(prizeValueGold)
                .extData("").build();
    }

    public static SendPrizeDTO of(ScenePrizeDO scenePrizeDO) {
        return of(scenePrizeDO, null);
    }

    public static SendPrizeDTO of(ScenePrizeDO scenePrizeDO, Long toUid) {
        if (Objects.isNull(scenePrizeDO)) {
            return null;
        }
        // 定向物品处理
        if (Objects.nonNull(scenePrizeDO.getToUid())) {
            scenePrizeDO.setToUid(toUid);
        }
        return SendPrizeDTO.builder()
                .appId(scenePrizeDO.getAppId())
                .prizeValue(scenePrizeDO.getPrizeValue())
                .prizeIcon(scenePrizeDO.getPrizeIcon())
                .prizeType(scenePrizeDO.getPrizeType())
                .prizeSubType(scenePrizeDO.getPrizeSubType())
                .expiredType(scenePrizeDO.getExpiredType())
                .prizeEffectiveDay(scenePrizeDO.getPrizeEffectiveDay())
                .prizeNum(scenePrizeDO.getPrizeNum())
                .scene(scenePrizeDO.getActivityCode())
                .extData(scenePrizeDO.getExtData())
                .valueGold(scenePrizeDO.getPrizeValueGold())
                .toUid(scenePrizeDO.getToUid())
                .prizeName(scenePrizeDO.getPrizeDesc())
                .scene(scenePrizeDO.getActivityCode())
                .build();
    }

    public static SendPrizeDTO of(DrawPoolItemDO drawPoolItemDO,Long toUid) {
        if (Objects.isNull(drawPoolItemDO)) {
            return null;
        }
        return SendPrizeDTO.builder()
                .appId(MDCUtil.getCurAppIdByMdc())
                .prizeValue(drawPoolItemDO.getItemKey())
                .prizeIcon(drawPoolItemDO.getItemIcon())
                .prizeType(drawPoolItemDO.getItemType())
                .prizeSubType(drawPoolItemDO.getItemSubType())
                .expiredType(drawPoolItemDO.getExpiredType())
                .prizeEffectiveDay(drawPoolItemDO.getItemEffectiveDay())
                .prizeNum(drawPoolItemDO.getItemNum())
                .extData(drawPoolItemDO.getExtData())
                .valueGold(drawPoolItemDO.getItemValueGold())
                .toUid(toUid)
                .prizeName(drawPoolItemDO.getItemName())
                .scene(drawPoolItemDO.getScene())
                .build();
    }


    public PrizeItem convert2PrizeItem() {
        return PrizeItem.builder()
                .prizeName(this.prizeName)
                .prizeIcon(this.prizeIcon)
                .valueGold(Math.toIntExact(this.valueGold))
                .build();
    }
}
