package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common;

/**
 * 天生羁绊常量
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:18 2025/4/22
 */
public class EmotionalBondsConstant {
    public static final String ACTIVITY_CODE = "emotional_bonds";
    public static final String GIFT_PACKAGE_SCENE = "gift_package_scene";
    public static final String GIFT_DISCOUNT_TAG="限时8折";
    public static final String MEMO = "天生羁绊活动购买礼盒";
    //打包购买价格
    public static final Long ALL_GIFT_PRICE =30000L;
    public static final String DISCOUNT_PRICE_CODE = "discountPrice";
    public static final String RANK_KEY = "ump:activity:%s:rank:%s";
    public static final String BASE_ICON_URL = "https://res-cdn.nuan.chat/nuanliao-fronted/activity/fetters/%s-icon.png";
    public static final String RANK_REWARD = "rank";
    public static final String EQUIPPED_RELATIONSHIP_KEY = "ump:activity:%s:equipped_relationship:%s";
    // 关系过期提醒记录键（记录已经提醒过的用户）
    public static final String EXPIRATION_REMINDER_KEY = "ump:activity:%s:expiration_reminder:%s";
    // 情侣特效列表缓存键
    public static final String COUPLE_EFFECTS_CACHE_KEY = "ump:activity:%s:couple_effects";
    // 单个情侣特效缓存键（用户ID和特效代码）
    public static final String COUPLE_EFFECT_CACHE_KEY = "ump:activity:%s:couple_effect:%s:%s";
    // 缓存过期时间（1小时，单位：秒）
    public static final int CACHE_EXPIRE_SECONDS = 3600;
    public static final String BIND_FRIEND_RANK_KEY = "ump:activity:%s:bind_friend_rank:%s";
}
