package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.EmotionalBondsJobManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.EmotionalBondsSpecialEffectManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.CoupleEffectVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 天生羁绊活动控制器
 */
@RestController
public class EmotionalBondsController {

    @Resource
    private EmotionalBondsSpecialEffectManager emotionalBondsSpecialEffectManager;

    @Resource
    private EmotionalBondsJobManager emotionalBondsJobManager;

    /**
     * 获取情侣特效列表
     *
     * @param param 基础参数
     * @return 情侣特效列表
     */
    @GetMapping("/api/inner/activity/emotional-bonds/couple-effects")
    public Result<List<CoupleEffectVO>> getCoupleEffects(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
            emotionalBondsSpecialEffectManager.getCoupleEffects(param));
    }

    /**
     * 根据特效代码获取情侣特效
     *
     * @param effectCode 特效代码
     * @return 情侣特效
     */
    @GetMapping("/api/inner/activity/emotional-bonds/couple-effect")
    public Result<CoupleEffectVO> getCoupleEffectByCode(@RequestParam("uid") Long uid, @RequestParam("effectCode") String effectCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
            emotionalBondsSpecialEffectManager.getCoupleEffectByCode(uid, effectCode));
    }

    /**
     * 处理即将过期的关系提醒
     * 查找3天内即将过期的关系，并发送提醒消息
     * 每小时执行一次，每个用户只提醒一次
     * @return 处理的关系数量
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/job/ump/emotional-bonds/send-expiration-reminders")
    public Result<Integer> sendExpirationReminders() {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
            emotionalBondsJobManager.sendExpirationReminders());
    }

    //版本判断
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/emotional-bonds/version-verify")
    public Result<Boolean> emotionalBondsVersionVerify(BaseParam param, @RequestParam("uid") Long uid, @RequestParam("appId") Long appId) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                emotionalBondsSpecialEffectManager.versionVerify(uid, appId));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/emotional-bonds/clear-costume-cache")
    public Result<Boolean> clearCostumeCache(@RequestParam("uid") Long uid) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                emotionalBondsSpecialEffectManager.clearCostumeCache(uid));
    }
}
