package cn.yizhoucp.ump.biz.project.web.vo;

import cn.yizhoucp.ump.biz.project.dto.RoomCoinBoxWeightDTO;
import lombok.extern.slf4j.Slf4j;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoinBoxConfigVO {
    private Long id;

    private String code;

    private String name;

    private Integer openUserMin;

    private Integer openUserMax;

    private String stepShow;

    private String probShow;

    private Integer allCoin;

    private List<Item> item;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        private Integer minCoin;

        private Integer maxCoin;

        private String weight;
    }
}
