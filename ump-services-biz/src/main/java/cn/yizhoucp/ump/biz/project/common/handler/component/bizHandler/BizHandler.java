package cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler;


import cn.yizhoucp.ump.api.enums.AdSpaceTypeEnum;
import cn.yizhoucp.ump.api.vo.appAdSpace.AppAdSpaceVO;
import cn.yizhoucp.ump.biz.project.common.ActivityTemplate;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerChainEnum;
import cn.yizhoucp.ump.biz.project.common.handler.BaseHandler;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import com.alibaba.fastjson.JSON;
import com.bytedance.tester.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Component("bizHandler")
public class BizHandler extends BaseHandler {


    @Resource
    private List<CallBackHandler> callBackHandlerList;

    private static final Map<String, JoinActivityHandler> joinActivityHandlerMap = new ConcurrentHashMap<>();
    private static final Map<String, ExitActivityHandler> exitActivityHandlerMap = new ConcurrentHashMap<>();
    private static final Map<String, BuoyInfoHandler> resourcesHandlerMap = new ConcurrentHashMap<>();
    private static final Map<String, PopBizHandler> popHandlerMap = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        log.debug("callbackhandlerList -> {}", callBackHandlerList);
        for (CallBackHandler handler : callBackHandlerList) {
            if (handler instanceof BuoyInfoHandler) {
                log.debug("init buoyInfoHandlerMap key:{}, value:{}", getHandlerCode(handler), JSON.toJSONString(handler));
                resourcesHandlerMap.put(getHandlerCode(handler), (BuoyInfoHandler) handler);
            }
            if (handler instanceof JoinActivityHandler) {
                joinActivityHandlerMap.put(getHandlerCode(handler), (JoinActivityHandler) handler);
            }
            if (handler instanceof ExitActivityHandler) {
                exitActivityHandlerMap.put(getHandlerCode(handler), (ExitActivityHandler) handler);
            }
            if (handler instanceof PopBizHandler) {
                popHandlerMap.put(getHandlerCode(handler), (PopBizHandler) handler);
            }
        }
        log.debug("popHandlerMap -> {}", popHandlerMap);
    }

    @Override
    protected void doHandle(HandlerContext context) {
        try {
            if (HandlerChainEnum.QUERY_AD_RESOURCE.equals(context.getChainEnum()) && Boolean.TRUE.equals(context.getQueryAdResourceBizHandle())) {
                doQueryAdResourceHandle(context);
            }

            String belongCode = getContextCode(context);
            if (StringUtils.isBlank(belongCode)) {
                return;
            }
            if (HandlerChainEnum.JOIN_ACTIVITY.equals(context.getChainEnum()) && Boolean.TRUE.equals(context.getJoinActivityBizHandle())) {
                doJoinActivityHandle(context, belongCode);
            }
            if (HandlerChainEnum.EXIT_ACTIVITY.equals(context.getChainEnum()) && Boolean.TRUE.equals(context.getExitActivityBizHandle())) {
                doExitActivityHandle(context, belongCode);
            }
            if (HandlerChainEnum.PUSH_POP.equals(context.getChainEnum()) && Boolean.TRUE.equals(context.getPopBizHandle())) {
                doPopHandle(context, belongCode);
            }
        } catch (Exception e) {
            log.error("bizHandler 处理失败 context:{}", JSON.toJSONString(context), e);
        }
    }

    private void doQueryAdResourceHandle(HandlerContext context) {
        if (MapUtils.isEmpty(resourcesHandlerMap) || CollectionUtils.isEmpty(context.getAdResourceList())) {
            log.debug("buoyInfoHandlerMap 或 adResourceList 为空");
            return;
        }
        List<AppAdSpaceVO> resources = context.getAdResourceList();
        log.info("doQueryAdResourceHandle resources:{}", JSON.toJSONString(resources));
        resources = resources.stream().map(r -> {
            if (Objects.nonNull(r.getBelongActivityCode())) {
                BuoyInfoHandler buoyInfoHandler = resourcesHandlerMap.get(r.getBelongActivityCode());
                log.debug("resourceActivityCode:{}, handler:{}", r.getBelongActivityCode(), JSON.toJSONString(buoyInfoHandler));
                if (Objects.nonNull(buoyInfoHandler)) {
                    context.setResourceCode(r.getActivityCode());
                    context.setActivityType(r.getActivityType());
                    r.setPayload(buoyInfoHandler.getPayloadInfo().apply(context));
                    BuoyInfoDTO buoyInfoDTO = buoyInfoHandler.getBuoyInfoHandler().apply(context);
                    // 使用 handler 返回信息覆盖默认信息
                    if (Objects.isNull(buoyInfoDTO)) {
                        return r;
                    }
                    r.setShowNum(buoyInfoDTO.getShowNum());
                    r.setCountDownTime(buoyInfoDTO.getCountDownTime());
                    r.setCountDownTimeColor(buoyInfoDTO.getCountDownTimeColor());
                    r.setCountDownTimeBorderColor(buoyInfoDTO.getCountDownTimeBorderColor());
                    r.setCountDownTimeTextSize(buoyInfoDTO.getCountDownTimeTextSize());
                    if (Objects.nonNull(buoyInfoDTO.getRouterUrl())) {
                        r.setActivityUrl(buoyInfoDTO.getRouterUrl());
                    }
                    if (Objects.nonNull(buoyInfoDTO.getRelationKey())) {
                        r.setActivitySvgaKey(buoyInfoDTO.getRelationKey());
                    }
                    // 设置活动资源位图标 语音房浮标类型
                    if (Objects.nonNull(buoyInfoDTO.getActivityIcon())) {
                        r.setActivityIcon(buoyInfoDTO.getActivityIcon());
                    }
                }
            }
            return r;
        }).collect(Collectors.toList());
        log.info("doQueryAdResourceHandle after result:{}", JSON.toJSONString(resources));
        context.setAdResourceList(resources);
    }

    private void doJoinActivityHandle(HandlerContext context, String activityCode) {
        joinActivityHandlerMap.get(activityCode).getJoinActivityHandler().accept(context);
    }

    private void doExitActivityHandle(HandlerContext context, String activityCode) {
        exitActivityHandlerMap.get(activityCode).getExitActivityHandler().accept(context);
    }

    private void doPopHandle(HandlerContext context, String activityCode) {
        PopBizHandler popBizHandler = popHandlerMap.get(activityCode);
        if (Objects.nonNull(popBizHandler)) {
            popBizHandler.getPopHandler().accept(context);
        }
    }

    private String getContextCode(HandlerContext context) {
        if (StringUtils.isNotBlank(context.getTemplateType())) {
            return context.getTemplateType();
        }
        return context.getActivityCode();
    }

    private String getHandlerCode(CallBackHandler handler) {
        if (handler instanceof ActivityTemplate) {
            String templateType = ((ActivityTemplate) handler).getTemplateType();
            if (templateType != null) {
                return templateType;
            }
        }
        return handler.getActivityCode();
    }
}
