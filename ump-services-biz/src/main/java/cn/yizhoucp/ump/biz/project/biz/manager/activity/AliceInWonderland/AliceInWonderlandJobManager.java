package cn.yizhoucp.ump.biz.project.biz.manager.activity.AliceInWonderland;

import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.starter.redis.manager.RedisManagerAspect;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.AliceInWonderlandConstant;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class AliceInWonderlandJobManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private RedisManagerAspect redisManagerAspect;

    @Resource
    private AliceInWonderlandRankManager rankManager;



    /**
     * 重置活动进度
     */
    public void resetActivityProgress() {
        String resetLock= AliceInWonderlandConstant.createResetLock();
        try {
            redisManager.setnx(resetLock, 1, DateUtil.ONE_HOUR_SECOND);
            //清空阵营分数
            List<Integer> campList= Arrays.asList(1, 2);
            for (Integer camp : campList) {
                //阵营总分数
            }
            log.info("重置活动进度");
        }finally {
            redisManager.delete(resetLock);
        }
    }

    public void sendPrize(String stage) {
        rankManager.sendRankPrize(stage);
    }
}
