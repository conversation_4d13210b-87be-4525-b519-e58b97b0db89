package cn.yizhoucp.ump.biz.common.core.biz;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用工具类
 *
 * Created by lu on 2017/12/7.
 */
public class CommonUtil {

    /**
     * 判断是否是ip地址
     *
     * @param ip 待校验信息
     * @return   是/否
     */
    public static boolean isIp(String ip) {

        if (StringUtils.isBlank(ip)) {
            return false;
        }

        if (ip.length() < 7 || ip.length() > 15 || "".equals(ip)) {
            return false;
        }

        String regex = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";

        Pattern pat = Pattern.compile(regex);

        Matcher mat = pat.matcher(ip);

        return mat.find();
    }


    public static boolean isEmpty(String str) {
        if (str == null || "".equals(str.trim())) {
            return true;
        } else {
            return false;
        }
    }
    public static boolean isEmpty(Object str) {
        if (str == null) {
            return true;
        } else {
            return false;
        }
    }
    public static boolean isEmpty(Map<?, ?> map) {
        if (map == null || map.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }
    public static boolean isEmpty(Long str) {
        if (str == null) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isEmpty(List<?> list) {
        if (list == null || list.size() == 0) {
            return true;
        } else {
            return false;
        }
    }
    public static boolean isEmpty(Set<?> set) {
        if (set == null || set.size() == 0) {
            return true;
        } else {
            return false;
        }
    }
    public static boolean isEmpty(String[] arr) {
        if (arr == null || arr.length == 0) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean isInteger(String str) {
        if (CommonUtil.isEmpty(str)){
            return false;
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    public static Date getPlusOneDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, 1);// 今天+1天
        c.add(Calendar.SECOND, -1);//-1秒
        date = c.getTime();
        return date;
    }

}
