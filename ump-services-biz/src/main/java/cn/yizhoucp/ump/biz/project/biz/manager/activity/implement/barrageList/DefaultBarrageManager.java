package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.barrageList;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.barrage.BarrageComponent;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.barrageList.NormalBarrageListKeyEnum;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 字符串弹幕策略
 * <p>
 * todo: 体系混乱，待重构，谨慎拓展
 *
 * @author: lianghu
 */
@Component
public class DefaultBarrageManager implements BarrageManager {

    @Resource
    protected BarrageComponent barrageComponent;

    /** 普通弹幕 key */
    private static final String BARRAGE_LIST_KEY = "ump:barrageListL:%s";
    /** 单词拉取弹幕条数 */
    private static final Long NUM_PER_REQUEST = 20L;
    /** 默认弹幕 */
    private static final String DEFAULT_BARRAGE = "敬请期待！";

    @Override
    public Object getBarrageList() {
        // 获取弹幕
        List<String> barrageList = barrageComponent.getBarrageList(String.format(BARRAGE_LIST_KEY, ActivityInfoUtil.getActivityCode()), NUM_PER_REQUEST);
        if (Objects.isNull(barrageList) || barrageList.size() < 1) {
            return Lists.newArrayList(DEFAULT_BARRAGE);
        }
        return barrageList;
    }

    @Override
    public String putBarrage(String msg) {
        barrageComponent.putBarrage(String.format(BARRAGE_LIST_KEY, ActivityInfoUtil.getActivityCode()), msg);
        return msg;
    }

    public String putBarrage(String msg, int targetSize) {
        barrageComponent.putBarrage(String.format(BARRAGE_LIST_KEY, ActivityInfoUtil.getActivityCode()), msg, targetSize);
        return msg;
    }

    public String putBarrage(String activityCode, String msg) {
        barrageComponent.putBarrage(String.format(BARRAGE_LIST_KEY, activityCode), msg);
        return msg;
    }

}
