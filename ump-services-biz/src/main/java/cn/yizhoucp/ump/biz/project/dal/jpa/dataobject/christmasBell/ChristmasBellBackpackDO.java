package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.christmasBell;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 圣诞铃铛背包
 * @create 2024-12-13
 **/
@EntityListeners(value = AuditingEntityListener.class)
@Entity
@Table(name = "christmas_bell_backpack")
@Data
public class ChristmasBellBackpackDO {

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long uid;

    /**
     * 铃铛 key
     */
    private String bellKey;

    /**
     * 铃铛数量
     */
    private Integer bellCount;


    /**
     * 创建时间
     */
    @CreatedDate
    private Date createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date updateTime;


}
