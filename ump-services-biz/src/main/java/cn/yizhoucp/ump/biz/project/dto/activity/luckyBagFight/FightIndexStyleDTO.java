package cn.yizhoucp.ump.biz.project.dto.activity.luckyBagFight;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 首页样式（打怪版）
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FightIndexStyleDTO implements Serializable {

    /** 标题 */
    private String indexTitleImg;
    /** 福袋背景 */
    private String indexBgImg;
    /** 顶部手气福袋按钮 */
    private String topBagBtn;
    /** 顶部打怪兽按钮 */
    private String topFightBtn;
    /** 武器列表 */
    private String indexWeaponList;
    /** 武器描述 */
    private String indexWeaponDesc;
    /** 武器规则图 */
    private String indexWeaponRuleImg;
    /** 弹幕背景 */
    private String indexBarrageBgImg;
    /** 按钮背景 */
    private String indexBtnBg;
    /** 礼物区背景底色 */
    private String indexGiftBgColor;
    /** 单抽按钮 */
    private String indexDrawBtn1;
    /** 十抽按钮 */
    private String indexDrawBtn10;
    /** 百抽按钮 */
    private String indexDrawBtn100;
    /** 福袋 icon */
    private String indexBagIcon;
    /** 保底背景 */
    private String indexGuarantBgImg;
    /** 保底文案背景 */
    private String indexGuarantTextColor;
}
