package cn.yizhoucp.ump.biz.project.biz.manager.activity.clickToGold;

import cn.yizhoucp.family.api.client.FamilyFeignService;
import cn.yizhoucp.family.api.dto.family.FamilyInfoDTO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.ClickToGoldConstant.*;

/**
 * 点石成金 定时任务相关内容
 * <AUTHOR>
 * @Date 2023/2/20 16:18
 * @Version 1.0
 */
@Slf4j
@Component
public class ClickToGoldJobManager {

    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private RedisManager redisManager;
    @Resource
    private FamilyFeignService familyFeignService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ClickToGoldBizManager clickToGoldBizManager;
    @Resource
    private ClickToGoldPageManager clickToGoldPageManager;


    private static final String PACKAGE_INFO = "今天是周日！您还有 %s 颗原石，%s 颗灵珠未兑换使用，待今晚23:59:59将会自动清空，请及时使用哦~";
    private static final String RANK_MSG = "点石成金！恭喜您的家族获得本周勇闯通天塔的冠军！奖励3个%s放至您的背包（您可自由选择活跃用户赠送），继续保持噢！";

    /**
     * 调用时机：
     *          每周周日 八点调用
     * 通知用户背包 原石和灵珠余量
     * @return
     */
    public Boolean notifyUserPackageInfo() {
        log.info("notifyUserPackageInfo");
        Set<Long> userIds = Sets.newHashSet();
        List<Object> pearlUids;
        List<Object> stoneUids;
        do {
            pearlUids = redisManager.sPopCount(String.format(PEARL_NUMS, ActivityTimeUtil.getStartAndEndWeek(clickToGoldBizManager.getActivityCode())), 1000L);
            log.debug("pearlUids {}", JSON.toJSONString(pearlUids));
            if (!CollectionUtils.isEmpty(pearlUids)) {
                pearlUids.forEach(id -> userIds.add(Long.valueOf(id.toString())));
            }
        } while (CollectionUtils.isNotEmpty(pearlUids));
        do {
            stoneUids = redisManager.sPopCount(String.format(ROUGH_STONE_NUMS, ActivityTimeUtil.getStartAndEndWeek(clickToGoldBizManager.getActivityCode())), 1000L);
            log.debug("stoneUids {}", JSON.toJSONString(stoneUids));
            if (!CollectionUtils.isEmpty(stoneUids)) {
                stoneUids.forEach(id -> userIds.add(Long.valueOf(id.toString())));
            }
        } while (CollectionUtils.isNotEmpty(stoneUids));
        log.info("notifyUserPackageInfo uids {}", JSON.toJSONString(userIds));
        for (Object uid : userIds) {
            Long userId = Long.valueOf(uid.toString());

            //Integer pearlNums = Optional.ofNullable((Integer) redisManager.hget(String.format(PEARL_NUMS, ActivityTimeUtil.getStartAndEndWeek(clickToGoldBizManager.getActivityCode())), uid.toString())).orElse(0);
            //Integer roughStoneNums = Optional.ofNullable((Integer) redisManager.hget(String.format(ROUGH_STONE_NUMS, ActivityTimeUtil.getStartAndEndWeek(clickToGoldBizManager.getActivityCode())), uid.toString())).orElse(0);
            List<Long> giftNum = clickToGoldPageManager.getGiftNum(BaseParam.builder().uid(userId).build());
            if (giftNum.get(0) == 0 && giftNum.get(1) == 0) {
                continue;
            }
            notifyComponent.npcNotify(userId, String.format(PACKAGE_INFO, giftNum.get(0), giftNum.get(1)));
        }
        return Boolean.TRUE;
    }

    /**
     *  家族榜单 前三榜单处理
     * @return
     */
    public Boolean sendRankReward() {
        String rankKey = String.format(FAMILY_RANK, ActivityTimeUtil.getLastWeekStartAndEnd(clickToGoldBizManager.getActivityCode()));
        log.info("sendRandReward rank {}", rankKey);
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(rankKey, 0d, Double.MAX_VALUE, 0, 3);
        if (CollectionUtils.isEmpty(typedTuples)) {
            return Boolean.FALSE;
        }
        log.info("sendRandReward 榜单前三 {}", JSON.toJSONString(typedTuples));
        int rank = 0;
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            rank++;
            long familyId = Long.parseLong(typedTuple.getValue().toString());
            FamilyInfoDTO familyInfoDTO = familyFeignService.findFamilyInfoById(familyId, ServicesAppIdEnum.lanling.getAppId()).successData();
            if (Objects.isNull(familyInfoDTO)) {
                log.info("findFamilyInfoById familyId {} return null", familyId);
                continue;
            }
            sendPrizeManager.sendGift(ServicesAppIdEnum.lanling.getAppId(), getGiftKeyByRank(rank - 1), familyInfoDTO.getOwnerUid(), 3L, 15, "click-to-gold");
            log.info("sendRandReward familyId {} uid {} giftKey {}", familyId, familyInfoDTO.getOwnerUid(), getGiftKeyByRank(rank - 1));
            notifyComponent.npcNotify(familyInfoDTO.getOwnerUid(), String.format(RANK_MSG, getGiftNameByRank(rank - 1)));
            String msg = null;
            if (rank == 1) {
                msg = String.format("恭喜，%s 家族成为上周最强炼化家族", familyInfoDTO.getName());
            } else if (rank == 2) {
                msg = String.format("恭喜，%s 家族上周位居荣耀榜第二位", familyInfoDTO.getName());
            } else if (rank == 3) {
                msg = String.format("恭喜，%s 家族上周位居荣耀榜第三位", familyInfoDTO.getName());
            }
            if (StringUtils.isNotBlank(msg)) {
                redisManager.hset(String.format(FAMILY_RANK_RECORD, ActivityTimeUtil.getLastWeekStartAndEnd(clickToGoldBizManager.getActivityCode())), String.valueOf(rank), msg, DateUtil.ONE_WEEK_SECONDS * 3);
            }
        }
        return Boolean.TRUE;
    }

    private String getGiftKeyByRank(Integer rank) {
        if (rank == null || rank > 2) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return COMMON_REWARD.get(rank).getGiftKey();
    }

    private String getGiftNameByRank(Integer rank) {
        if (rank == null || rank > 2) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        return COMMON_REWARD.get(rank).getName();
    }

}
