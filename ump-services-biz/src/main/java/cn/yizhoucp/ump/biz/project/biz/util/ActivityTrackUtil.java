package cn.yizhoucp.ump.biz.project.biz.util;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class ActivityTrackUtil {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动抽奖
     *
     * @param activityType 活动类型
     * @param uid 用户ID
     * @param poolCode 奖池代码
     * @param itemKey 奖励键
     * @param itemAmount 奖励金额
     * @param itemCount 奖励数量
     */
    public void trackLottery(String activityType, String poolCode, String itemKey, Long itemCount, Long itemAmount,Long uid ) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", activityType);
        params.put("attribute_type", "platform_activity");
        params.put("pool_code", poolCode);
        params.put("award_key", itemKey);
        params.put("award_amount", itemAmount);
        params.put("award_count", itemCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动任务完成
     *
     * @param activityType 活动类型
     * @param uid 用户ID
     * @param taskType 任务类型
     */
    public void trackTaskFinish(String activityType, String taskType, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", activityType);
        params.put("attribute_type", "platform_activity");
        params.put("task_type", taskType);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动领取奖励
     *
     * @param activityType 活动类型
     * @param uid 用户ID
     * @param itemKey 奖励键
     * @param itemAmount 奖励数量
     * @param rewardType 奖励类型
     */
    public void trackReceiveAward(String activityType, String rewardType,String itemKey, Long itemCount, Integer itemAmount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", activityType);
        params.put("attribute_type", "platform_activity");
        params.put("award_type", rewardType);
        params.put("award_key", itemKey);
        params.put("award_amount", itemAmount);
        params.put("award_count", itemCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }
}