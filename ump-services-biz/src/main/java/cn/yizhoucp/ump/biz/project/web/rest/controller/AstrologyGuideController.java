package cn.yizhoucp.ump.biz.project.web.rest.controller;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.AstrologyGuideVO;
import cn.yizhoucp.ump.biz.project.biz.manager.AstrologyGuideManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class AstrologyGuideController {

    @Resource
    private AstrologyGuideManager astrologyGuideManager;

    @Deprecated
    @GetMapping("/api/inner/ump/astrology-guide/get-user-show-guide-status")
    public Result<AstrologyGuideVO> getUserShowGuideStatus(@RequestParam("uid") Long uid, @RequestParam("create") Boolean create) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyGuideManager.getUserShowGuideStatus(uid, create, null));
    }

    @Deprecated
    @GetMapping("/api/inner/ump/astrology-guide/process-next")
    public Result<Boolean> processNext(@RequestParam("uid") Long uid, @RequestParam("curStep") Integer curStep) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> astrologyGuideManager.processNext(uid, curStep));
    }
}
