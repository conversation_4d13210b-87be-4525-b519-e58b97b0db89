package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw;

import cn.hutool.core.util.RandomUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.util.Base64Util;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.coinservices.UserAccountVO;
import cn.yizhoucp.ms.core.vo.imservices.CountDownVO;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageAttrsBayWindow;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageModel;
import cn.yizhoucp.ms.core.vo.landingservices.label.TagVO;
import cn.yizhoucp.product.client.CoinGiftProductFeignService;
import cn.yizhoucp.rank.api.enums.RankTimeEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyFight.DrawBaseVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyFight.DrawButtonVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyFight.DrawRewardVO;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.rank.api.client.RankInfoFeignService;
import cn.yizhoucp.rank.api.dto.RankItemDTO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.luckyBag.LuckyBagTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.luckyBag.DrawTaskEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawConfig;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.luckyBag.LuckyBagActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import cn.yizhoucp.ump.biz.project.biz.util.NumberUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ms.core.base.ErrorCode.DRAW_CONFIG_ERROR;
import static cn.yizhoucp.ms.core.base.enums.CoinBusinessType.QUESTION_ACTIVITY;

/**
 * <AUTHOR> pepper
 * @Classname ChatieFamilyDrawManager
 * @Description
 * @Date 2022/6/6 14:58
 */
@Slf4j
@Component
public class SakaFamilyDrawManager extends AbstractDrawManager {

    @Resource
    private RankInfoFeignService rankInfoFeignService;

    @Resource
    private FeignImService feignImService;

    @Resource
    @Qualifier("reportHandleThreadPool")
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private LuckyBagActivityManager luckyBagActivityManager;

    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private DrawPoolService drawPoolService;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private LogComponent logComponent;

    @Resource
    private CoinGiftProductFeignService coinGiftProductFeignService;

    private static final String SAKA_FAMILY_LUCK_VALUE = "ump:saka:family:luck:value";

    private static final String SAKA_FAMILY_LUCK_TIMES = "ump:saka:family:luck:times";


    private static final String GIFT_NAME_PRE = "splice.getMissionList.gift.name";


    private static final String BARRAGE_CODE = "splice.family.draw.barrage";
    private static final String BARRAGE_HTML_CODE = "splice.family.draw.html.barrage";

    private static final String DRAW_TAG_PRE = "splice.family.getMissionList.tag.";

    private static final String BACKGROUP_IMG = "https://chatie-backend-cdn.myrightone.com/res/chatie/gift/simple-back3.png";
    private static final String BACKGROUP_ICON = "https://chatie-backend-cdn.myrightone.com/res/chatie/gift/simple-left1.png";

    private static final String CLICK_ICON = "https://chatie-backend-cdn.myrightone.com/res/chatie/gift/simple-more4.png";
    private static final String BUSINESS = "SakaFamilyDraw";

    /**
     * 每周抽奖次数统计缓存 yyyyMMdd map接口 key为 userId value为 times
     */
    private static final String SAKA_WEEK_LUCKY_DRAW_TIMES = "ump:saka:lucky:getMissionList:times:%s";

    /**
     * 每个用户每周的领取记录 yyyyMMdd userId map接口 key为code value为领取时间戳
     */
    private static final String SAKA_WEEK_LUCKY_DRAW_RECEIVE_RECORD = "ump:saka:lucky:getMissionList:receive:record:%s:%s";

    private static final String TASK_CODE = "lucky_bag_task_reward";

    private static final List<DrawButtonVO> drawButtons = new ArrayList<>();

    static {
        DrawButtonVO button1 = new DrawButtonVO();
        button1.setTimes(1);
        button1.setOriginalPrice(10);
        button1.setFinalPrice(10);
        drawButtons.add(button1);
        DrawButtonVO button2 = new DrawButtonVO();
        button2.setTimes(10);
        button2.setOriginalPrice(100);
        button2.setFinalPrice(100);
        drawButtons.add(button2);
        DrawButtonVO button3 = new DrawButtonVO();
        button3.setTimes(100);
        button3.setOriginalPrice(1000);
        button3.setFinalPrice(900);
        button3.setLeftTopIcon("https://chatie-backend-cdn.myrightone.com/res/chatie/activity/luckyBag/left-top-draw.png");
        drawButtons.add(button3);

    }

    @Override
    protected void resourceCheck(DrawContext context) {

        //1. 检查需要的资源
        Long count = this.getDrawCoin(context);

        //2. 扣除资源;
        boolean deduct = this.deductCoin(context, count);
        if (!deduct) {
            throw new ServiceException(ErrorCode.BALANCE_NOT_ENOUGH, getErrorMsg());
        }
        // 放入缓存中
        this.setUserLuckyDrawWeekTimes(context.getDrawParam().getUid(), null, context.getDrawParam().getTimes());
    }

    @Override
    public DrawReturn draw(DrawParam param) {
        DrawContext context = DrawContext.builder().drawParam(param).build();

        // 资源检查
        this.resourceCheck(context);

        // 获取抽奖奖品
        List<DrawPoolItemDO> prizeDOList = this.doDraw(param);

        // 下发奖励
        super.sendPrize(ActivityInfoUtil.getActivityCode(), SecurityUtils.getCurrentUserIdLong(), prizeDOList, context);

        // 扣除资源
        this.deductResource(context);

        // 记录日志
        logComponent.putDrawLog(param.getUnionId(), param.getAppId(), param.getActivityCode(), param.getUid(), prizeDOList);

        // 后置处理
        this.callback(context);

        DrawReturn drawReturn = DrawReturn.builder()
                .hasRealPrize(hasRealPrize(prizeDOList))
                .luckValue(getLuckValue(param.getUid().toString()))
                .prizeItemList(Lists.transform(prizeDOList, DrawPoolItemDO::convert2PrizeItem)).build();

        log.debug("drawReturn :{}", drawReturn);
        return drawReturn;
    }

    public List<DrawPoolItemDO> doDraw(DrawParam param) {

        // 获取活动配置
        DrawPoolDO drawPoolDO = drawPoolService.getByActivityCodeAndPoolCodeAndEnable(param.getActivityCode(), param.getPoolCode());

        log.debug("doDraw : {}", drawPoolDO);
        if (Objects.isNull(drawPoolDO)) {
            log.error("获取奖池失败 activityCode:{}, poolCode:{}", param.getActivityCode(), param.getPoolCode());
            throw new ServiceException(DRAW_CONFIG_ERROR);
        }


        DrawConfig config = DrawConfig.getInstance(drawPoolDO);
        if (Objects.isNull(config)) {
            log.error("奖池配置失败 activityCode:{}, poolCode:{}", param.getActivityCode(), param.getPoolCode());
            throw new ServiceException(DRAW_CONFIG_ERROR);
        }

        int luckTimes = getLuckTimes(config.getLuckyLimit(), param.getTimes(), param.getUid());

        // 抽奖
        Map<String, DrawPoolItemDO> giftMap = new HashMap<>(8);


        //幸运值抽奖
        luckDraw(param, config, giftMap, luckTimes);

        //默认抽奖
        defaultDraw(param, config, giftMap, drawPoolDO, luckTimes);


        return sortAndConvert(giftMap);
    }

    @Override
    protected void deductResource(DrawContext context) {
    }

    @Override
    protected void callback(DrawContext context) {
        if (log.isDebugEnabled()) {
            log.debug("callback : {}", context);
        }
        try {
            // 记录本次抽奖已完成
            DrawParam param = context.getDrawParam();
            // 小助手消息、数据埋点
            Long uid = Optional.ofNullable(param.getUid()).orElse(-1L);

            //基于业务需要，扩展字段传递的值为家族id
            Long familyId = null;
            Long chatId = null;
            if (JSON.isValid(param.getExtValue())) {
                JSONObject jsonObject = JSON.parseObject(param.getExtValue());
                familyId = jsonObject.getLong("familyId");
                chatId = jsonObject.getLong("chatId");
            }

            String type = param.getType();
            String activityCode = param.getActivityCode();
            Long appId = param.getAppId();

            List<DrawPoolItemDO> prizeDOList = context.getPrizeDOList();

            log.info(" 抽奖后置处理 uid:{}, familyId:{}, type:{}, prize:{}", uid, familyId, type, prizeDOList);
            taskExecutor.submit(() -> incrRank(uid, activityCode, prizeDOList));

            putBarrage(prizeDOList, uid, chatId, appId);

            track(uid, context.getFeeCoin(), context.getFreeCoin(), familyId, param.getTimes(), prizeDOList);

            prizeInternation(prizeDOList);

            //活动处理
            luckyBagActivityManager.setUserLuckyBagNumber(uid, param.getTimes());

            // 福袋埋点
//            final Long finalFamilyId = familyId;
//            taskExecutor.submit(() -> dataReport(param, finalFamilyId, prizeDOList));
        } catch (Exception e) {
            log.error("抽奖后置处理异常", e);
        }
    }

    /**
     * 礼物名称国际化
     *
     * @param prizeDOList
     */
    private void prizeInternation(List<DrawPoolItemDO> prizeDOList) {
        for (DrawPoolItemDO item : prizeDOList) {
            item.setItemName(getItemNameByKey(item.getItemKey()));
        }
    }

    public Integer getLuckValue(String item) {
        return NumberUtil.getInt(redisManager.hget(SAKA_FAMILY_LUCK_VALUE, item), 0);
    }

    private void putBarrage(List<DrawPoolItemDO> prizeDOList, Long uid, Long chatId, Long appId) {


        for (DrawPoolItemDO item : prizeDOList) {

            if (item.getItemValueGold() > 99) {
                UserBaseVO userBaseVO = feignUserService.getUserWithoutStatusBaseVO(appId, uid).successData();

                log.info("putBarrage , uid : {}, userBaseVO : {}", uid, userBaseVO);
                String h5msg =
                        internationalManager.changeCommonText(
                                new Object[]{userBaseVO.getName(), item.getItemIcon(), item.getTargetTimes(), item.getItemValueGold().toString()},
                                BARRAGE_HTML_CODE);
                String msg =
                        internationalManager.changeCommonText(
                                new Object[]{userBaseVO.getName(), getItemNameByKey(item.getItemKey()), item.getTargetTimes()},
                                BARRAGE_CODE);

                barrageManagerFactory.getStrategy().putBarrage(msg);
                log.debug("putBarrage h5msg -> {} item -> {} msg -> {}", h5msg, item, msg);
                barrage(h5msg, uid, chatId, appId, userBaseVO.getAvatar());
            }
        }
    }

    public DrawBaseVO getDrawBaseInfo(Long appId, Long userId) {
        DrawBaseVO result = new DrawBaseVO();
        if (appId == null || userId == null) {
            return result;
        }
        // 本周抽奖次数
        String startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        Integer times = this.getUserLuckyDrawWeekTimes(userId, startDateOfWeek);
        result.setCurrentTimes(times);

        // 奖励信息
        this.getDrawRewardList(userId, startDateOfWeek, times, result);

        // 抽奖按钮信息
        result.setDrawButtonList(drawButtons);
        return result;

    }

    public CommonResultVO rewardReceive(String code, Long appId, Long userId) {
        DrawTaskEnum drawTask = DrawTaskEnum.convertFormCode(code);
        log.debug(" rewardReceive code : {}, drawTask : {}", code, drawTask);
        CommonResultVO result = new CommonResultVO();
        result.setResult(false);
        if (appId == null || userId == null || drawTask == null) {
            return result;
        }
        if (!redisManager.setnx("lucky_draw_reward_receive_" + code + "_" + userId, userId, 3)) {
            return result;
        }
        String startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        // 校验是否已经领取过
        if (this.checkUserWeekReceiveRecord(userId, startDateOfWeek, drawTask.getCode())) {
            return result;
        }
        // 校验是否具备领取资格
        if (this.getUserLuckyDrawWeekTimes(userId, startDateOfWeek) < drawTask.getNeedTimes()) {
            return result;
        }
        // 设置为已经领取
        this.setUserWeekReceiveRecord(userId, startDateOfWeek, code);
        // 下发奖励
        String itemKey = null;
        if (LuckyBagTypeEnum.gift.equals(drawTask.getType())) {
            itemKey = drawTask.getItemKey();
        }
        luckyBagActivityManager.doLuckyBagExchangeAction(appId, userId, drawTask.getType(), drawTask.getNeedCoin(), TASK_CODE, LuckyBagActivityManager.SCENE_CODE, itemKey);
        result.setResult(true);
        Map<String, Object> params = new HashMap<>();
        params.put("prodname", drawTask.getName());
        params.put("userId", userId);
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), userId, "fudai_exchange", params, ServicesNameEnum.ump_services.getCode());
        return result;

    }

    private void getDrawRewardList(Long userId, String startDateOfWeek, Integer times, DrawBaseVO result) {
        Map<String, DrawTaskEnum> map = DrawTaskEnum.getDrawTaskMap();
        if (map == null || map.size() <= 0) {
            return;
        }
        Map<String, Long> recordMap = this.getUserWeekReceiveRecord(userId, startDateOfWeek);
        List<DrawRewardVO> list = new ArrayList<>(map.size());
        for (Map.Entry<String, DrawTaskEnum> entry : map.entrySet()) {
            String code = entry.getKey();
            DrawTaskEnum task = entry.getValue();
            DrawRewardVO drawRewardVO = new DrawRewardVO();
            drawRewardVO.setCode(code);
            Integer needTimes = task.getNeedTimes();
            drawRewardVO.setNeedTimes(needTimes);
            drawRewardVO.setIcon(task.getIcon());
            drawRewardVO.setRewardTips(StringUtils.isBlank(task.getInternationTips()) ? "" : task.getInternationTips());
            Long record = recordMap.get(code);
            int status = times < needTimes ? 0 : 1;
            if (status == 1) {
                status = record == null ? 1 : 2;
            }
            drawRewardVO.setCurrentStatus(status);
            list.add(drawRewardVO);
        }
        // 进行国际化操作
        for (DrawRewardVO drawRewardVO : list) {
            drawRewardVO.setRewardTips(internationalManager.changeCommonText(null, drawRewardVO.getRewardTips()));
        }
        list = list.stream().sorted(Comparator.comparing(DrawRewardVO::getNeedTimes)).collect(Collectors.toList());
        result.setRewardList(list);
    }

    private void incrRank(Long uid, String code, List<DrawPoolItemDO> prizeDOList) {

        int score = 0;
        for (DrawPoolItemDO item : prizeDOList) {
            score += item.getTargetTimes() * item.getItemValueGold();
        }

        log.info("SakaFamilyDrawManager incrRank uid : {}, score : {}", uid, score);

        Set<String> set = new HashSet<>();
        set.add(RankTimeEnum.DAYLY.getCode());
        set.add(RankTimeEnum.WEEKLY.getCode());
        rankInfoFeignService.updateRankItem(RankItemDTO.builder()
                .score(score)
                .item(uid.toString())
                .code(code)
                .type(JSON.toJSONString(set.toArray()))
                .build());

    }

    private void trackLuckValue(Long uid, Integer oldValue) {
        Map<String, Object> params = new HashMap<>();
        params.put("lucky_value", oldValue);
        params.put("userId", uid);
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "lucky_value_reach", params, ServicesNameEnum.ump_services.getCode());
    }

    private void track(Long uid, Long fee, Long free, Long familyId, Integer times, List<DrawPoolItemDO> prizeDOList) {


        Integer count = 0;
        for (DrawPoolItemDO item : prizeDOList) {
            count += item.getTargetTimes() * item.getItemValueGold().intValue();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("times", times);
        params.put("gift_amount", count);
        params.put("family_id", familyId);
        params.put("user_id", uid);
        params.put("amount", times * 10);
        params.put("free_count", free);
        params.put("fee_count", fee);
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "love_lottery_gold", params, ServicesNameEnum.ump_services.getCode());

        Map<String, Object> coinParams = new HashMap<>();
        coinParams.put("from", "fudai");
        coinParams.put("much", times * 10);
        coinParams.put("pay_count", fee);
        coinParams.put("free_count", free);
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "coin_spend", coinParams, ServicesNameEnum.ump_services.getCode());

    }

    private Long getDrawCoin(DrawContext context) {
        DrawParam params = context.getDrawParam();
        String poolCode = params.getPoolCode();

        params.setTimes(0);
        long price = 10L;
        if ("saka-family-one".equals(poolCode)) {
            params.setTimes(1);
        } else if ("saka-family-ten".equals(poolCode)) {
            params.setTimes(10);
        } else if ("saka-family-hundred".equals(poolCode)) {
            params.setTimes(100);
            // 百抽 需要打9折
            price = 9L;
        } else {
            //奖池匹配异常
            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR);
        }

        //基础抽奖为 10 金币/次
        Long count = params.getTimes() * price;

        return count;
    }

    private Boolean deductCoin(DrawContext context, Long count) {
        log.debug("deductCoin Enter");
        DrawParam params = context.getDrawParam();

        Long appId = params.getAppId();
        Long uid = params.getUid();
        UserAccountVO userAccountVO = feignUserService.getUserAccountByUid(appId, uid).successData();

        //没有获取到钱包信息
        if (userAccountVO == null) {
            return false;
        }

        //金额钱不够
        if (userAccountVO.getFreeBalance() + userAccountVO.getBalance() < count) {
            return false;
        }

        //优先扣除免费金币，钱不够的情况才会扣除付费金币
        Long freeCoin;
        Long feeCoin;
        if (userAccountVO.getFreeBalance() >= count) {
            freeCoin = count;
            feeCoin = 0L;
        } else {
            freeCoin = userAccountVO.getFreeBalance();
            feeCoin = count - freeCoin;
        }
        context.setFreeCoin(freeCoin);
        context.setFeeCoin(feeCoin);


        log.info("deductCoin reduceAccountCoin feeCoin : {}, freeCoin : {}", feeCoin, freeCoin);
        userCoinAccountManager.reduceCoin(params.getUnionId(), appId, uid, null, AppScene.family, AppFunctionEnum.lucky_bag_lottery.getCode(),
                feeCoin, freeCoin, AccountReduceType.adapt, BUSINESS, BUSINESS, QUESTION_ACTIVITY.getDescription());

        return true;
    }


    /**
     * chatie 使用
     *
     * @param msg
     * @param uid
     * @param chatId
     * @param appId
     * @param avator
     */
    private void barrage(String msg, Long uid, Long chatId, Long appId, String avator) {

        log.debug("SakaFamilyDrawManager barrage start uid : {}", uid);
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.server_push.getCode());

        SystemMessageAttrsBayWindow systemMessageAttrsBayWindow = new SystemMessageAttrsBayWindow();
        systemMessageAttrsBayWindow.setKey(ClientPushKey.all_channel_message.getCode());
        systemMessageAttrsBayWindow.setMainBg(BACKGROUP_IMG);
        systemMessageAttrsBayWindow.setMessage(msg);

        String fromUriEncode = Base64Util.encodeToString(avator == null ? "" : avator.replace("https://chatie-user-cdn.myrightone.com/", "") + "?x-oss-process=image/resize,h_70,m_lfit/rounded-corners,r_35/format,png");
        fromUriEncode = fromUriEncode.replace("/", "_");
        systemMessageAttrsBayWindow.setGiftIcon("https://chatie-user-cdn.myrightone.com/res/prod/bg-single.png?x-oss-process=image/resize,h_216,m_lfit/watermark,image_" + fromUriEncode + ",g_west,x_86");
//        systemMessageAttrsBayWindow.setGiftIcon(BACKGROUP_ICON);
        systemMessageAttrsBayWindow.setFamilyAnim(Boolean.TRUE);
        systemMessageAttrsBayWindow.setClickIcon(CLICK_ICON);

        // 设置开始时间、结束时间、持续时间
        Long Duration = 15L;
        CountDownVO countDown = new CountDownVO();
        countDown.setDuration(Duration);
        countDown.setStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.YMDHMS)));
        countDown.setEndTime(LocalDateTime.now().plusSeconds(Duration).format(DateTimeFormatter.ofPattern(DateUtil.YMDHMS)));

        systemMessageAttrsBayWindow.setCountDown(countDown);

        smm.setAttrs(systemMessageAttrsBayWindow);
        String body = JSON.toJSONString(smm);
        log.debug("doSendEffects body : {}", body);
        feignImService.sendTeamCustomSystemMsgBase64(chatId, uid, appId, Base64Util.encodeToString(body));
    }


    private String getErrorMsg() {
        String errorMsg = internationalManager.changeCommonText(null, "splice.balance.not.enough");

        log.debug("getErrorMsg errorMsg :{}", errorMsg);
        return errorMsg;
    }

    private String getItemNameByKey(String key) {
        SecurityUser securityUser = SecurityUtils.getCurrentUser();
        if (securityUser == null) {
            securityUser = new SecurityUser();
        }
        return coinGiftProductFeignService.findInternationNameByAppIdAndItemKeyAndLanguage(
                Optional.ofNullable(securityUser.getAppId()).orElse(639697L),
                key,
                Optional.ofNullable(MDCUtil.getLanguageByMdc()).map(UserLanguageEnum::getCode).orElse("en")
        ).successData();
    }

    private String getCoinMemo() {
        return internationalManager.changeCommonText(null, "splice.family.draw.deduct.coin");
    }


    private void luckDraw(DrawParam param, DrawConfig config,
                          Map<String, DrawPoolItemDO> giftMap, int limit) {
        log.debug("luckDraw Enter, limit : {}", limit);
        String poolCode = config.getLuckyPrizeKey();
        DrawPoolDO poolDO = drawPoolService.getByActivityCodeAndPoolCodeAndEnable(param.getActivityCode(), poolCode);

        List<DrawPoolItemDO> list = drawPoolItemService.getByPoolCode(poolCode);

        for (int i = 0; i < limit; i++) {
            DrawPoolItemDO drawPoolItemDO = doDraw(list, config, poolDO);
            redisManager.hincr(SAKA_FAMILY_LUCK_VALUE, param.getUid().toString(), -1 * config.getLuckyLimit(), cn.yizhoucp.ump.biz.project.biz.util.DateUtil.ONE_MONTH_SECOND);
            defaultTrack(param, config, drawPoolItemDO);
            putDrawPrize(giftMap, drawPoolItemDO);

            DrawPoolItemDO giftItem = giftMap.get(drawPoolItemDO.getItemKey());
            setTag(giftItem, createTag("luck"));
        }
    }

    private void defaultDraw(DrawParam param, DrawConfig config,
                             Map<String, DrawPoolItemDO> giftMap,
                             DrawPoolDO poolDO, int limit) {
        log.debug("defaultDraw Enter, limit : {}", limit);

        //奖品列表
        List<DrawPoolItemDO> list = drawPoolItemService.getByPoolCode(poolDO.getPoolCode());

        for (int i = limit; i < param.getTimes(); i++) {
            // 获取抽奖奖品
            DrawPoolItemDO drawPoolItemDO = doDraw(list, config, poolDO);
            // 埋点
            defaultTrack(param, config, drawPoolItemDO);
            putDrawPrize(giftMap, drawPoolItemDO);
        }
    }

    private void defaultTrack(DrawParam param, DrawConfig config, DrawPoolItemDO item) {
        if (StringUtils.isBlank(config.getDrawTrackKey())) {
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("uid", param.getUid());
        params.put("times", param.getTimes());
        params.put("type", param.getType());
        params.put("gift_key", item.getItemKey());
        yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), param.getUid(), config.getDrawTrackKey(), params, ServicesNameEnum.ump_services.getCode());
    }

    private Integer getLuckTimes(Integer luckyLimit, Integer times, Long userId) {
        log.info("setLuckValue limit : {}, times:{}, uid : {} ", luckyLimit, times, userId);

        String uid = userId.toString();
        Integer oldValue = getLuckValue(uid);
        Integer luckTimes = oldValue / luckyLimit;
        Integer value = getLuckByTime(times);

        if (log.isDebugEnabled()) {
            log.debug("setLuckValue oldValue : {}, luckTimes :{}, value : {}", oldValue, luckTimes, value);
        }

        if (luckTimes > 0) {
            trackLuckValue(userId, oldValue);
        }
        redisManager.hincr(SAKA_FAMILY_LUCK_VALUE, uid, value, cn.yizhoucp.ump.biz.project.biz.util.DateUtil.ONE_MONTH_SECOND);
        redisManager.hset(SAKA_FAMILY_LUCK_TIMES, uid, luckTimes);

        return luckTimes;

    }


    private Integer getLuckByTime(Integer times) {
        if (times == 1) {
            return 1;
        } else if (times == 10) {
            return 12;
        } else if (times == 100) {
            return 140;
        }
        return 0;
    }

    private List<DrawPoolItemDO> sortAndConvert(Map<String, DrawPoolItemDO> giftMap) {
        List<DrawPoolItemDO> list = new ArrayList<>(giftMap.values());

        // 根据礼物价值进行排序
        list.sort((o1, o2) -> {
            if (o1.getItemValueGold() > o2.getItemValueGold()) {
                return -1;
            } else {
                return 1;
            }
        });

        //按照业务逻辑，将第五种以后的礼物全部转换成最后一种礼物
        int count = list.size();
        if (count > 6) {
            int times = 0;
            for (int i = 5; i < count; i++) {
                times += list.get(i).getTargetTimes();
            }
            DrawPoolItemDO item = list.get(count - 1);
            item.setTargetTimes(times);

            list = new ArrayList<>(list.subList(0, 6));
            list.set(5, item);
        }

        list.forEach(item -> {
            if (item.getItemValueGold() >= 520) {
                setTag(item, createTag("rate"));
            }
        });

        return list;
    }

    private TagVO createTag(String tag) {
        TagVO tagVO = new TagVO();
        tagVO.setTag(tag);
        tagVO.setValue(internationalManager.changeCommonText(null, DRAW_TAG_PRE.concat(tag)));
        return tagVO;
    }

    private void setTag(DrawPoolItemDO item, TagVO tag) {
        Object array = item.getExtValue("tag");

        if (Objects.isNull(array)) {
            item.setExtValue("tag", JSON.toJSON(Collections.singletonList(tag)));
        } else {
            List<TagVO> list = JSON.parseArray(array.toString(), TagVO.class);
            list.add(tag);
            item.setExtValue("tag", JSON.toJSON(list));
        }

    }

    private void putDrawPrize(Map<String, DrawPoolItemDO> giftMap, DrawPoolItemDO item) {
        if (Objects.isNull(item) || Objects.isNull(item.getId())) {
            return;
        }

        DrawPoolItemDO giftItem = giftMap.get(item.getItemKey());
        if (Objects.isNull(giftItem)) {
            item.setTargetTimes(1);
            giftMap.put(item.getItemKey(), item);
        } else {
            giftItem.setTargetTimes(giftItem.getTargetTimes() + item.getItemNum());
        }
    }


    private DrawPoolItemDO doDraw(List<DrawPoolItemDO> list, DrawConfig config, DrawPoolDO drawPool) {

        Integer randomInt = RandomUtil.randomInt(drawPool.getPoolSize());

        if (!CollectionUtils.isEmpty(list)) {
            int limit = 0;
            for (DrawPoolItemDO item : list) {
                Double index = getProb(item.getExtData());
                limit += index * drawPool.getPoolSize();
                if (randomInt < limit) {
                    return item;
                }
            }
        }

        DrawPoolItemDO defaultPrize = drawPoolItemService.getByPoolCodeAndValue(drawPool.getPoolCode(), config.getDefaultPrizeKey());
        return defaultPrize;
    }

    private Double getProb(String extData) {
        if (!JSON.isValid(extData)) {
            return 0D;
        }

        Double prob = JSON.parseObject(extData).getDouble("prob");
        return prob;
    }

    /**
     * 设置用户当周抽奖次数
     *
     * @param userId
     * @param startDateOfWeek
     * @param times
     */
    private void setUserLuckyDrawWeekTimes(Long userId, String startDateOfWeek, Integer times) {
        if (startDateOfWeek == null) {
            startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        }
        String key = String.format(SAKA_WEEK_LUCKY_DRAW_TIMES, startDateOfWeek);
        redisManager.hincr(key, userId.toString(), times, DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取用户指定周的抽奖次数
     *
     * @param userId
     * @param startDateOfWeek
     * @return
     */
    private Integer getUserLuckyDrawWeekTimes(Long userId, String startDateOfWeek) {
        if (startDateOfWeek == null) {
            startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        }
        String key = String.format(SAKA_WEEK_LUCKY_DRAW_TIMES, startDateOfWeek);
        Object value = redisManager.hget(key, userId.toString());
        if (value == null) {
            return 0;
        }
        double times = Double.parseDouble(value.toString());
        return (int) times;
    }

    /**
     * 用户领取记录
     *
     * @param userId
     * @param startDateOfWeek
     * @param code
     */
    private void setUserWeekReceiveRecord(Long userId, String startDateOfWeek, String code) {
        if (startDateOfWeek == null) {
            startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        }
        String key = String.format(SAKA_WEEK_LUCKY_DRAW_RECEIVE_RECORD, startDateOfWeek, userId);
        // 重试一次
        if (!redisManager.hset(key, code, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND)) {
            redisManager.hset(key, code, System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
        }
    }

    /**
     * 校验用户是否领取过某奖励
     *
     * @param userId
     * @param startDateOfWeek
     * @param code
     * @return
     */
    private Boolean checkUserWeekReceiveRecord(Long userId, String startDateOfWeek, String code) {
        if (startDateOfWeek == null) {
            startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        }
        String key = String.format(SAKA_WEEK_LUCKY_DRAW_RECEIVE_RECORD, startDateOfWeek, userId);
        return redisManager.hHasKey(key, code);
    }

    /**
     * 获取用户每周领取记录
     *
     * @param userId
     * @param startDateOfWeek
     * @return
     */
    private Map<String, Long> getUserWeekReceiveRecord(Long userId, String startDateOfWeek) {
        if (startDateOfWeek == null) {
            startDateOfWeek = DateUtil.getStartDateOfWeek(new Date());
        }
        String key = String.format(SAKA_WEEK_LUCKY_DRAW_RECEIVE_RECORD, startDateOfWeek, userId);
        Map<Object, Object> map = redisManager.hmget(key);
        if (map == null || map.size() <= 0) {
            return new HashMap<>(0);
        }
        HashMap<String, Long> result = new HashMap<>(map.size());
        for (Map.Entry<Object, Object> entry : map.entrySet()) {
            result.put(entry.getKey().toString(), Long.valueOf(entry.getValue().toString()));
        }
        return result;
    }

}
