package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 天生羁绊排行榜类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:18 2025/4/22
 */
@Slf4j
@Service
public class EmotionalBondsRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;
    @Resource
    private EmotionalBondsTrackManager emotionalBondsTrackManager;

    @Override
    protected void postProcess(RankContext rankContext) {
        // 后置处理
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        // 前置处理
    }

    /**
     * 发放上一周排行榜奖励
     * @param rankContext 排行榜上下文
     * @return 是否发放成功
     */
    @Override
    public Boolean sendPrize(RankContext rankContext) {
        // 获取排行榜奖励配置
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(
                ServicesAppIdEnum.lanling.getAppId(),
                EmotionalBondsConstant.ACTIVITY_CODE,
                "rank"
        );
        log.info("获取排行榜奖励配置: {}", JSON.toJSONString(scenePrizeDOList));

        if (CollectionUtils.isEmpty(scenePrizeDOList)) {
            log.warn("排行榜奖励配置为空");
            return Boolean.FALSE;
        }

        // 获取上一周的排行榜数据
        String lastWeekRankKey = emotionalBondsRedisManager.getLastWeekRankKey();
        RankVO rankVO = this.getRank(RankContext.builder()
                .activityCode(EmotionalBondsConstant.ACTIVITY_CODE)
                .rankKey(lastWeekRankKey)
                .rankLen(30L) // 只获取前10名
                .type(RankContext.RankType.cp) // CP类型排行榜
                .build());

        log.info("获取上一周排行榜数据: {}", rankVO);
        if (rankVO == null) {
            log.warn("上一周排行榜数据为空");
            return Boolean.FALSE;
        }

        // 获取CP排行榜列表
        List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
        if (CollectionUtils.isEmpty(cpRankItemList)) {
            log.warn("上一周CP排行榜列表为空");
            return Boolean.FALSE;
        }

        // 按排名分组奖励配置
        Map<Long, List<ScenePrizeDO>> rankPrizeMap = scenePrizeDOList.stream()
                .filter(prize -> prize.getPrizeBelongToRank() != null && prize.getPrizeBelongToRank() > 0)
                .collect(Collectors.groupingBy(ScenePrizeDO::getPrizeBelongToRank));

        // 遍历CP排行榜，发放奖励
        for (CpRankItem cpRankItem : cpRankItemList) {
            Long rank = cpRankItem.getRank();

            // 获取对应排名的奖励
            List<ScenePrizeDO> prizes = rankPrizeMap.get(rank);
            if (CollectionUtils.isEmpty(prizes)) {
                log.info("排名 {} 没有配置奖励", rank);
                continue;
            }

            // 发放奖励给CP双方
            Long userId1 = cpRankItem.getMaleUid();
            Long userId2 = cpRankItem.getFemaleUid();

            // 发放奖励给用户1
            if (userId1 != null) {
                sendPrizeToUser(userId1, rank, prizes);
            }

            // 发放奖励给用户2
            if (userId2 != null) {
                sendPrizeToUser(userId2, rank, prizes);
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 给指定用户发放奖励
     * @param userId 用户ID
     * @param rank 排名
     * @param prizes 奖励列表
     */
    private void sendPrizeToUser(Long userId, Long rank, List<ScenePrizeDO> prizes) {
        try {
            // 发放奖励
            sendPrizeManager.sendPrize(
                    BaseParam.builder()
                            .appId(ServicesAppIdEnum.lanling.getAppId())
                            .unionId(ServicesAppIdEnum.lanling.getUnionId())
                            .uid(userId)
                            .build(),
                    prizes.stream().map(prize -> cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO.of(prize, userId)).collect(Collectors.toList())
            );

            // 发送通知消息
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    userId,
                    String.format("恭喜在「天生羁绊」活动中，上周排名在%s名，奖励已经下发，快去查看吧～", rank)
            );
            //埋点
            for (ScenePrizeDO prize : prizes) {
                emotionalBondsTrackManager.allActivityReceiveAward("emotinal_rank", prize.getPrizeValue(), prize.getPrizeValueGold(), prize.getPrizeNum(), userId);
            }
            log.info("成功给用户 {} 发放排名 {} 的奖励", userId, rank);
        } catch (Exception e) {
            log.error("给用户 {} 发放排名 {} 的奖励失败: {}", userId, rank, e.getMessage(), e);
        }
    }
}
