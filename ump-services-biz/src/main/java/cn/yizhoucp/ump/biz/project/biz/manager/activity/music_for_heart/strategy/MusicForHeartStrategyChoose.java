package cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.strategy;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.common.MusicForHeartEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.StrategyManager;
import org.springframework.stereotype.Component;


/**
 * 随悦而动策略选择类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:07 2025/4/7
 */
@Component
public class MusicForHeartStrategyChoose extends StrategyManager<MusicForHeartEnums.ButtonEnum, ExecutableStrategy> {
}
