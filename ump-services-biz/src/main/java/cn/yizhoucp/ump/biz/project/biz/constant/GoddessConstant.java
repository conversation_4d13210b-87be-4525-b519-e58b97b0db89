package cn.yizhoucp.ump.biz.project.biz.constant;

/**
 * 女神相关
 *
 * <AUTHOR>
 */
public class GoddessConstant {

    /** 女神培训报名-学习内容 */
    public static final String LEARN_CONTENT_LIST = "LEARN_CONTENT_LIST_%s_%s";

    /** 女神培训报名-群二维码 */
    public static final String WECHAT_QR_CODE_CACHE = "WECHAT_QR_CODE_CACHE_%s";

    /** 可用的女神培训活动代码 */
    public static final String AVAILABLE_TRAIN_CODE_CACHE = "AVAILABLE_TRAIN_CODE_CACHE_%s";

    /** 报名成功用户缓存（和活动绑定） set */
    public static final String SIGN_UP_SUCCESS_CACHE = "SIGN_UP_SUCCESS_CACHE_%s";

    /** 报名最后一天刷新缓存标识 */
    public static final String SIGN_UP_LAST_DAY_FLAG_CACHE = "SIGN_UP_LAST_DAY_FLAG_CACHE_%s";

    /** 报名开启刷新缓存标识 */
    public static final String SIGN_UP_START_FLAG_CACHE = "SIGN_UP_START_FLAG_CACHE_%s";

    /** 女神预约缓存 */
    public static final String SIGN_UP_APPOINTMENT_CACHE = "SIGN_UP_APPOINTMENT_CACHE_%s";

    /** 上一次培训优秀女神缓存 */
    public static final String EXCELLENT_GODDESS_UID_CACHE = "EXCELLENT_GODDESS_UID_CACHE_%s";

    /** 培训优秀女神报名人数缓存 */
    public static final String EXCELLENT_GODDESS_COUNT_CACHE = "EXCELLENT_GODDESS_COUNT_CACHE_%s";

    /** 预约弹窗不弹出用户缓存 */
    public static final String APPOINTMENT_NOT_POP_CACHE = "APPOINTMENT_NOT_POP_CACHE_%s";

    /** 判断预约用户是否为第一周 */
    public static final String APPOINTMENT_USER_BUOY = "APPOINTMENT_USER_BUOY_%s";

}
