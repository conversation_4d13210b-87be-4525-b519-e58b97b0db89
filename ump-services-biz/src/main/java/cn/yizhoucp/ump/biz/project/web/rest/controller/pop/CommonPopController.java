package cn.yizhoucp.ump.biz.project.web.rest.controller.pop;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 弹窗
 *
 * @author: lianghu
 */
@RequestMapping("/api/inner/common-pop")
@RestController
public class CommonPopController {

    @Resource
    private PopManager popManager;

    @GetMapping("/pop-by-code")
    private Result<Boolean> popByCode(BaseParam param, String code) {
        return Result.successResult(popManager.popByCode(param.getAppId(), param.getUnionId(), param.getUid(), code));
    }

}
