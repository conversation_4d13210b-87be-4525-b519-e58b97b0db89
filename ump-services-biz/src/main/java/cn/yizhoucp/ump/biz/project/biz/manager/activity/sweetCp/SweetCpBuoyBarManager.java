package cn.yizhoucp.ump.biz.project.biz.manager.activity.sweetCp;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.buoyBar.ChatBarVO;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.BuoyBarManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;


@Component
public class SweetCpBuoyBarManager implements BuoyBarManager {

    @Resource
    private ActivityStatusManager activityStatusManager;
    @Resource
    private SweetCpBizManager sweetCpBizManager;

    @Override
    public ChatBarVO getBuoyBar(BaseParam param, Long toUid) {
        if (Objects.isNull(param.getUid()) || Objects.isNull(toUid)) {
            return ChatBarVO.builder().isShow(Boolean.FALSE).build();
        }

        Boolean enable = activityStatusManager.activityIsEnable(param, sweetCpBizManager.getActivityCode());
        if (!Boolean.TRUE.equals(enable)) {
            return ChatBarVO.builder().isShow(Boolean.FALSE).build();
        }

        return ChatBarVO.builder()
                .isShow(Boolean.TRUE)
                .icon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-02/1675404409161349.png")
                .href(sweetCpBizManager.getActivityUrl(param, "buoyBar") + "&toUid=" + toUid)
                .value(Optional.ofNullable(sweetCpBizManager.getSweetVal(param.getUid(), toUid)).orElse(0L).intValue())
                .maxValue(Optional.ofNullable(sweetCpBizManager.getStageVal(param.getUid(), toUid)).orElse(0L).intValue())
                .toUid(toUid)
                .valueName("甜蜜值")
                .build();
    }
}
