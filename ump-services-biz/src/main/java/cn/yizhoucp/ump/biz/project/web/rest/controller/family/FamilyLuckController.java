package cn.yizhoucp.ump.biz.project.web.rest.controller.family;

import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.SakaFamilyDrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.family.FamilyLuckManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> pepper
 * @Classname FamilyLuckController
 * @Description
 * @Date 2022/7/20 20:23
 */
@RestController
public class FamilyLuckController {

    @Resource
    private FamilyLuckManager familyLuckManager;

    @Resource
    private SakaFamilyDrawManager sakaFamilyDrawManager;

    @GetMapping("/api/inner/family/draw-luck-value")
    public Result<Integer> getFamilyLuckValue() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> familyLuckManager.getFamilyLuckValue());
    }

    @GetMapping("/api/inner/luck/reward-receive")
    public Result<CommonResultVO> rewardReceive(String code) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
            SecurityUser user = SecurityUtils.getCurrentUser();
            return sakaFamilyDrawManager.rewardReceive(code, user.getAppId(), user.getUserId());
        });
    }
}
