package cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkHistoryCaptainVO;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkIndexVO;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkRankVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.internal.LegendaryStarParkRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LegendaryStarParkIndexManager implements IndexManager {

    @Resource
    private LegendaryStarParkRedisManager legendaryStarParkRedisManager;

    @Resource
    private DrawPoolItemJpaDAO drawPoolItemJpaDAO;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private LegendaryStarParkRankManager legendaryStarParkRankManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private LegendaryStarParkTrackManager legendaryStarParkTrackManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private LegendaryStarParkDrawManager legendaryStarParkDrawManager;


    @Override
    public LegendaryStarParkIndexVO getIndex(BaseParam param, Long roomId, String extData) {
        LegendaryStarParkIndexVO indexVO = new LegendaryStarParkIndexVO();
        indexVO.setDrawItem(getDrawItem(param));
        indexVO.setGiftPools(buildGiftPool(param, roomId));
        indexVO.setDailyTasks(buildDailyTasks(param, roomId));
        return indexVO;
    }

    private List<LegendaryStarParkIndexVO.DailyTask> buildDailyTasks(BaseParam param, Long roomId) {
        List<LegendaryStarParkIndexVO.DailyTask> dailyTasks = new ArrayList<>();
        for (LegendaryStarParkEnums.DailyTaskEnum taskEnum : LegendaryStarParkEnums.DailyTaskEnum.values()) {
            LegendaryStarParkIndexVO.DailyTask dailyTask = new LegendaryStarParkIndexVO.DailyTask();
            dailyTask.setTaskId(taskEnum.getTaskId());
            dailyTask.setRewardNum(taskEnum.getRewardNum());
            Long progress = legendaryStarParkRedisManager.getDailyTaskProgress(param.getUid(), taskEnum.name(), taskEnum.getTaskKey());
            dailyTask.setCurrentProgress(progress > taskEnum.getTaskNum() ? taskEnum.getTaskNum() : progress);
            dailyTask.setTaskTitle(taskEnum.getTaskTitle());
            dailyTask.setStatus(getTaskStatus(param, taskEnum));
            dailyTask.setTotalGoal(taskEnum.getTaskNum());
            dailyTasks.add(dailyTask);
        }
        return dailyTasks;
    }

    private Integer getTaskStatus(BaseParam param, LegendaryStarParkEnums.DailyTaskEnum taskEnum) {
        int status = 0;
        Long progress = legendaryStarParkRedisManager.getDailyTaskProgress(param.getUid(), taskEnum.name(), taskEnum.getTaskKey());
        if (progress >= taskEnum.getTaskNum()) {
            status = 1;
        }
        Boolean claimed = legendaryStarParkRedisManager.getDailyTaskClaimed(param.getUid(), taskEnum.name());
        return Boolean.TRUE.equals(claimed) ? 2 : status;
    }

    private List<LegendaryStarParkIndexVO.GiftPool> buildGiftPool(BaseParam param, Long roomId) {
        List<LegendaryStarParkIndexVO.GiftPool> giftPools = new ArrayList<>();
        for (LegendaryStarParkEnums.LegendaryStarParkPoolType poolType : LegendaryStarParkEnums.LegendaryStarParkPoolType.values()) {
            LegendaryStarParkIndexVO.GiftPool giftPool = new LegendaryStarParkIndexVO.GiftPool();
            giftPool.setPoolId(poolType.getId());
            giftPool.setPoolCode(poolType.getPoolCode());
            giftPool.setCaptainInfo(buildCaptainInfo(param, roomId, poolType.getPoolCode()));
            Integer roomGloryValue = getRoomGloryValue(roomId, poolType.getPoolCode());
            giftPool.setRoomGloryValue(roomGloryValue);
            giftPool.setMaxRoomGloryValue(poolType.getGloryMomentDrawStarNumber());
            giftPool.setBrodCast(legendaryStarParkRedisManager.getBrodCast(poolType.getPoolCode()));
            giftPool.setPoolGifts(buildPoolGifts(poolType.getPoolCode()));
            giftPool.setGiftsCollect(buildGiftCollect(param, roomId, poolType.getPoolCode()));
            giftPool.setGloryMoment(getGloryMoment(roomId, poolType.getPoolCode()));
            giftPools.add(giftPool);
        }
        return giftPools;
    }

    private LegendaryStarParkIndexVO.GiftPool.GloryMoment getGloryMoment(Long roomId, String poolCode) {
        LegendaryStarParkIndexVO.GiftPool.GloryMoment gloryMoment = new LegendaryStarParkIndexVO.GiftPool.GloryMoment();
        Long actionTime = legendaryStarParkRedisManager.getGloryMoment(roomId, poolCode);
        if (actionTime != null) {
            gloryMoment.setIsActive(Boolean.TRUE);
            gloryMoment.setStartTime(actionTime.toString());
            Long endTime = actionTime + LegendaryStarParkConstant.GLORY_MOMENT_TIME * 1000L;
            gloryMoment.setEndTime(endTime.toString());
        } else {
            gloryMoment.setIsActive(Boolean.FALSE);
        }
        return gloryMoment;
    }

    private LegendaryStarParkIndexVO.GiftPool.GiftCollect buildGiftCollect(BaseParam param, Long roomId, String poolCode) {
        LegendaryStarParkIndexVO.GiftPool.GiftCollect giftCollect = new LegendaryStarParkIndexVO.GiftPool.GiftCollect();
        giftCollect.setCollectTasks(buildCollectTasks(param, poolCode));
        giftCollect.setRewardList(buildRewardList(param, poolCode));
        giftCollect.setStatus(getAllCollectedTaskStatus(param, poolCode));
        return giftCollect;
    }

    private Integer getAllCollectedTaskStatus(BaseParam param, String poolCode) {
        Integer status = 1;
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LegendaryStarParkConstant.ACTIVITY_CODE, poolCode);
        Map<Integer, Integer> scenePrizeDOMap = new HashMap<>();
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
            String config = scenePrizeDO.getExtData();
            Integer taskId = Integer.valueOf(parseFromJson(config, "taskId"));
            Integer goal = Integer.valueOf(parseFromJson(config, "goal"));
            scenePrizeDOMap.put(taskId, goal);
        }
        for (LegendaryStarParkEnums.CollectedTaskEnum taskEnum : LegendaryStarParkEnums.CollectedTaskEnum.values()) {
            Integer progress = legendaryStarParkRedisManager.getCollectedTaskProgress(param.getUid(), taskEnum.getTaskId(), poolCode);
            if (progress < scenePrizeDOMap.get(taskEnum.getTaskId())) {
                status = 0;
            }
        }
        Boolean claimed = legendaryStarParkRedisManager.getCollectedTaskClaimed(param.getUid(), poolCode);
        return Boolean.TRUE.equals(claimed) ? 2 : status;
    }

    private List<LegendaryStarParkIndexVO.GiftPool.PoolGift> buildRewardList(BaseParam param, String poolCode) {
        List<LegendaryStarParkIndexVO.GiftPool.PoolGift> rewardList = new ArrayList<>();
        List<ScenePrizeDO> scenePrizes = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LegendaryStarParkConstant.ACTIVITY_CODE, poolCode + "_" + "reward");
        for (ScenePrizeDO scenePrizeDO : scenePrizes) {
            LegendaryStarParkIndexVO.GiftPool.PoolGift poolGift = new LegendaryStarParkIndexVO.GiftPool.PoolGift();
            poolGift.setGiftIcon(scenePrizeDO.getPrizeIcon());
            poolGift.setGiftName(scenePrizeDO.getPrizeDesc());
            poolGift.setGiftValue(scenePrizeDO.getPrizeValueGold());
            poolGift.setGiftKey(scenePrizeDO.getPrizeValue());
            poolGift.setValidTime(scenePrizeDO.getPrizeEffectiveDay());
            rewardList.add(poolGift);
        }
        return rewardList;
    }

    private List<LegendaryStarParkIndexVO.GiftPool.GiftCollect.CollectTask> buildCollectTasks(BaseParam param, String poolCode) {
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LegendaryStarParkConstant.ACTIVITY_CODE, poolCode);
        if (scenePrizeDOS == null || scenePrizeDOS.isEmpty()) {
            return CollUtil.newArrayList();
        }
        List<LegendaryStarParkIndexVO.GiftPool.GiftCollect.CollectTask> collect = new ArrayList<>();
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
            LegendaryStarParkIndexVO.GiftPool.GiftCollect.CollectTask collectTask = new LegendaryStarParkIndexVO.GiftPool.GiftCollect.CollectTask();
            String config = scenePrizeDO.getExtData();
            Integer taskId = Integer.valueOf(parseFromJson(config, "taskId"));
            Integer goal = Integer.valueOf(parseFromJson(config, "goal"));
            collectTask.setTaskId(taskId);
            Integer progress = legendaryStarParkRedisManager.getCollectedTaskProgress(param.getUid(), taskId, poolCode);
            collectTask.setGiftName(scenePrizeDO.getPrizeDesc());
            collectTask.setGoal(goal);
            collectTask.setGiftIcon(scenePrizeDO.getPrizeIcon());
            collectTask.setProgress(progress);
            collectTask.setStatus(getCollectedTaskStatus(param, taskId, goal, poolCode));
            collect.add(collectTask);
        }
        return collect;
    }

    private Integer getCollectedTaskStatus(BaseParam param, Integer taskId, Integer goal, String poolCode) {
        int status = 0;
        Integer progress = legendaryStarParkRedisManager.getCollectedTaskProgress(param.getUid(), taskId, poolCode);
        if (progress >= goal) {
            status = 1;
        }
/*
        Boolean claimed = legendaryStarParkRedisManager.getCollectedTaskClaimed(param.getUid(), taskId.toString(), poolCode);
*/
        return status;
    }


    private String parseFromJson(String config, String taskId) {
        JSONObject jsonObject = JSON.parseObject(config);
        String value = jsonObject.getString(taskId);
        return Optional.ofNullable(value).orElse("");
    }

    private List<LegendaryStarParkIndexVO.GiftPool.PoolGift> buildPoolGifts(String poolCode) {
        List<LegendaryStarParkIndexVO.GiftPool.PoolGift> poolGifts = new ArrayList<>();
        List<DrawPoolItemDO> drawPoolItems = drawPoolItemJpaDAO.getByPoolCode(poolCode);
        for (DrawPoolItemDO drawPoolItem : drawPoolItems) {
            LegendaryStarParkIndexVO.GiftPool.PoolGift poolGift = new LegendaryStarParkIndexVO.GiftPool.PoolGift();
            poolGift.setGiftIcon(drawPoolItem.getItemIcon());
            poolGift.setGiftName(drawPoolItem.getItemName());
            poolGift.setGiftValue(drawPoolItem.getItemValueGold());
            poolGift.setGiftKey(drawPoolItem.getItemKey());
            poolGifts.add(poolGift);
        }
        return poolGifts;
    }

    private Integer getRoomGloryValue(Long roomId, String poolCode) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByGloryMomentPoolOrPoolCode(poolCode);
        if (poolType == null) {
            return LegendaryStarParkConstant.GLORY_MAX_VALUE_KEY;
        }
        Long gloryValue = legendaryStarParkRedisManager.getRoomDrawTimes(roomId, poolCode);
        return gloryValue.intValue() > poolType.getGloryMomentDrawStarNumber() ? poolType.getGloryMomentDrawStarNumber() : gloryValue.intValue();
    }

    private Integer getDrawItem(BaseParam param) {
        return legendaryStarParkRedisManager.getDrawItems(param.getUid()).intValue();
    }

    private LegendaryStarParkIndexVO.CaptainInfo buildCaptainInfo(BaseParam param, Long roomId, String poolCode) {
        LegendaryStarParkIndexVO.CaptainInfo captainInfo = new LegendaryStarParkIndexVO.CaptainInfo();
        JSONObject captainInfoJson = legendaryStarParkRedisManager.getCaptainInfo(roomId, poolCode);
        Long captainId = captainInfoJson.getLong("captainId");
        String tenure = captainInfoJson.getString("tenure");
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), captainId);
        if (userVO == null) {
            return initCaptainInfo(poolCode, roomId);
        }
        captainInfo.setId(captainId);
        captainInfo.setAvatar(userVO.getAvatar());
        captainInfo.setName(userVO.getName());
        captainInfo.setTenure(tenure);
        captainInfo.setTotalRewards(calGoldReward(tenure, poolCode));
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        captainInfo.setRewardName(poolType.getTargetItemName());
        return captainInfo;
    }

    private LegendaryStarParkIndexVO.CaptainInfo initCaptainInfo(String poolCode, Long roomId) {
        LegendaryStarParkIndexVO.CaptainInfo captainInfo = new LegendaryStarParkIndexVO.CaptainInfo();
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        captainInfo.setName("");
        captainInfo.setId(0L);
        captainInfo.setAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
        captainInfo.setTenure("0");
        captainInfo.setTotalRewards(0L);
        captainInfo.setRewardName(poolType.getTargetItemName());
        return captainInfo;
    }

    public Long calGoldReward(String tenure, String poolCode) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        if (poolType == null || tenure == null) {
            return 0L;
        }
        Instant timestampInstant = Instant.ofEpochMilli(Long.parseLong(tenure));
        Instant now = Instant.now();
        Duration duration = Duration.between(timestampInstant, now);
        Long rewardCoins = duration.toMinutes() * poolType.getRewardPerMinute();
        return rewardCoins > poolType.getMaxRewardCoin() ? poolType.getMaxRewardCoin() : rewardCoins;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return LegendaryStarParkConstant.ACTIVITY_CODE;
    }

    public LegendaryStarParkRankVO getRank(String rankKey) {
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(rankKey);
        if (poolType == null) {
            return null;
        }
        BaseParam param = BaseParam.ofMDC();
        RankVO rankVO = legendaryStarParkRankManager.getRank(RankContext.builder()
                .param(param)
                .activityCode(getActivityCode())
                .rankKey(legendaryStarParkRedisManager.getWeeklyKey(poolType.getPoolRankKey()))
                .rankLen(LegendaryStarParkConstant.RANK_LEN)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.user)
                .build());
        return buildLegendaryStarParkRankVO(rankVO, param.getUid(), poolType.getPoolCode());
    }


    private LegendaryStarParkRankVO buildLegendaryStarParkRankVO(RankVO rankVO, Long uid, String poolCode) {
        LegendaryStarParkRankVO.LotteryInfo lotteryInfo = buildLotteryInfo(uid, poolCode);
        LegendaryStarParkRankVO.MyselfRank myselfRank = buildMySelfRank(rankVO, poolCode);
        List<LegendaryStarParkRankVO.RankList> rankLists = buildRankList(rankVO, poolCode);
        return LegendaryStarParkRankVO.builder()
                .lotteryInfo(lotteryInfo)
                .myselfRank(myselfRank)
                .rankList(rankLists)
                .build();
    }

    private LegendaryStarParkRankVO.LotteryInfo buildLotteryInfo(Long uid, String poolCode) {
        LegendaryStarParkRankVO.LotteryInfo lotteryInfo = new LegendaryStarParkRankVO.LotteryInfo();
        lotteryInfo.setTotalDraws(LegendaryStarParkConstant.MAX_DRAW_TIMES);
        Long drawTimes = legendaryStarParkRedisManager.getDrawTimes(uid, poolCode);
        lotteryInfo.setCurrentDraws(drawTimes > LegendaryStarParkConstant.MAX_DRAW_TIMES ? LegendaryStarParkConstant.MAX_DRAW_TIMES : drawTimes);
        lotteryInfo.setTasks(buildTasks(uid, poolCode));
        return lotteryInfo;
    }

    private List<LegendaryStarParkRankVO.Task> buildTasks(Long uid, String poolCode) {
        List<LegendaryStarParkRankVO.Task> tasks = new ArrayList<>();
        List<ScenePrizeDO> scenePrizes = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), getActivityCode(), poolCode + "_" + "draw_times");
        Map<Long, ScenePrizeDO> map = scenePrizes.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeBelongToRank, scenePrizeDO -> scenePrizeDO));
        for (LegendaryStarParkEnums.LegendaryStarParkDrawTimesTask task : LegendaryStarParkEnums.LegendaryStarParkDrawTimesTask.values()) {
            LegendaryStarParkRankVO.Task taskVO = new LegendaryStarParkRankVO.Task();
            ScenePrizeDO prizeDO = map.get(task.getTaskId().longValue());
            if (prizeDO == null) {
                continue;
            }
            taskVO.setGiftInfo(buildGiftInfo(prizeDO));
            taskVO.setRequiredDraws(prizeDO.getNeedCount());
            taskVO.setIsRewardClaimed(getClaimedDrawTimesTaskClaimed(uid, poolCode, task.getTaskId(), prizeDO.getNeedCount()));
            taskVO.setTaskId(task.getTaskId());
            tasks.add(taskVO);
        }
        return tasks;
    }

    private Long getClaimedDrawTimesTaskClaimed(Long uid, String poolCode, Integer taskId, Integer needCount) {
        long status = 0L;
        Long drawTimes = legendaryStarParkRedisManager.getDrawTimes(uid, poolCode);
        if (drawTimes >= needCount) {
            status = 1L;
        }
        Boolean claimed = legendaryStarParkRedisManager.isDrawTimesTaskClaimed(uid, poolCode, taskId);
        return Boolean.TRUE.equals(claimed) ? 2L : status;
    }

    private LegendaryStarParkRankVO.GiftInfo buildGiftInfo(ScenePrizeDO scenePrizeDO) {
        if (scenePrizeDO == null) {
            return LegendaryStarParkRankVO.GiftInfo.builder().build();
        }
        LegendaryStarParkRankVO.GiftInfo giftInfo = new LegendaryStarParkRankVO.GiftInfo();
        giftInfo.setGiftIcon(scenePrizeDO.getPrizeIcon());
        giftInfo.setGiftName(scenePrizeDO.getPrizeDesc());
        giftInfo.setGiftValue(scenePrizeDO.getPrizeValueGold());
        return giftInfo;
    }

    private List<LegendaryStarParkRankVO.RankList> buildRankList(RankVO rankVO, String poolCode) {
        List<LegendaryStarParkRankVO.RankList> rankList = new ArrayList<>();
        List<RankItem> rankVOList = rankVO.getRankList();
        for (RankItem rankItem : rankVOList) {
            LegendaryStarParkRankVO.RankList rankListItem = LegendaryStarParkRankVO.RankList.builder().build();
            rankListItem.setRank(rankItem.getRank());
            rankListItem.setName(rankItem.getName());
            rankListItem.setIcon(rankItem.getIcon());
            rankListItem.setValue(rankItem.getValue());
            rankListItem.setDrawnGiftList(buildDrawnGiftList(rankItem.getId(), poolCode));
            rankListItem.setId(rankItem.getId());
            rankList.add(rankListItem);
        }
        return rankList;
    }

    private LegendaryStarParkRankVO.MyselfRank buildMySelfRank(RankVO rankVO, String poolCode) {
        LegendaryStarParkRankVO.MyselfRank myselfRank = new LegendaryStarParkRankVO.MyselfRank();
        RankItem myselfRankInfo = rankVO.getMyselfRank();
        myselfRank.setRank(myselfRankInfo.getRank());
        myselfRank.setName(myselfRankInfo.getName());
        myselfRank.setIcon(myselfRankInfo.getIcon());
        myselfRank.setValue(myselfRankInfo.getValue());
        myselfRank.setCurrentCheckpoint(0L);
        myselfRank.setDrawnGiftList(buildDrawnGiftList(myselfRankInfo.getId(), poolCode));
        myselfRank.setId(myselfRank.getId());
        return myselfRank;
    }

    private List<LegendaryStarParkRankVO.MyselfRankDrawnGiftList> buildDrawnGiftList(Long id, String poolCode) {
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemJpaDAO.getByPoolCode(poolCode);
        if (CollUtil.isEmpty(drawPoolItemDOList)) {
            return null;
        }
        List<LegendaryStarParkRankVO.MyselfRankDrawnGiftList> drawnGiftList = new ArrayList<>();
        for (DrawPoolItemDO drawPoolItemDO : drawPoolItemDOList) {
            Long drawPoolGiftTimes = legendaryStarParkRedisManager.getDrawPoolGiftItems(id, poolCode, drawPoolItemDO.getItemKey());
            if (drawPoolGiftTimes == 0) {
                continue;
            }
            LegendaryStarParkRankVO.MyselfRankDrawnGiftList drawnGiftListItem = new LegendaryStarParkRankVO.MyselfRankDrawnGiftList();
            drawnGiftListItem.setDrawnCount(drawPoolGiftTimes);
            drawnGiftListItem.setGiftIcon(drawPoolItemDO.getItemIcon());
            drawnGiftListItem.setGiftValue(drawPoolItemDO.getItemValueGold());
            drawnGiftListItem.setGiftName(drawPoolItemDO.getItemName());
            drawnGiftList.add(drawnGiftListItem);
        }
        return drawnGiftList;
    }

    @NoRepeatSubmit(time = 3)
    public Boolean getClaimedReward(String sceneCode, String taskId, String extData) {
        //奖池收集任务
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(sceneCode);
        if (poolType != null) {
            //下发奖池礼物
            distributeRewards(poolType);
        }
        if (LegendaryStarParkConstant.DRAW_TIMES_SCENE_CODE.equals(sceneCode)) {
            LegendaryStarParkEnums.LegendaryStarParkDrawTimesTask drawTimesTask = LegendaryStarParkEnums.LegendaryStarParkDrawTimesTask.getTaskByTaskId(taskId);
            if (drawTimesTask != null) {
                //下发抽奖次数奖励
                distributeDrawTimesRewards(drawTimesTask, extData);
            }
        }
        if (LegendaryStarParkConstant.DAILY_SCENE_CODE.equals(sceneCode)) {
            LegendaryStarParkEnums.DailyTaskEnum dailyTaskEnum = LegendaryStarParkEnums.DailyTaskEnum.getTaskById(taskId);
            if (dailyTaskEnum != null) {
                distributeDailyTaskRewards(dailyTaskEnum);
            }
        }

        return Boolean.TRUE;
    }

    private void distributeDailyTaskRewards(LegendaryStarParkEnums.DailyTaskEnum dailyTaskEnum) {
        BaseParam param = BaseParam.ofMDC();
        legendaryStarParkRedisManager.incrementDrawItems(param.getUid(), dailyTaskEnum.getRewardNum());
        legendaryStarParkRedisManager.setDailyTaskClaimed(param.getUid(), dailyTaskEnum.name());
    }

    private void distributeDrawTimesRewards(LegendaryStarParkEnums.LegendaryStarParkDrawTimesTask drawTimesTask, String extData) {
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), getActivityCode(), extData + "_" + "draw_times");
        if (CollUtil.isEmpty(scenePrizeDOList)) {
            return;
        }
        List<ScenePrizeDO> prizeList = scenePrizeDOList.stream().filter(item -> {
            Long taskId = item.getPrizeBelongToRank();
            return drawTimesTask.getTaskId().equals(taskId.intValue());
        }).collect(Collectors.toList());
        BaseParam param = BaseParam.ofMDC();
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(param.getUid()).build(),
                prizeList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
        );
        legendaryStarParkRedisManager.setDrawTimesTaskClaimed(param.getUid(), extData, drawTimesTask.getTaskId());
        for (ScenePrizeDO scenePrizeDO : prizeList) {
            legendaryStarParkTrackManager.allActivityReceiveAward(LegendaryStarParkConstant.STAR_PARADISE, scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), param.getUid());
        }
    }

    private void distributeRewards(LegendaryStarParkEnums.LegendaryStarParkPoolType poolType) {
        Integer status = getAllCollectedTaskStatus(BaseParam.ofMDC(), poolType.getPoolCode());
        if (status < 1) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "未完成任务");
        }
        //扣减道具
        decrementCollectedTaskItems(BaseParam.ofMDC(), poolType);
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), getActivityCode(), poolType.getPoolCode() + "_" + "reward");
        if (CollUtil.isEmpty(scenePrizeDOList)) {
            return;
        }
        BaseParam param = BaseParam.ofMDC();
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(param.getUid()).build(),
                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
        );
/*
        legendaryStarParkRedisManager.setCollectedTaskClaimed(param.getUid(), poolType.getPoolCode());
*/
        legendaryStarParkTrackManager.allActivityTaskFinish(param.getUid(), poolType.getCollectedTrackCode());
        sendCollectedTaskProgress(param.getUid(), scenePrizeDOList);
    }

    private void sendCollectedTaskProgress(Long uid, List<ScenePrizeDO> scenePrizeDOList) {
        if (CollUtil.isEmpty(scenePrizeDOList)) {
            return;
        }
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
            notifyComponent.npcNotify(
                    ServicesAppIdEnum.lanling.getUnionId(),
                    uid,
                    String.format("恭喜星乐园管理员在「传说中的星乐园」活动中，成功领取%s礼物，礼物已经下发，快去查看吧～", scenePrizeDO.getPrizeDesc())
            );
            legendaryStarParkTrackManager.allActivityReceiveAward(LegendaryStarParkConstant.GLORY_STAR_PARADISE, scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), uid);
        }
    }

    private void decrementCollectedTaskItems(BaseParam baseParam, LegendaryStarParkEnums.LegendaryStarParkPoolType poolType) {
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), LegendaryStarParkConstant.ACTIVITY_CODE, poolType.getPoolCode());
        Map<Integer, Integer> scenePrizeDOMap = new HashMap<>();
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOS) {
            String config = scenePrizeDO.getExtData();
            Integer taskId = Integer.valueOf(parseFromJson(config, "taskId"));
            Integer goal = Integer.valueOf(parseFromJson(config, "goal"));
            scenePrizeDOMap.put(taskId, goal);
        }
        for (LegendaryStarParkEnums.CollectedTaskEnum taskEnum : LegendaryStarParkEnums.CollectedTaskEnum.values()) {
            Integer goal = scenePrizeDOMap.get(taskEnum.getTaskId());
            if (goal == null) {
                goal = 0;
            }
            legendaryStarParkRedisManager.decrementCollectedTaskProgress(baseParam.getUid(), taskEnum.getTaskId(), poolType.getPoolCode(), goal.longValue());
        }
    }

    public LegendaryStarParkHistoryCaptainVO getCaptainsHistory(String poolCode, Long roomId) {
        LegendaryStarParkHistoryCaptainVO captainsHistory = new LegendaryStarParkHistoryCaptainVO();
        captainsHistory.setCurrentCaptain(buildCurrentCaptain(poolCode, roomId));
        captainsHistory.setHistoryCaptains(buildHistoryCaptains(poolCode, roomId));
        return captainsHistory;
    }

    private List<LegendaryStarParkIndexVO.CaptainInfo> buildHistoryCaptains(String poolCode, Long roomId) {
        List<LegendaryStarParkIndexVO.CaptainInfo> historyCaptainsList = new ArrayList<>();
        List<JSONObject> historyCaptains = legendaryStarParkRedisManager.getHistoryCaptains(poolCode, roomId);
        for (JSONObject captain : historyCaptains) {
            LegendaryStarParkIndexVO.CaptainInfo captainInfo = new LegendaryStarParkIndexVO.CaptainInfo();
            Long captainId = captain.getLong("captainId");
            String tenure = Optional.ofNullable(captain.getString("tenure")).orElse(String.valueOf(System.currentTimeMillis()));
            String endTime = Optional.ofNullable(captain.getString("endTime")).orElse(String.valueOf(System.currentTimeMillis()));
            String formattedDuration = formatDurationDifference(tenure, endTime);
            Long totalRewards = captain.getLong("rewardCoins");
            UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), captainId);
            if (userVO == null) {
                continue;
            }
            captainInfo.setId(captainId);
            captainInfo.setName(userVO.getName());
            captainInfo.setAvatar(userVO.getAvatar());
            captainInfo.setTotalRewards(totalRewards);
            captainInfo.setTenure(formattedDuration);
            historyCaptainsList.add(captainInfo);
        }
        return historyCaptainsList;
    }

    private String formatDurationDifference(String tenure, String endTime) {
        long difference = Long.parseLong(endTime) - Long.parseLong(tenure);
        Duration duration = Duration.ofMillis(difference);
        Long hours = duration.toHours();
        Long minutes = duration.toMinutes() % 60;
        Long seconds = duration.getSeconds() % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }


    private LegendaryStarParkIndexVO.CurrentCaptainInfo buildCurrentCaptain(String poolCode, Long roomId) {
        LegendaryStarParkIndexVO.CurrentCaptainInfo currentCaptainInfo = new LegendaryStarParkIndexVO.CurrentCaptainInfo();
        JSONObject captainInfo = legendaryStarParkRedisManager.getCaptainInfo(roomId, poolCode);
        Long captainId = captainInfo.getLong("captainId");
        String tenure = captainInfo.getString("tenure");
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), captainId);
        if (userVO == null) {
            return initCurrentCaptain(poolCode, roomId);
        }
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        currentCaptainInfo.setId(captainId);
        currentCaptainInfo.setAvatar(userVO.getAvatar());
        currentCaptainInfo.setName(userVO.getName());
        currentCaptainInfo.setRewards(calGoldReward(tenure, poolCode));
        currentCaptainInfo.setRewardName(poolType.getTargetItemName());
        currentCaptainInfo.setCoinsPerMinute(poolType.getRewardPerMinute());
        currentCaptainInfo.setMaxCoinsEarned(poolType.getMaxRewardCoin());
        currentCaptainInfo.setTenure(tenure);
        return currentCaptainInfo;
    }

    private LegendaryStarParkIndexVO.CurrentCaptainInfo initCurrentCaptain(String poolCode, Long roomId) {
        LegendaryStarParkIndexVO.CurrentCaptainInfo currentCaptainInfo = new LegendaryStarParkIndexVO.CurrentCaptainInfo();
        LegendaryStarParkEnums.LegendaryStarParkPoolType poolType = LegendaryStarParkEnums.LegendaryStarParkPoolType.getPoolTypeByPoolCode(poolCode);
        currentCaptainInfo.setId(0L);
        currentCaptainInfo.setTenure("0");
        currentCaptainInfo.setAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
        currentCaptainInfo.setRewardName(poolType.getTargetItemName());
        currentCaptainInfo.setMaxCoinsEarned(poolType.getMaxRewardCoin());
        currentCaptainInfo.setCoinsPerMinute(poolType.getRewardPerMinute());
        currentCaptainInfo.setRewards(0L);
        currentCaptainInfo.setName("");
        return currentCaptainInfo;
    }

    public Boolean sendCaptainReward() {
        for (LegendaryStarParkEnums.LegendaryStarParkPoolType poolType : LegendaryStarParkEnums.LegendaryStarParkPoolType.values()) {
            JSONObject result = legendaryStarParkRedisManager.getLastWeekCaptainInfo(poolType.getPoolCode());
            Long captainId = result.getLong("captainId");
            String tenure = result.getString("tenure");
            if (captainId == null) {
                continue;
            }
            Long rewardCoins = calGoldReward(tenure, poolType.getPoolCode());
            legendaryStarParkDrawManager.sendCoin(captainId, rewardCoins);
            log.info("sendCaptainReward: result {} captainId {}, tenure {}, rewardCoins {}", result, captainId, tenure, rewardCoins);
        }
        return Boolean.TRUE;
    }
}
