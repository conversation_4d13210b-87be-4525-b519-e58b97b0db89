package cn.yizhoucp.ump.biz.project.dto;

import lombok.Data;
import lombok.ToString;

/**
 * 规则引擎执行上下文
 * <AUTHOR>
 */
@Data
@ToString
public final class RuleContext {

  /**
   * 开始执行的瞬间 (cxt初始化瞬间)
   */
  private final long requestTime = System.currentTimeMillis();

  /** 关联id (例如活动 id) */
  private long relationId;

  /** 当前正在执行的节点 id */
  private long currentNodeId;

  /** 当前正在执行的节点的父节点 id */
  private long currentParentNodeId;

  /** 当前正在执行节点的后置节点 id */
  private long nextNodeId;

  /** 当前循环点 */
  private int currentLoop;

  public RuleContext(long relationId) {
    this.relationId = relationId;
  }
}
