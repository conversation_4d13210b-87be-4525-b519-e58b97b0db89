package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.OfficialCombineUserDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OfficialCombineUserJpaDAO extends JpaRepository<OfficialCombineUserDO, Long>, JpaSpecificationExecutor<OfficialCombineUserDO>, CrudRepository<OfficialCombineUserDO, Long> {

    @Query(value = "select * from official_combine_user where app_id = ?1 and status in ?2", nativeQuery = true)
    List<OfficialCombineUserDO> getByExpiredTime(Long appId, List<String> status);

    @Query(value = "select * from official_combine_user where app_id = ?1 and female_uid = ?2 and status in ?3", nativeQuery = true)
    List<OfficialCombineUserDO> getWomanRelation(Long appId,Long userId, List<String> status);

    @Query(value = "select * from official_combine_user where app_id = ?1 and male_uid = ?2 and status in ?3", nativeQuery = true)
    List<OfficialCombineUserDO> getManRelation(Long appId,Long userId, List<String> status);


    @Query(value = "select * from official_combine_user where page_index in ?1 order by create_time desc limit 500", nativeQuery = true)
    List<OfficialCombineUserDO> getByPageIndex(List<Integer> pageIndexList);

    @Query(value = "select count(id) from official_combine_user where order_id is not null ", nativeQuery = true)
    Integer findCountByOrderId();

    @Query(value = "select distinct room_id from official_combine_user where app_id = ?1 and status = ?2 and room_id in ?3", nativeQuery = true)
    List<Long> findListByRoomId(Long appId,String status, List<Long> roomIds);

    @Query(value = "select * from official_combine_user where id > ?1 limit ?2", nativeQuery = true)
    List<OfficialCombineUserDO> findByIdGreaterThanLimit(Long id, Integer limit);

    @Query(value = "select * from official_combine_user where male_uid in ?1 or female_uid in ?1", nativeQuery = true)
    List<OfficialCombineUserDO> findByUserIds(List<Long> userIds);

    @Query(value = "select * from official_combine_user where status in ?1 order by id limit ?2", nativeQuery = true)
    List<OfficialCombineUserDO> findListByStatus(List<String> statusList, Integer num);

    @Query(value = "select * from official_combine_user where id > ?1 and status in ?2 order by id limit ?3", nativeQuery = true)
    List<OfficialCombineUserDO> findListByIdAndStatus(Long id, List<String> statusList, Integer num);


    List<OfficialCombineUserDO> findByEndTimeGreaterThanAndStatus(LocalDateTime endTime, String status);

    @Query(value = "select * from official_combine_user where male_uid = ?1 and female_uid = ?2 and status = ?3 order by id asc limit 1", nativeQuery = true)
    OfficialCombineUserDO findByMaleUidAndFemaleUid(Long maleUid, Long femaleUid, String status);

    @Query(value = "select * from official_combine_user where male_uid = ?1 and status = ?2 order by id asc limit 1", nativeQuery = true)
    OfficialCombineUserDO findByMaleUidAndStatus(Long maleUid, String status);

    @Query(value = "select * from official_combine_user where female_uid = ?1 and status = ?2 order by id asc limit 1", nativeQuery = true)
    OfficialCombineUserDO findByFemaleUidAndStatus(Long femaleUid, String status);

}
