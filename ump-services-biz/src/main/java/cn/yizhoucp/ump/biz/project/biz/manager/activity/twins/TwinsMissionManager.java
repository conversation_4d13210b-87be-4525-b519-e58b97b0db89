package cn.yizhoucp.ump.biz.project.biz.manager.activity.twins;

import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.param.jimu.QueryMissionParam;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.project.biz.enums.MissionDistributeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.pop.IdempotentTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ActivityReportManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.DefaultMissionManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.MissionContext;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import cn.yizhoucp.ump.biz.project.biz.manager.pop.PopManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityMissionJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityMissionRecordJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.PrizeItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionRecordDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.LoginPopDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PrizeItemDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TwinsMissionManager extends DefaultMissionManager {

    @Resource
    private PrizeItemJpaDAO prizeItemJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ActivityMissionJpaDAO activityMissionJpaDAO;
    @Resource
    private RedisManager redisManager;
    @Resource
    @Lazy
    private TwinsPageManager twinsPageManager;
    @Resource
    private PopManager popManager;
    @Resource
    private ActivityReportManager activityReportManager;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    private ActivityMissionRecordJpaDAO activityMissionRecordJpaDAO;

    @Override
    protected Boolean doCheck(MissionContext context) {
        return super.doCheck(context);
    }

    @Override
    protected Boolean init(MissionContext context) {
        return Boolean.TRUE;
    }

    @Override
    protected Boolean completedProcess(MissionContext context) {
        MissionParam param = context.getMissionParam();
        // 获取任务信息
//        TwinsConstant.TaskCodeEnum taskEnum = TwinsConstant.TaskCodeEnum.valueOf(param.getTaskCode());
        ActivityMissionDO activityMissionDO = activityMissionJpaDAO.getTopByBelongActivityCodeAndCode(getActivityCode(), param.getTaskCode());
        if (activityMissionDO == null) {
            log.error("twins 任务空 context:{}", JSON.toJSONString(context));
            return false;
        }

        List<PrizeItemDO> prizeItemDOList = prizeItemJpaDAO.getByMissionId(activityMissionDO.getId());
        if (CollectionUtils.isEmpty(prizeItemDOList)) {
            log.error("twins 任务奖励空 context:{}", JSON.toJSONString(context));
            return false;
        }

        for (PrizeItemDO prizeItemDO : prizeItemDOList) {
            // 弹窗提示
            popManager.popByLoginPopDO(param.getAppId(), param.getUnionId(), param.getUid(), LoginPopDO.builder()
                    .unionId(param.getUnionId())
                    .idempotentType(IdempotentTypeEnum.NONE.name())
                    .pushType("h5dialog")
                    .url(twinsPageManager.getPopUrl(param.getBaseParam(), Lists.newArrayList(SendPrizeDTO.builder()
                            .prizeValue(prizeItemDO.getPrizeKey())
                            .prizeNum(prizeItemDO.getPrizeNum()).build())))
                    .build());
            // 埋点处理
            activityReportManager.missionCompletedReport(param.getBaseParam(), getActivityCode(), "receive_give_gift_constellation", activityMissionDO.getGiftKey(), prizeItemDO.getPrizeKey(), "operational_activity");
        }

        // 下发祈愿卡
        return this.sendPrize(context);
    }

    @Override
    public TaskVO getMissionList(QueryMissionParam param) {
        log.debug("twins 获取任务列表 param:{}", JSON.toJSONString(param));
        // 查询任务列表
        List<ActivityMissionDO> missionList = activityMissionJpaDAO.findAll(Example.of(ActivityMissionDO.builder().appId(param.getAppId()).belongActivityCode(getActivityCode()).status(CommonStatus.enable.getCode()).build()));
        missionList = missionList.stream().filter(activityMissionDO -> !"点亮奖励".equals(activityMissionDO.getActivityPlate())).collect(Collectors.toList());
        // 补充完成次数
        List<TaskItem> result = missionList.stream().map(m -> {
            if (m.getIcon() == null) {
                CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(param.getAppId(), m.getGiftKey()).successData();
                if (coinGiftProductVO != null) {
                    m.setIcon(coinGiftProductVO.getIcon());
                }
            }

            TaskItem taskItem = m.convert2TaskItem();
            taskItem.setMaxFinishTimes(m.getGiftCount());
            taskItem.setCurFinishTimes(getCurrTimes(param.getBaseParam(), m.getCode(), m.getGiftCount()));
            return taskItem;
        }).collect(Collectors.toList());
        return TaskVO.builder()
                .taskItemList(result)
                .build();
    }

    @Override
    public String getActivityCode() {
        return twinsPageManager.getActivityCode();
    }

    @Override
    public Boolean sendPrize(MissionContext context) {
        log.debug("context: {}", context);

        MissionParam missionParam = context.getMissionParam();
        if (missionParam == null) {
            return false;
        }
        JSONObject bizParam = missionParam.getBizParam();
        if (bizParam == null) {
            return false;
        }
        ActivityMissionDO activityMissionDO = (ActivityMissionDO) bizParam.get("missionConfig");
        if (activityMissionDO == null) {
            return false;
        }
        if (MissionDistributeTypeEnum.MANUAL.getCode().equals(activityMissionDO.getDistributeType())) {
            return false;
        }

        List<PrizeItemDO> prizeItemDOList = prizeItemJpaDAO.getByMissionId(activityMissionDO.getId());
        if (CollectionUtils.isEmpty(prizeItemDOList)) {
            return false;
        }
        context.setPrizeItemDOList(prizeItemDOList);
        ActivityMissionRecordDO activityMissionRecordDO = activityMissionRecordJpaDAO.getByUserIdAndMissionCodeAndDeadlineLimit1(missionParam.getUid(), activityMissionDO.getCode(), super.getMissionDeadline(activityMissionDO));
        if (activityMissionRecordDO == null) {
            return false;
        }
        if (activityMissionRecordDO.getPrizeSent() == 1) {
            return true;
        }

        activityMissionRecordDO.setPrizeSent(1);
        activityMissionRecordJpaDAO.save(activityMissionRecordDO);
        log.info("下发奖励 context: {}", context);

        // TODO 是否在背包展示支持可配置
        return sendPrizeManager.sendGift(missionParam.getAppId(), prizeItemDOList.get(0).getPrizeKey(), missionParam.getUid(), prizeItemDOList.get(0).getPrizeNum().longValue(), 365, getActivityCode(), Boolean.FALSE, Boolean.FALSE);
    }

    private Integer getCurrTimes(BaseParam param, String taskCode, Integer limit) {
        ActivityMissionRecordDO activityMissionRecordDO = activityMissionRecordJpaDAO.getByUserIdAndMissionCodeLimit1(param.getUid(), taskCode);
        if (activityMissionRecordDO == null || activityMissionRecordDO.getCurrentTimes() == null) {
            return 0;
        }

        return Math.min(limit, activityMissionRecordDO.getCurrentTimes().intValue());
    }

}
