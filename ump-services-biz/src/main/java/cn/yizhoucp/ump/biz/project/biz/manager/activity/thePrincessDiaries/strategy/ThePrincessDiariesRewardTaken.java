package cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.ThePrincessDiariesTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.common.ThePrincessDiariesRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.thePrincessDiaries.vo.ThePrincessDiariesRewardTakenResponse;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ThePrincessDiariesRewardTaken implements ExecutableStrategy {
    @Resource
    private ThePrincessDiariesRedisManager thePrincessDiariesRedisManager;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private ThePrincessDiariesTrackManager thePrincessDiariesTrackManager;
    @Resource
    private NotifyComponent notifyComponent;

    @NoRepeatSubmit(time = 3)
    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        Long toUid = buttonEventParam.getOtherId();
        //校验是否所有都点亮
        for (ThePrincessDiariesEnums.LightTaskEnum lightTaskEnum : ThePrincessDiariesEnums.LightTaskEnum.values()) {
            //校验是否已点亮
            Boolean isLit = thePrincessDiariesRedisManager.getLightTaskIsComplete(uid, toUid, lightTaskEnum.getLightTaskId());
            if (Boolean.FALSE.equals(isLit)) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "暂未达成领取条件哦～");
            }
        }
        //校验是否已领取
        Boolean isTaken = thePrincessDiariesRedisManager.getLightRewardClaimed(uid, toUid);
        if (Boolean.TRUE.equals(isTaken)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已经领取过啦～");
        }
        //领取奖励
        List<ScenePrizeDO> scenePrizeDOs = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), ThePrincessDiariesConstant.ACTIVITY_CODE, ThePrincessDiariesConstant.LIT_TASK_REWARD);
        if (CollUtil.isEmpty(scenePrizeDOs)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖励不存在");
        }
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
        );
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(toUid).build(),
                scenePrizeDOs.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, toUid)).collect(Collectors.toList())
        );

        thePrincessDiariesRedisManager.setLightRewardClaimed(uid, toUid);
        //埋点
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOs) {
            thePrincessDiariesTrackManager.allActivityReceiveAward("love_bags", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), uid);
            thePrincessDiariesTrackManager.allActivityReceiveAward("love_bags", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), toUid);
        }
        String msg = "恭喜在「公主日记」活动中，成功点亮全部公主华服，获取奖励；奖励已经全部下发，快去查看吧～";
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                toUid,
                msg
        );


        return ThePrincessDiariesRewardTakenResponse.builder()
                .rewardList(buildRewardList(scenePrizeDOs))
                .build();
    }

    private List<ThePrincessDiariesRewardTakenResponse.GiftInfo> buildRewardList(List<ScenePrizeDO> scenePrizeDOs) {
        if (CollUtil.isEmpty(scenePrizeDOs)) {
            return null;
        }
        return scenePrizeDOs.stream().map(scenePrizeDO -> ThePrincessDiariesRewardTakenResponse.GiftInfo.builder()
                .giftKey(scenePrizeDO.getPrizeValue())
                .giftName(scenePrizeDO.getPrizeDesc())
                .giftIcon(scenePrizeDO.getPrizeIcon())
                .giftValue(scenePrizeDO.getPrizeValueGold())
                .build()).collect(Collectors.toList());
    }
}
