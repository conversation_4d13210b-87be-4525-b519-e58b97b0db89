package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/6/12 16:10
 * @Version 1.0
 */
public class DragonConstant {

    public static final String ASSISTANCE_RANK = "ump:dragon:assistanceRank";

    /** 用户活动道具 redis-string(uid) */
    public static final String USER_ACTIVITY_CARD = "ump:dragon:userActivityCard_%s";

    @Getter
    @AllArgsConstructor
    public enum ActivityGiftEnum {
        YZGL_GIFT,
        FQMM_GIFT,
        ZYN_GIFT,
        QQJY_GIFT,
        ;

        public static DragonConstant.ActivityGiftEnum get(String name) {
            for (DragonConstant.ActivityGiftEnum gift : values()) {
                if (gift.name().equals(name)) {
                    return gift;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum ActivityCardEnum {

        MI(0, "MDJ_GIFT"),
        ZONG(1, "ZDJ_GIFT"),
        ZHI(2, "ZTDJ_GIFT"),
        LV(3, "LDJ_GIFT");

        private Integer index;
        private String limitKey;

        public static DragonConstant.ActivityCardEnum get(String name) {
            for (DragonConstant.ActivityCardEnum gift : values()) {
                if (gift.getLimitKey().equals(name)) {
                    return gift;
                }
            }
            return null;
        }
    }


    public static Boolean isExplosiveTime() {
        LocalDateTime localDateTime = ActivityTimeUtil.getNow(ActivityCheckListEnum.DRAGON.getCode());
        return (LocalDateTime.parse("2023-06-24T00:00:00").isBefore(localDateTime) && LocalDateTime.parse("2023-06-24T00:59:59").isAfter(localDateTime)) || (
                LocalDateTime.parse("2023-06-24T23:00:00").isBefore(localDateTime) && LocalDateTime.parse("2023-06-24T23:59:59").isAfter(localDateTime));
    }

    @AllArgsConstructor
    @Getter
    public enum Game {
        PromotionGame("ump:dragon:promotionGame", "晋级赛", LocalDateTime.parse("2023-06-20T11:59:59"), LocalDateTime.parse("2023-06-20T23:59:59"), 10L),
        PkGame("ump:dragon:pkGame", "pk赛", LocalDateTime.parse("2023-06-21T00:00:00"), LocalDateTime.parse("2023-06-21T23:59:59"), 8L),
        GroupGame("ump:dragon:groupGame", "小组赛", LocalDateTime.parse("2023-06-22T00:00:00"), LocalDateTime.parse("2023-06-22T23:59:59"), 4L),
        BreakoutGame("ump:dragon:breakoutGame", "突围赛", LocalDateTime.parse("2023-06-23T00:00:00"), LocalDateTime.parse("2023-06-23T23:59:59"), 3L),
        FinalsGame("ump:dragon:finalsGame", "决赛", LocalDateTime.parse("2023-06-24T00:00:00"), LocalDateTime.parse("2023-06-24T23:59:59"), 2L),
        ;

        private String rankKey;
        private String description;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Long rankLen;

        public static Game findCurGame() {
            LocalDateTime localDateTime = ActivityTimeUtil.getNow(ActivityCheckListEnum.DRAGON.getCode());
            for (Game value : values()) {
                if (value.getStartTime().isBefore(localDateTime) && value.getEndTime().isAfter(localDateTime)) {
                    return value;
                }
            }
            return null;
        }

        public static Game findByRankKey(String rankKey) {
            if (StringUtils.isBlank(rankKey)) {
                return null;
            }
            for (Game value : values()) {
                if (StringUtils.equalsIgnoreCase(value.getRankKey(), rankKey)) {
                    return value;
                }
            }
            return null;
        }
    }

}
