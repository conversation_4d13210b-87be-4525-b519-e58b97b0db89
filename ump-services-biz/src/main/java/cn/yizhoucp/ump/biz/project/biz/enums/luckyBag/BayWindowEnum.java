package cn.yizhoucp.ump.biz.project.biz.enums.luckyBag;

/**
 * 飘屏 TODO
 *
 * <AUTHOR>
 */
public enum BayWindowEnum {

    /** 抢七开始 */
    SEVEN_START("SEVEN_START", "https://res-cdn.nuan.chat/activity/astrology/70-head.png", "https://res-cdn.nuan.chat/activity/astrology/70-bg.png",
            "https://res-cdn.nuan.chat/activity/astrology/70-btn.png", "", "#FFFFFF",
            "全服公告：福袋高爆进度进入冲刺！现在抽取福袋，有机会成为天选之人——直接获得传说礼物！<a href=\"app://router.nuan.chat/user/jumpDialog?dialog=luckYouDialog\">点击立即加入></a>", "抢七开始（全服福气值达到70%）"),
    /** 高爆 30s 倒计时 */
    COUNT_DOWN("COUNT_DOWN", "https://res-cdn.nuan.chat/activity/astrology/start-head.png", "https://res-cdn.nuan.chat/activity/astrology/start-bg.png",
            "https://res-cdn.nuan.chat/activity/astrology/start-btn.png", "", "#FFFFFF",
            "全服公告：全服福气充满，30秒后高爆即将开启！<a href=\"app://router.nuan.chat/user/jumpDialog?dialog=luckYouDialog\">点击立即加入></a>", "高爆 30s 倒计时"),
    /** 高爆开始 */
    START("START", "https://res-cdn.nuan.chat/activity/astrology/start-head.png", "https://res-cdn.nuan.chat/activity/astrology/ing-bg-%s.png",
            "https://res-cdn.nuan.chat/activity/astrology/start-btn.png", "", "#FFFFFF",
            "全服公告：高爆开启，神话出现！传说礼物概率8倍！还剩 %s 分钟！<a href=\"app://router.nuan.chat/user/jumpDialog?dialog=luckYouDialog\">点击立即加入></a>", "高爆开始"),

    LUCKY_BAD_FIGHT_PRE("START", "https://res-cdn.nuan.chat/res/luckyBag/start_head.png", "https://res-cdn.nuan.chat/res/luckyBag/start_background.png",
            "https://res-cdn.nuan.chat/res/luckyBag/start_button.png", "5分钟后%s活动即将开启，超多奖励等你拿！<a href=\"app://router.nuan.chat/user/jumpDialog?dialog=luckYouDialog\">「立即前往」></a>", "#FFFFFF",
            "5分钟后%s活动即将开启，超多奖励等你拿！<a href=\"app://router.nuan.chat/user/jumpDialog?dialog=luckYouDialog\">「立即前往」</a>", "怪兽倒计时"),
    PROTECT_CONSTELLATION("PROTECT_CONSTELLATION","", "https://res-cdn.nuan.chat/activity/ConstellationActivities/Pb.png",
            "", "星座礼盒进入\"星运时刻\"，特定礼物出现，限时10分钟！<a href=\"%s\">「立即前往」</a>", "#FFFFFF",
            "星座礼盒进入\"星运时刻\"，特定礼物出现，限时10分钟！<a href=\"%s\">「立即前往」</a>", "守护星座系列活动"),
    ;

    private String code;

    private String headImg;

    private String backgroundImg;

    private String buttonImg;

    private String text;

    private String textColor;

    private String noticeText;

    private String desc;

    BayWindowEnum(String code, String headImg, String backgroundImg, String buttonImg, String text, String textColor, String noticeText, String desc) {
        this.code = code;
        this.headImg = headImg;
        this.backgroundImg = backgroundImg;
        this.buttonImg = buttonImg;
        this.text = text;
        this.textColor = textColor;
        this.noticeText = noticeText;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getHeadImg() {
        return headImg;
    }

    public String getBackgroundImg() {
        return backgroundImg;
    }

    public String getButtonImg() {
        return buttonImg;
    }

    public String getText() {
        return text;
    }

    public String getTextColor() {
        return textColor;
    }

    public String getNoticeText() {
        return noticeText;
    }

    public String getDesc() {
        return desc;
    }
}
