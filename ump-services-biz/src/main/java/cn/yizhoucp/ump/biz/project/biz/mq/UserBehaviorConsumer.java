package cn.yizhoucp.ump.biz.project.biz.mq;

import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.util.FunctionUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqAbstractConsumer;
import cn.yizhoucp.ump.biz.project.biz.manager.EvaluateRewardManager;
import cn.yizhoucp.ump.biz.project.biz.manager.ability.UserStartUpAppManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.rushSky.RushSkyBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.sweetCp.SweetCpBizManager;
import cn.yizhoucp.ump.biz.project.biz.manager.astrology.AstrologyCallBackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.goddessTrain.GoddessTrainAppointmentManager;
import cn.yizhoucp.ump.biz.project.biz.manager.officialCombine.OfficialCombineV2Manager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

import static cn.yizhoucp.ms.core.base.TopicTagEnum.*;

/**
 * 用户启动app 事件消费
 *
 * <AUTHOR>
 * @Date 2022/7/19 18:09
 * @Version 1.0
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "user_behavior_topic", consumerGroup = "USER_RECALL_UMP_SERVICES_GROUP")
public class UserBehaviorConsumer extends RocketmqAbstractConsumer {

    @Resource
    private UserStartUpAppManager userStartUpAppManager;
    @Resource
    private RushSkyBizManager rushSkyBizManager;
    @Resource
    private GoddessTrainAppointmentManager goddessTrainAppointmentManager;
    @Resource
    private SweetCpBizManager sweetCpBizManager;
    @Resource
    private AstrologyCallBackManager astrologyCallBackManager;

    @Resource
    private EvaluateRewardManager evaluateRewardManager;

    @Resource
    private OfficialCombineV2Manager officialCombineV2Manager;

    /** 关注 tag 列表 */
    private static final Set<String> LISTEN_TAG_SET = Sets.newHashSet(TOPIC_USER_STARTUP_APP.getTagKey(), TOPIC_USER_REGISTER_COMPLETE_NOW.getTagKey(),
            TOPIC_USER_BEHAVIOR_ONLINE.getTagKey(), TOPIC_DELAY_USER_SWITCH_TAB.getTagKey());

    @Override
    protected String getTopic() {
        return TopicConstant.TOPIC_USER_BEHAVIOR.getTopicKey();
    }

    @Override
    protected Set<String> getTags() {
        return LISTEN_TAG_SET;
    }

    @Override
    protected Boolean consume(String unionId, Long userId, String tag, String param) {
        if (TOPIC_USER_STARTUP_APP.getTagKey().equals(tag)) {
            return userStartUpHandle(BaseParam.builder().appId(MDCUtil.getCurAppIdByMdc()).unionId(unionId).uid(userId).build(), JSON.parseObject(param));
        } else if (TOPIC_USER_REGISTER_COMPLETE_NOW.getTagKey().equals(tag)) {
            return userRegister(param, unionId, userId);
        } else if (TOPIC_USER_BEHAVIOR_ONLINE.getTagKey().equals(tag)) {
            return userOnline(param);
        } else if (TOPIC_DELAY_USER_SWITCH_TAB.getTagKey().equals(tag)) {
            return userSwitchTab(param);
        }
        return Boolean.FALSE;
    }

    private Boolean userOnline(String jsonBody) {
        log.info("user_online {}", jsonBody);
        return Boolean.TRUE;
    }

    private Boolean userRegister(String param, String unionId, Long userId) {
        JSONObject paramObject = JSONObject.parseObject(param);
        Long uid = paramObject.getLong("uid");
        String vestChannel = paramObject.getString("vestChannel");
        String platform = paramObject.getString("platform");
        String deviceId = paramObject.getString("deviceId");
        String deviceBrand = paramObject.getString("deviceBrand");
        if (!FunctionUtil.allNotNull(uid, vestChannel, platform, deviceId)) {
            log.warn("mq userRegister param is null, param:{}", param);
            return Boolean.FALSE;
        }
        evaluateRewardManager.userRegisterSuccess(uid, vestChannel, platform, deviceId, deviceBrand);
        return Boolean.TRUE;
    }

    private Boolean userStartUpHandle(BaseParam param, JSONObject bizParam) {
        // astrologerChallengeManager.userLoginHandler(param);
        astrologyCallBackManager.tryJoinActivity(param.getUid());
        userStartUpAppManager.commonActivityHandle(param.getAppId(), param.getUnionId(), param.getUid(), bizParam.getString("value"));
        sweetCpBizManager.loginHandle(param);
        rushSkyBizManager.loginHandle(param);
        goddessTrainAppointmentManager.signUpStart(param.getUnionId(), param.getAppId(), param.getUid());
        return Boolean.TRUE;
    }

    private Boolean userSwitchTab(String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        officialCombineV2Manager.showOfficialCombineChat(jsonObject.getLong("appId"), jsonObject.getLong("uid"), jsonObject.getString("sex"));
        return Boolean.TRUE;
    }

}
