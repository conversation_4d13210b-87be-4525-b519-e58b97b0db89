package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.DrawPooExtractType;
import cn.yizhoucp.ump.biz.project.web.vo.DrawPoolAdminVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 奖池
 *
 * @author: lianghu
 */
@EntityListeners(value = AuditingEntityListener.class)
@Table(name = "activity_draw_pool")
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrawPoolDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /** 活动 code */
    private String activityCode;
    /** 奖池编号 */
    private String poolCode;
    /** 奖池描述 */
    private String poolName;

    /** 奖池类型 */
    @Enumerated(EnumType.STRING)
    private DrawPooExtractType poolType;

    /** 最低规则 转化为 DrawLimitRule */
    private String limitRule;

    /** 奖池业务 Key */
    private String bizKey;
    /** 奖池状态 */
    private Integer status;
    /** 开始时间 */
    private LocalDateTime startTime;
    /** 结束时间 */
    private LocalDateTime endTime;
    /** 扩展信息 */
    private String extData;
    /** 创建时间 */
    @CreatedDate
    private LocalDateTime createTime;
    /** 更新时间 */
    @LastModifiedDate
    private LocalDateTime updateTime;

    /** 应用唯一ID */
    private String unionId;

    public Integer getPoolSize() {
        if (StringUtils.isBlank(extData)) {
            return null;
        }
        Map<String, Object> map = JSONObject.parseObject(extData, Map.class);
        return (Integer) map.get("poolSize");
    }

    public DrawPoolDO setUseDoubleProb(Boolean useDoubleProb) {
        if (Objects.isNull(useDoubleProb)) {
            return null;
        }
        Map<String, Object> ext = new HashMap();
        ext.put("useDoubleProb", useDoubleProb);
        extData = JSON.toJSONString(ext);
        return this;
    }

    public Integer getSingleCoin() {
        if (StringUtils.isBlank(extData)) {
            return 1;
        }
        Map<String, Object> map = Optional.ofNullable(JSON.parseObject(extData, new TypeReference<Map<String, Object>>() {})).orElse(new HashMap<>());
        return (Integer) Optional.ofNullable(map.get("singleCoin")).orElse(10);
    }

    public DrawPoolDO setSingleCoin(Integer singleCoin) {
        Map<String, Object> ext = Optional.ofNullable(JSON.parseObject(extData, new TypeReference<Map<String, Object>>() {})).orElse(new HashMap<String, Object>());
        ext.put("singleCoin", singleCoin);
        extData = JSON.toJSONString(ext);
        return this;
    }

    public static void main(String[] args) {
        DrawPoolDO drawPoolDO = DrawPoolDO.builder().build().setUseDoubleProb(Boolean.TRUE);
        System.out.println(JSON.toJSONString(drawPoolDO));
    }

    public Boolean getUseDoubleProb() {
        if (StringUtils.isBlank(extData)) {
            return null;
        }
        Map<String, Object> map = JSONObject.parseObject(extData, Map.class);
        return (Boolean) map.get("useDoubleProb");
    }

    public DrawPoolAdminVO convert2AdminVO() {
        return DrawPoolAdminVO.builder()
                .id(getId())
                .activityCode(getActivityCode())
                .poolName(getPoolName())
                .poolCode(getPoolCode())
                .poolType(getPoolType().getCode())
                .build();
    }

}
