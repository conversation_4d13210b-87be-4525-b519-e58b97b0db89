package cn.yizhoucp.ump.biz.project.dal.mybatis.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * 传说中的梦幻岛灵兽表
 * @TableName ln_spirit_animal
 */
@TableName(value ="ln_spirit_animal")
@Data
public class LnSpiritAnimal implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 灵兽唯一标识
     */
    @TableField(value = "unique_key")
    private String uniqueKey;

    /**
     * 灵兽名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 灵兽图片-主界面
     */
    @TableField(value = "icon_index")
    private String iconIndex;

    /**
     * 灵兽图片-弹窗
     */
    @TableField(value = "icon_push")
    private String iconPush;

    /**
     * 喜好口粮
     */
    @TableField(value = "preference_ration")
    private String preferenceRation;

    /**
     * 讨厌口粮
     */
    @TableField(value = "nuisance_ration")
    private String nuisanceRation;

    /**
     * 主页封面key
     */
    @TableField(value = "hc_key")
    private String hcKey;

    /**
     * 进场特效key
     */
    @TableField(value = "ese_key")
    private String eseKey;

    /**
     * 进房提醒key
     */
    @TableField(value = "cir_key")
    private String cirKey;

    /**
     * 新型房送礼通道key
     */
    @TableField(value = "nrgc_key")
    private String nrgcKey;

    /**
     * 红包封面key
     */
    @TableField(value = "rec_key")
    private String recKey;

    /**
     * 状态
     * {@link cn.yizhoucp.ms.core.base.Status}
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}