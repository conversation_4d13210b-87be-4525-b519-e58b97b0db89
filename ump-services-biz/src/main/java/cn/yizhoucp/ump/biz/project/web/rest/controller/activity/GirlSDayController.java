package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.girlSDay.IndexVO;
import cn.yizhoucp.ump.api.vo.girlSDay.LevelInfoVO;
import cn.yizhoucp.ump.api.vo.girlSDay.MoreUserVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.girlsDay.GirlSDayEveryDayTaskEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay.*;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay.GirlSDayHelper.ACTIVITY_CODE;

@RestController
public class GirlSDayController {
    @Resource
    private GirlSDrawManager girlSDrawManager;

    @Resource
    private GirlSBizManager girlSBizManager;

    @Resource
    private GirlSDayIndexManager girlSDayIndexManager;

    @Resource
    private GirlSDayPkManager girlSDayPkManager;

    @Resource
    private GirlSDayHelper girlSDayHelper;

    @Resource
    private GirlSDayRankManager girlSDayRankManager;

    @GetMapping("/api/inner/activity/girl-s-day/draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSDrawManager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }

    @GetMapping("/api/inner/ump/girl-s-day/index")
    public Result<IndexVO> index(@RequestParam(value = "toUid", required = false) Long toUid) {
        BaseParam param = BaseParam.ofMDC();
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSDayIndexManager.index(param, toUid));
    }

    @GetMapping("/api/inner/ump/girl-s-day/more-select")
    public Result<MoreUserVO> moreUserSelect(@RequestParam("type") String type, @RequestParam(value = "toUid", required = false) Long toUid) {
        BaseParam param = BaseParam.ofMDC();
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSDayIndexManager.moreUser(param.getUid(), type, toUid));
    }

    @GetMapping("/api/inner/ump/girl-s-day/receive-every-day-reward")
    public Result<JSONObject> receiveEveryDayReward(@RequestParam("index") Integer index) {
        BaseParam param = BaseParam.ofMDC();
        GirlSDayEveryDayTaskEnum byIndex = GirlSDayEveryDayTaskEnum.getByIndex(index);
        if (byIndex == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务不存在");
        }
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSBizManager.receiveDailyTaskReward(param.getUid(), byIndex));
    }

    @GetMapping("/api/inner/ump/girl-s-day/draw")
    public Result<PrizeItem> draw() {
        BaseParam param = BaseParam.ofMDC();
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSBizManager.fairyWandDraw(param));
    }

    @GetMapping("/api/inner/ump/girl-s-day/up-level")
    public Result<LevelInfoVO> upLevel(@RequestParam("toUid") Long toUid) {
        BaseParam param = BaseParam.ofMDC();
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSBizManager.upLevel(param, toUid));
    }

    @GetMapping("/api/inner/ump/girl-s-day/get-gift-level")
    public Result<Integer> getGiftLevel() {
        BaseParam param = BaseParam.ofMDC();
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSBizManager.getGiftLevel(param.getUid()));
    }

    @GetMapping("/api/inner/ump/girl-s-day/my-draw-recode")
    public Result<List<DrawLogItem>> drawRecord() {
        SecurityUser su = SecurityUtils.getCurrentUser();
        return RestBusinessTemplate.executeWithoutTransaction(() -> girlSBizManager.drawLogList(su.getUserId()));
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/ump/job/girl-s-day/start-pk")
    public Result<Boolean> startPk() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
                    girlSDayPkManager.generatePk(girlSDayHelper.getYesterdayNowYyyyMMdd());
                    return Boolean.TRUE;
                }
        );
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/ump/job/girl-s-day/generate-pk-result")
    public Result<Boolean> generatePkResult() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
                    girlSDayPkManager.pkResult();
                    return Boolean.TRUE;
                }
        );
    }

    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/ump/job/girl-s-day/post-prize")
    public Result<Boolean> postPrize() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> {
                    girlSDayRankManager.postGift();
                    return Boolean.TRUE;
                }
        );
    }
}
