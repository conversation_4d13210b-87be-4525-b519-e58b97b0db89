package cn.yizhoucp.ump.biz.project.biz.mq.kafka.sync;


import cn.yizhoucp.ump.biz.project.biz.manager.LuckyBagSyncManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 手气福袋迁移
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class LuckyBagSyncConsumer {

    @Resource
    private LuckyBagSyncManager luckyBagSyncManager;

    @KafkaListener(topics = "octopus_ump_prize_pool_cdc", containerFactory = "batchFactory")
    public void consume(List<String> records) {
        try {
            log.info("福袋迁移数据消费 records:{}", JSON.toJSONString(records));
            luckyBagSyncManager.sync(records);
        } catch (Exception e) {
            log.error("福袋迁移数据消费失败 records:{}", JSON.toJSONString(records), e);
        }
    }

}
