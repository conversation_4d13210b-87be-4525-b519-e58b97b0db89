package cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.strategy;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.common.MusicForHeartRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.music_for_heart.vo.MusicForHeartRankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MusicForHeartRank implements ExecutableStrategy {

    @Resource
    private MusicForHeartRedisManager musicForHeartRedisManager;
    
    private static final String MAN_RANK = "man";
    private static final String WOMAN_RANK = "woman";
    private static final int DEFAULT_PAGE_SIZE = 10;
    private static final int DEFAULT_PAGE_NUM = 1;

    @Override
    public MusicForHeartRankVO execute(ButtonEventParam buttonEventParam) {
        if (buttonEventParam == null || !buttonEventParam.check()) {
            return MusicForHeartRankVO.builder().build();
        }
        
        // 获取排行榜类型：man 或 woman
        String rankType = buttonEventParam.getBizKey();
        if (StringUtils.isBlank(rankType) || (!MAN_RANK.equals(rankType) && !WOMAN_RANK.equals(rankType))) {
            // 如果没有指定或类型不正确，默认使用男神榜
            rankType = MAN_RANK;
        }
        
        // 解析扩展参数
        int pageNum = DEFAULT_PAGE_NUM;
        int pageSize = DEFAULT_PAGE_SIZE;
        Long uid = buttonEventParam.getBaseParam() != null ? buttonEventParam.getBaseParam().getUid() : 0L;
        
        if (StringUtils.isNotBlank(buttonEventParam.getExtDate())) {
            try {
                JSONObject extData = JSON.parseObject(buttonEventParam.getExtDate());
                if (extData.containsKey("pageNum")) {
                    pageNum = extData.getIntValue("pageNum");
                }
                if (extData.containsKey("pageSize")) {
                    pageSize = extData.getIntValue("pageSize");
                }
            } catch (Exception e) {
                // 解析失败，使用默认值
            }
        }
        
        // 检查参数合法性
        if (pageNum <= 0) {
            pageNum = DEFAULT_PAGE_NUM;
        }
        if (pageSize <= 0) {
            pageSize = DEFAULT_PAGE_SIZE;
        }
        
        // 调用 Redis 管理器获取排行榜数据
        return musicForHeartRedisManager.getRankList(rankType, pageNum, pageSize, uid);
    }
}
