package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.PlatformEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.VestChannelEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.VersionUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.CoupleEffectVO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.ActivitySpecialEffectDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.ActivitySpecialEffectService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 天生羁绊特效管理类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EmotionalBondsSpecialEffectManager {

    @Resource
    private ActivitySpecialEffectService activitySpecialEffectService;
    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private UserFeignManager userFeignManager;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    /**
     * 获取情侣特效列表
     *
     * @param param 基础参数
     * @return 情侣特效列表
     */
    public List<CoupleEffectVO> getCoupleEffects(BaseParam param) {
        // 尝试从缓存中获取数据
        List<CoupleEffectVO> cacheData = emotionalBondsRedisManager.getCoupleEffectsCache();
        if (!CollUtil.isEmpty(cacheData)) {
            return cacheData;
        }

        // 缓存中没有数据或解析失败，从数据库获取
        log.debug("从数据库获取情侣特效列表");
        List<ActivitySpecialEffectDO> effectList = activitySpecialEffectService.getByActivityCodeAndEffectType(
                EmotionalBondsConstant.ACTIVITY_CODE, "coupleEffect");

        // 转换为VO对象 - 列表模式下仅返回基本信息
        List<CoupleEffectVO> result = effectList.stream().map(effect -> {
            Map<String, Object> configMap = parseConfig(effect.getConfig());
            return CoupleEffectVO.builder()
                    .effectCode(effect.getEffectCode())
                    .effectName(getConfigValue(configMap, "effectName", null))
                    .backgroundImg(getConfigValue(configMap, "backgroundImg", null))
                    .build();
        }).collect(Collectors.toList());

        // 将结果存入缓存
        try {
            emotionalBondsRedisManager.setCoupleEffectsCache(result);
            log.debug("情侣特效列表已缓存，过期时间：{}秒", DateUtil.ONE_DAY_SECONDS);
        } catch (Exception e) {
            log.error("缓存情侣特效列表失败", e);
        }

        return result;
    }

    /**
     * 根据特效代码获取情侣特效
     * 包含关系检查和过期处理逻辑
     *
     * @param effectCode 特效代码
     * @return 情侣特效
     */
    public CoupleEffectVO getCoupleEffectByCode(Long uid, String effectCode) {

        // 如果用户缓存中没有，则从数据库查询
        log.debug("从数据库查询特效: {}", effectCode);
        ActivitySpecialEffectDO effect = activitySpecialEffectService.getByEffectCode(effectCode);
        if (effect == null || !"coupleEffect".equals(effect.getEffectType())) {
            return null;
        }

        // 获取用户关系信息
        Long relationshipId = emotionalBondsRedisManager.getEquippedRelationship(uid);
        if (relationshipId == null) {
            log.warn("用户{}没有装备任何关系",uid);
            return null;
        }

        // 从合并表中获取关系信息
        EmotionalBondSummaryDO existingRelationship = emotionalBondSummaryService.getById(relationshipId);
        if (existingRelationship == null) {
            log.warn("用户{}装备的关系{}不存在",uid, relationshipId);
            // 清除无效的装备关系缓存
            emotionalBondsRedisManager.clearEquippedRelationship(uid);
            return null;
        }

        // 检查关系是否过期
        Date currentTime = new Date();
        if (existingRelationship.getExpireTime() != null && currentTime.after(existingRelationship.getExpireTime())) {
            log.info("用户{}的关系已过期，过期时间: {}", uid, existingRelationship.getExpireTime());
            // 清除无效的装备关系缓存
            emotionalBondsRedisManager.clearEquippedRelationship(uid);
            return null;
        }

        // 确定当前用户是主动方还是被动方，并获取对方的用户ID
        Long oppositeId;
        String userRole;
        if (uid.equals(existingRelationship.getUserId())) {
            oppositeId = existingRelationship.getOppositeId();
            userRole = existingRelationship.getUserSelectedRole();
        } else {
            oppositeId = existingRelationship.getUserId();
            userRole = existingRelationship.getOppositeSelectedRole();
        }

        UserVO oppositedUser = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), oppositeId);
        UserVO user = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        if (oppositeId == null || user == null) {
            return null;
        }

        return convertToCoupleEffectVO(effect, existingRelationship, user, oppositedUser, userRole);
    }

    /**
     * 将DO对象转换为VO对象
     * 从 config 字段读取所有配置，实体类中只有基本字段
     * 纯粹的转换方法，不包含业务逻辑
     *
     * @param effect              特效DO对象
     * @param existingRelationship 关系实体
     * @param user                当前用户
     * @param oppositeUser        对方用户
     * @param userRole            当前用户角色
     * @return 情侣特效VO对象
     */
    private CoupleEffectVO convertToCoupleEffectVO(ActivitySpecialEffectDO effect, EmotionalBondSummaryDO existingRelationship,
                                                 UserVO user, UserVO oppositeUser, String userRole) {
        // 解析config字段中的JSON配置
        Map<String, Object> configMap = parseConfig(effect.getConfig());
        EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effect.getEffectCode());

        // 确定图标
        String iconRight;
        if (rolePairEnum.getRoleA().equals(userRole)) {
            iconRight = getConfigValue(configMap, "iconRightA", null);
        } else {
            iconRight = getConfigValue(configMap, "iconRightB", null);
        }

        // 生成描述文本
        String desc;
        Long uid = user.getId();
        if (uid.equals(existingRelationship.getUserId())) {
            desc = String.format(effect.getDesc(), oppositeUser.getName(), existingRelationship.getUserSelectedRole(), user.getName());
        } else {
            desc = String.format(effect.getDesc(), oppositeUser.getName(), existingRelationship.getOppositeSelectedRole(), user.getName());
        }

        // 构建VO对象，从 config 中读取所有配置
        CoupleEffectVO.CoupleEffectVOBuilder builder = CoupleEffectVO.builder()
                .effectCode(effect.getEffectCode())
                .effectName(getConfigValue(configMap, "effectName", null))
                .desc(desc)
                .userAvatarLeft(user.getAvatar())
                .userAvatarRight(oppositeUser.getAvatar())
                .iconBetween(getConfigValue(configMap, "iconBetween", null))
                .iconBottomLeft(getConfigValue(configMap, "iconBottomLeft", null))
                .iconRight(iconRight)
                .backgroundImg(getConfigValue(configMap, "backgroundImg", null));

        // 收集额外的配置参数（不在标准字段中的参数）
        Map<String, Object> extraConfig = new HashMap<>();
        for (Map.Entry<String, Object> entry : configMap.entrySet()) {
            String key = entry.getKey();
            // 过滤掉已经处理过的标准字段
            if (!isStandardField(key)) {
                extraConfig.put(key, entry.getValue());
            }
        }

        if (!extraConfig.isEmpty()) {
            builder.extraConfig(extraConfig);
        }

        return builder.build();
    }

    /**
     * 将DO对象转换为VO对象 - 兼容原有方法签名
     * 该方法仅用于兼容原有代码，新代码应使用带参数的方法
     *
     * @param effect 特效DO对象
     * @return 情侣特效VO对象
     */
    private CoupleEffectVO convertToCoupleEffectVO(ActivitySpecialEffectDO effect) {
        // 仅返回基本信息，不包含用户相关数据
        Map<String, Object> configMap = parseConfig(effect.getConfig());
        return CoupleEffectVO.builder()
                .effectCode(effect.getEffectCode())
                .effectName(getConfigValue(configMap, "effectName", null))
                .backgroundImg(getConfigValue(configMap, "backgroundImg", null))
                .build();
    }

    /**
     * 判断是否为标准字段
     *
     * @param fieldName 字段名称
     * @return 是否为标准字段
     */
    private boolean isStandardField(String fieldName) {
        return "effectCode".equals(fieldName) ||
                "effectName".equals(fieldName) ||
                "userAvatarLeft".equals(fieldName) ||
                "userAvatarRight".equals(fieldName) ||
                "iconBetween".equals(fieldName) ||
                "desc".equals(fieldName) ||
                "iconBottomLeft".equals(fieldName) ||
                "iconRight".equals(fieldName) ||
                "backgroundImg".equals(fieldName) ||
                "animationDuration".equals(fieldName) ||
                "animationEffect".equals(fieldName);
    }

    /**
     * 解析config字段中的JSON配置
     *
     * @param config 配置JSON字符串
     * @return 配置Map
     */
    private Map<String, Object> parseConfig(String config) {
        if (StringUtils.isBlank(config)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(config, Map.class);
        } catch (Exception e) {
            log.error("解析特效配置失败: {}", config, e);
            return new HashMap<>();
        }
    }

    /**
     * 从配置Map中获取值，如果不存在则使用默认值
     *
     * @param configMap    配置Map
     * @param key          配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    private String getConfigValue(Map<String, Object> configMap, String key, String defaultValue) {
        Object value = configMap.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    public Boolean versionVerify(Long uid, Long appId) {
        log.debug("button lnVersionVerify uid:{},appId:{}", uid, appId);
        if (Objects.isNull(appId)) {
            appId = ServicesAppIdEnum.lanling.getAppId();
        }
        UserVO userVO = userFeignManager.getBasicWithCache(appId, uid);
        if (Objects.isNull(userVO)) {
            log.warn("fail loginUid {}", MDCUtil.getCurUserIdByMdc());
            throw new ServiceException(ErrorCode.USER_NOT_EXIST);
        }
        // ios直接跳过
        PlatformEnum userPlatform = userVO.getPlatform();
        if (PlatformEnum.iOS.equals(userPlatform)) {
            return false;
        }
        VestChannelEnum vestChannel = userVO.getVestChannel();
        String versionCodeStr = userVO.getVersionName();
        String enableVersion = "4.122.52";
        switch (vestChannel) {
            case yumo:
                enableVersion = "2.135.19";
                break;
            case yanyuan:
                enableVersion = "2.83.19";
                break;
            case momogirl:
                enableVersion = "2.120.19";
                break;
            case huayuan:
                enableVersion = "4.139.19";
                break;
            case couduis:
                enableVersion = "7.135.19";
                break;
            case lianainiang:
                enableVersion = "5.146.19 ";
                break;
            case nuanliao:
                enableVersion = "5.166.19";
                break;
            case laoxiang:
                enableVersion = "2.88.19";
                break;
            case aiyuliaotian:
                enableVersion = "2.78.19";
                break;
            case qznychat:
                enableVersion = "3.56.19";
                break;
            case paiyuan:
                enableVersion = "1.0.9";
                break;
            default:
                enableVersion = "1.0.0";
        }
        if ("null".equalsIgnoreCase(versionCodeStr)) {
            return Boolean.FALSE;
        }
        log.info("emotionalBondsVersionVerify uid:{} vestChannel {} enableVersion: {} versionCodeStr:{}", userVO.getId(), vestChannel.getCode(), enableVersion, versionCodeStr);
        return VersionUtil.isNewVersion(enableVersion, versionCodeStr);
    }

    public Boolean clearCostumeCache(Long uid) {
        emotionalBondsRedisManager.clearEquippedRelationship(uid);
        return Boolean.TRUE;
    }
}
