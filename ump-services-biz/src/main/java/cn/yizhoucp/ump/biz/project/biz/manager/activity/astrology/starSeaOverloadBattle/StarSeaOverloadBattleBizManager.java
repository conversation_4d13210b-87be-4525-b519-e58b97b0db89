package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaOverloadBattle;

import cn.yizhoucp.ms.core.base.TopicConstant;
import cn.yizhoucp.ms.core.base.TopicTagEnum;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.cassandra.base.enums.MqDelayLevelEnum;
import cn.yizhoucp.starter.cassandra.base.rocketmq.RocketmqProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.astrology.starSeaOverloadBattle.OverloadVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawEvent;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import cn.yizhoucp.ump.biz.project.biz.event.navigation.NavigationConsumerPetrolEvent;
import cn.yizhoucp.ump.biz.project.biz.event.navigation.NavigationConsumerPetrolMessage;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.AstrologyValueManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.ASTROLOGY_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.COUNTRY_TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.NAVIGATION_PETROL;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.OVERLOAD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.OVERLOAD_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.RECEIVE_REDPACKET;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.REPLACE_GIFT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK_OVERALL1;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_RANK_OVERALL2;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.STAR_SEA_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaOverloadBattleConstant.TASK_CUR_FINISH_TIMES;

@Service
public class StarSeaOverloadBattleBizManager {

    @Resource
    private ActivityManager activityManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    private StarSeaOverloadBattleRankManager starSeaOverloadBattleRankManager;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private RocketmqProducerManager rocketmqProducerManager;
    @Resource
    private StarSeaOverloadBattleTrackManager starSeaOverloadBattleTrackManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private AstrologyValueManager astrologyValueManager;

    @EventListener
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void onDraw(AstrologyDrawEvent event) {
        ActivityDO activityDO = activityManager.getOnlineActivityInfo(BaseParam.ofMDC(), ACTIVITY_CODE);
        if (activityDO == null || activityDO.getStatus() <= 0) {
            return;
        }

        AstrologyDrawMessage drawMessage = event.getSource();

        Long astrologyTimes = redisManager.incrLong(
                String.format(ASTROLOGY_TIMES, drawMessage.getUid(), this.getDate()),
                drawMessage.getConsumeTimes(),
                DateUtil.ONE_MONTH_SECOND
        );

        for (int i = 0; i < StarSeaOverloadBattleConstant.Task.values().length; i++) {
            if (i == 0) {
                if (Boolean.TRUE.equals(redisManager.hasKey(String.format(RECEIVE_REDPACKET, drawMessage.getUid(), DateUtil.getNowYyyyMMdd())))
                        && Boolean.TRUE.equals(redisManager.setnx(String.format("ump:star_sea_overload_battle2:task_1_finish:%s:%s", drawMessage.getUid(), DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    Long taskCurFinishTimes = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, drawMessage.getUid(), StarSeaOverloadBattleConstant.Task.values()[i].name()), 1L, DateUtil.ONE_MONTH_SECOND);
                    if (taskCurFinishTimes == StarSeaOverloadBattleConstant.Task.values()[i].getItemNum()) {
                        starSeaOverloadBattleTrackManager.allActivityTaskFinish(StarSeaOverloadBattleConstant.Task.values()[i].name(), drawMessage.getUid());
                    }
                }
            } else {
                int itemNum = 0;
                for (DrawPoolItemDTO drawPoolItemDTO : drawMessage.getDrawResult()) {
                    if (StarSeaOverloadBattleConstant.Task.values()[i].getItemValueGold().contains(drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold())) {
                        itemNum += drawPoolItemDTO.getTargetTimes() * drawPoolItemDTO.getDrawPoolItemDO().getItemNum();
                    }
                }
                Long taskCurFinishTimes = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, drawMessage.getUid(), StarSeaOverloadBattleConstant.Task.values()[i].name()), itemNum, DateUtil.ONE_MONTH_SECOND);
                if (taskCurFinishTimes == StarSeaOverloadBattleConstant.Task.values()[i].getItemNum()) {
                    starSeaOverloadBattleTrackManager.allActivityTaskFinish(StarSeaOverloadBattleConstant.Task.values()[i].name(), drawMessage.getUid());
                }
            }
        }

        LocalDateTime startTime = activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();

        if (startTime != null) {
            StarSeaOverloadBattleConstant.CountryTask countryTask = StarSeaOverloadBattleConstant.CountryTask.values()[Math.toIntExact(ChronoUnit.DAYS.between(startTime, now))];
            Long countryTaskCurFinishTimes = null;
            if ("astrology_times".equals(countryTask.getType())) {
                countryTaskCurFinishTimes = redisManager.incrLong(String.format(COUNTRY_TASK_CUR_FINISH_TIMES, countryTask.name()), drawMessage.getConsumeTimes(), DateUtil.ONE_MONTH_SECOND);
            } else if ("gift_count".equals(countryTask.getType())) {
                for (DrawPoolItemDTO drawPoolItemDTO : drawMessage.getDrawResult()) {
                    if (countryTask.getGiftCoin().equals(drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold())) {
                        countryTaskCurFinishTimes = redisManager.incrLong(String.format(COUNTRY_TASK_CUR_FINISH_TIMES, countryTask.name()), drawPoolItemDTO.getTargetTimes() * drawPoolItemDTO.getDrawPoolItemDO().getItemNum(), DateUtil.ONE_MONTH_SECOND);
                    }
                }
            }
            if (countryTaskCurFinishTimes != null && countryTaskCurFinishTimes >= countryTask.getTargetValue()) {
                CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(ServicesAppIdEnum.lanling.getAppId(), countryTask.getPrizeKey()).successData();
                if (coinGiftProductVO != null) {
                    redisManager.hset(
                            REPLACE_GIFT,
                            String.valueOf(coinGiftProductVO.getNeedCoin()),
                            PrizeItem.builder()
                                    .prizeKey(coinGiftProductVO.getGiftKey())
                                    .prizeIcon(coinGiftProductVO.getIcon())
                                    .prizeName(coinGiftProductVO.getName())
                                    .valueGold(Math.toIntExact(coinGiftProductVO.getNeedCoin()))
                                    .build());
                    if (Boolean.TRUE.equals(redisManager.setnx(String.format("ump:star_sea_overload_battle2:replace_gift_track:%s", countryTask.name()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                        starSeaOverloadBattleTrackManager.replaceGift(coinGiftProductVO.getGiftKey(), coinGiftProductVO.getNeedCoin(), drawMessage.getUid());
                    }
                }
            }
        }

        String date = this.getDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");
        LocalDateTime parse = LocalDateTime.parse(date + " 00:00:00", formatter);
        long between = ChronoUnit.DAYS.between(activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS), parse);
        if (between < 4) {
            starSeaOverloadBattleRankManager.incrRankValue(drawMessage.getUid(), Long.valueOf(drawMessage.getConsumeTimes()), String.format(STAR_SEA_RANK, this.getDate()));
        } else if (between < 7) {
            starSeaOverloadBattleRankManager.incrRankValue(drawMessage.getUid(), Long.valueOf(drawMessage.getConsumeTimes()), STAR_SEA_RANK_OVERALL2);
        } else if (between < 11) {
            starSeaOverloadBattleRankManager.incrRankValue(drawMessage.getUid(), Long.valueOf(drawMessage.getConsumeTimes()), String.format(STAR_SEA_RANK, this.getDate()));
        } else {
            starSeaOverloadBattleRankManager.incrRankValue(drawMessage.getUid(), Long.valueOf(drawMessage.getConsumeTimes()), STAR_SEA_RANK_OVERALL1);
        }

        for (DrawPoolItemDTO drawPoolItemDTO : drawMessage.getDrawResult()) {
            if (drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold() >= 9999) {
                Object overload = redisManager.get(OVERLOAD);
                OverloadVO overloadVO = new OverloadVO();
                if (overload != null) {
                    overloadVO = JSON.parseObject(String.valueOf(overload), OverloadVO.class);
                    if (overloadVO.getUid().equals(drawMessage.getUid())) {
                        break;
                    }
                    starSeaOverloadBattleRankManager.incrRankValue(overloadVO.getUid(), System.currentTimeMillis() - overloadVO.getCountdownTimestamp() + DateUtil.ONE_HOUR_MILLISECONDS * 3, OVERLOAD_RANK);
                    redisManager.incrLong(String.format(STAR_SEA_VALUE, overloadVO.getUid()), overloadVO.getValue(), DateUtil.ONE_MONTH_SECOND);
                    starSeaOverloadBattleTrackManager.seaStarKingListOut((System.currentTimeMillis() - overloadVO.getCountdownTimestamp() + DateUtil.ONE_HOUR_MILLISECONDS * 3) / 1000, overloadVO.getValue(), overloadVO.getUid());
                    astrologyValueManager.increase(overloadVO.getUid(), overloadVO.getValue(), "星海霸主战-霸主位");
                    notifyComponent.npcNotify(
                            ServicesAppIdEnum.lanling.getUnionId(),
                            overloadVO.getUid(),
                            String.format("您的霸主位已被其他人夺取，本次共获得%s星海值，快去夺回霸主位吧！", overloadVO.getValue())
                    );
                }
                overloadVO.setValue(100L);
                overloadVO.setUid(drawMessage.getUid());
                overloadVO.setCountdownTimestamp(System.currentTimeMillis() + DateUtil.ONE_HOUR_MILLISECONDS * 3);
                overloadVO.setAddStarSeaValueTimes(0);
                UserVO userVO = feignUserService.getBasic(drawMessage.getUid(), MDCUtil.getCurAppIdByMdc()).successData();
                if (userVO != null) {
                    overloadVO.setIcon(userVO.getAvatar());
                    overloadVO.setName(userVO.getName());
                }
                redisManager.set(OVERLOAD, JSON.toJSONString(overloadVO), DateUtil.ONE_MONTH_SECOND);

                rocketmqProducerManager.sendDelayMessage(TopicConstant.TOPIC_ACTIVITY.getTopicKey(), TopicTagEnum.TOPIC_ACTIVITY_STAR_SEA_OVERLOAD_BATTLE.getTagKey(), JSON.toJSONString(overloadVO), null, MqDelayLevelEnum.NINE_MINUTE);

                break;
            }
        }
    }

//    @EventListener
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void consumerPetrol(NavigationConsumerPetrolEvent event) {
        NavigationConsumerPetrolMessage message = event.getSource();
        Long uid = message.getUid();
        Integer consumerCount = message.getConsumerCount();

        Long navigationPetrol = redisManager.incrLong(
                String.format(NAVIGATION_PETROL, uid, DateUtil.getNowYyyyMMdd()),
                consumerCount,
                DateUtil.ONE_MONTH_SECOND
        );
        if (navigationPetrol >= StarSeaOverloadBattleConstant.Task.values()[1].getPetrol() && navigationPetrol - consumerCount < StarSeaOverloadBattleConstant.Task.values()[1].getPetrol()) {
            Long taskCurFinishTimes = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, StarSeaOverloadBattleConstant.Task.values()[1].name()), 1L, DateUtil.ONE_MONTH_SECOND);
            if (taskCurFinishTimes == StarSeaOverloadBattleConstant.Task.values()[1].getItemNum()) {
                starSeaOverloadBattleTrackManager.allActivityTaskFinish(StarSeaOverloadBattleConstant.Task.values()[1].name(), uid);
            }
        }
        if (navigationPetrol >= StarSeaOverloadBattleConstant.Task.values()[2].getPetrol() && navigationPetrol - consumerCount < StarSeaOverloadBattleConstant.Task.values()[2].getPetrol()) {
            Long taskCurFinishTimes = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, StarSeaOverloadBattleConstant.Task.values()[2].name()), 1L, DateUtil.ONE_MONTH_SECOND);
            if (taskCurFinishTimes == StarSeaOverloadBattleConstant.Task.values()[2].getItemNum()) {
                starSeaOverloadBattleTrackManager.allActivityTaskFinish(StarSeaOverloadBattleConstant.Task.values()[2].name(), uid);
            }
        }
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Map<Long, PrizeItem> getReplaceGiftMap() {
        Map<Object, Object> hmget = redisManager.hmget(REPLACE_GIFT);
        if (MapUtils.isEmpty(hmget)) {
            return Collections.emptyMap();
        }

        Map<Long, PrizeItem> replaceGiftMap = Maps.newHashMapWithExpectedSize(hmget.size());
        for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
            replaceGiftMap.put(Long.parseLong(String.valueOf(entry.getKey())), JSON.parseObject(String.valueOf(entry.getValue()), PrizeItem.class));
        }

        return replaceGiftMap;
    }

    public Boolean receiveRedpacket(BaseParam param) {
        return redisManager.setnx(String.format(RECEIVE_REDPACKET, param.getUid(), DateUtil.getNowYyyyMMdd()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
    }

    public String getDate() {
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        String date = null;
        if (hour < 2) {
            date = cn.yizhoucp.ump.biz.project.biz.util.DateUtil.format(cn.yizhoucp.ump.biz.project.biz.util.DateUtil.getBeforeDay(new Date()), cn.yizhoucp.ump.biz.project.biz.util.DateUtil.YMD_WITHOUT_LINE);
        } else {
            date = cn.yizhoucp.ump.biz.project.biz.util.DateUtil.format(new Date(), cn.yizhoucp.ump.biz.project.biz.util.DateUtil.YMD_WITHOUT_LINE);
        }

        return date;
    }

}
