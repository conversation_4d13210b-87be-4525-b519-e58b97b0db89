package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;


import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.miracleWinterSnow.MiracleWinterSnowRankManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class MiracleWinterSnowController {

    @Resource
    private MiracleWinterSnowRankManager miracleWinterSnowRankManager;

    /**
     * 领取奖励
     */
    @GetMapping("/api/inner/activity/miracle-winter-snow/receive-reward")
    public Result<Boolean> receiveReward(String taskKey) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> miracleWinterSnowRankManager.receiveReward(taskKey));
    }
}
