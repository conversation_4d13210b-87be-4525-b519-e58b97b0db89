package cn.yizhoucp.ump.biz.project.biz.manager.activity.StarrySea;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.StarrySea.StarrySeaConstant.*;


@Slf4j
@Service
public class StarrySeaRankManager extends AbstractRankManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private NotifyComponent notifyComponent;

    public static final String TIMES_BOARD = "ump:starrySea2:times:board:%s";

    public static final String GIFT_BOARD = "ump:starrySea2:giftcoin:board";

    public static final String BOARD_PRIZE_MESSAGE = "恭喜您在“幻月星海”活动中获得“占星次数榜”第%s名并获得%s金币礼物“%s”，奖励已发放至您的账号，请注意查收哦~";

    private static final Map<Long, String> NUMBER_MAP = new HashMap<Long, String>() {{
        put(1L, "一");
        put(2L, "二");
        put(3L, "三");
    }};

    @Override
    protected void postProcess(RankContext rankContext) {
    }

    @Override
    protected void doPreProcess(RankContext rankContext) {
        rankContext.setRankLen(5L);
    }

    public void incrTimesRank(AstrologyDrawMessage drawMessage) {
        incrRankValue(drawMessage.getUid(), Long.valueOf(drawMessage.getConsumeTimes()), getTimesKey());
    }

    private static String getTimesKey() {
        return String.format(TIMES_BOARD, DateUtil.getNowYyyyMMdd());
    }

    private static String getTimesKey(String dateStr) {
        return String.format(TIMES_BOARD, dateStr);
    }


    public void incrGiftRank(Long uid, long coin) {
        incrRankValue(uid, coin, GIFT_BOARD);
    }

    public RankVO getTimesRank(BaseParam param) {
        return getRank(RankContext.builder().rankKey(getTimesKey()).type(RankContext.RankType.user).param(param).build());
    }

    public RankVO getGiftRank(BaseParam param) {
        return getRank(RankContext.builder().rankKey(GIFT_BOARD).type(RankContext.RankType.user).param(param).build());
    }

    public Boolean postGiftYesterday() {
        String yesterdayStr = ActivityTimeUtil.getYesterdayStr(null);
//        String yesterdayStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(ActivityTimeUtil.getNow(null));
        log.info("发放 -> {}的奖励", yesterdayStr);
        if (Boolean.TRUE.equals(redisManager.setnx(String.format("ump:starrySea2:sendPrizeIdempotent:%s", yesterdayStr), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
            RankVO rank = this.getRank(RankContext.builder().rankKey(getTimesKey(yesterdayStr)).rankLen(3L).type(RankContext.RankType.user).build());
            List<RankItem> rankList = rank.getRankList();
            if (!CollectionUtils.isEmpty(rankList)) {
                List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, "times-board");

                for (RankItem rankItem : rankList) {
                    // 发放奖励
                    List<SendPrizeDTO> prizeList = scenePrizeDOList.stream().filter(x -> x.getPrizeBelongToRank().equals(rankItem.getRank())).map(item -> SendPrizeDTO.of(item, rankItem.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(prizeList)) {
                        log.error("rank -> {}, 发放奖励失败，未找到奖励", rankItem);
                        continue;
                    }
                    log.info("rank -> {}, prize -> {} 发放奖励", rankItem, prizeList);
                    sendPrizeManager.sendPrize(
                            BaseParam.builder()
                                    .appId(ServicesAppIdEnum.lanling.getAppId())
                                    .unionId(ServicesAppIdEnum.lanling.getUnionId())
                                    .uid(rankItem.getId())
                                    .build(),
                            prizeList
                    );
                    notifyComponent.npcNotify(
                            ServicesAppIdEnum.lanling.getUnionId(),
                            rankItem.getId(),
                            String.format(BOARD_PRIZE_MESSAGE, NUMBER_MAP.get(rankItem.getRank()), prizeList.get(0).getValueGold(), prizeList.get(0).getPrizeName()));
                }
            }
        }
        return Boolean.TRUE;
    }
}
