//package api.project.biz.manager.activity.richMan;
//
//import api.project.biz.enums.activity.richMan.DiceWayEnum;
//import api.project.biz.enums.activity.richMan.RouteGridEnum;
//import api.project.biz.manager.activity.AbstractManager;
//import api.project.biz.remoteService.lanling.FeignLanlingService;
//import api.project.biz.util.LanlingActivityUtil;
//import api.project.biz.util.NumberUtil;
//import api.project.dto.activicy.rickMan.DiceModel;
//import cn.yizhoucp.ms.core.base.ErrorCode;
//import cn.yizhoucp.ms.core.base.ServiceException;
//import cn.yizhoucp.ms.core.base.SystemNPC;
//import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
//import cn.yizhoucp.ms.core.base.convert.SexType;
//import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
//import cn.yizhoucp.ms.core.base.enums.coin.PointFromType;
//import cn.yizhoucp.ms.core.base.model.PageResult;
//import cn.yizhoucp.ms.core.base.util.MDCUtil;
//import cn.yizhoucp.ms.core.base.util.RandomUtil;
//import cn.yizhoucp.ms.core.vo.coinservices.IntimacyUserInfoVO;
//import cn.yizhoucp.ms.core.vo.imservices.ImChatInfoVO;
//import cn.yizhoucp.ms.core.vo.umpServices.activity.richman.*;
//import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
//import cn.yizhoucp.ms.core.vo.userservices.UserBasicVO;
//import cn.yizhoucp.ms.core.vo.userservices.UserVO;
//import cn.yizhoucp.starter.redis.manager.RedisManager;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.openservices.shade.com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.time.Instant;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.util.*;
//import java.util.concurrent.locks.Lock;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> pepper
// * @Classname RichManager
// * @Description 520 大富翁
// * @Date 2022/5/12 16:00
// */
//@Slf4j
//@Service
//public class RichManager extends AbstractManager {
//
//    @Autowired
//    private FeignLanlingService feignLanlingService;
//
//
//    /**
//     * 保存用户骰子数量 Hash
//     * key: activity:richMan:dice:number:[fromUid]
//     * item: [toUid]
//     */
//    private final static String DICE_NUMBER = "activity:richMan:dice:number:%s";
//
//
//    /**
//     * 保存用户免费骰子数量 Hash, 只有第一次结算奖励的时候才统计这个数量
//     * key: activity:richMan:dice:number:[fromUid]
//     * item: [toUid]
//     */
//    private final static String DICE_FREE_NUMBER = "activity:richMan:dice:free:number:%s";
//
//    /**
//     * 保存用户能够摇骰子 Hash 【即用户是否完成[发消息、抽奖等操作】
//     * key: activity:richMan:dice:lock
//     * item: [fromUid]_[toUid]
//     */
//    private final static String HAS_DICE_PLAY = "activity:richMan:dice:lock";
//
//    /**
//     * 处理摇骰子逻辑时的分布式锁 lock
//     * key: activity:richMan:dice:hasLock:[fromUid]_[toUid]
//     */
//    private final static String DICE_LOCK = "activity:richMan:dice:hasLock:%s";
//
//
//    /**
//     * 保存用户当前的骰子路径 hash
//     * key: activity:richMan:dice:way
//     * item: [fromUid]_[toUid]
//     */
//    private final static String DICE_WAY = "activity:richMan:dice:way";
//
//
//    /**
//     * 记录是否展示客户端蒙层  hash
//     * key: activity:richMan:dice:attend
//     * item: [uid]
//     */
//    private final static String HAS_ENTER_ACTIVITY = "activity:richMan:dice:attend";
//
//
//    /**
//     * 记录是否展示H5蒙层  hash
//     * key: activity:richMan:dice:web:attend
//     * item: [uid]
//     */
//    private final static String HAS_ENTER_WEB_ACTIVITY = "activity:richMan:dice:web:attend";
//
//
//    /**
//     * 双方一同完成奔赴的次数 hash
//     * key: activity:richMan:dice:finish
//     * item: [small_uid]_[big_uid]
//     */
//    private final static String DICE_FINISH_NUM = "activity:richMan:dice:finish";
//
//
//    /**
//     * 奔赴时候的时间戳，取最新的 hash
//     * key: activity:richMan:dice:finish
//     * item:  [small_uid]_[big_uid]
//     */
//    private final static String DICE_FINISH_NUM_TIMESTAMP = "activity:richMan:dice:finish:timestamp";
//
//
//    /**
//     * 亲密度差值记录 hash，用于送给用户的骰子
//     * key: activity:richMan:intimate:[fromUid]
//     * item : [toUid]
//     */
//    private final static String DICE_INTIMATE_DIFF = "activity:richMan:intimate:diff:%s";
//
//
//    /**
//     * 亲密度初始值记录 hash，
//     * key: activity:richMan:intimate:
//     * item : [small_uid]_[big_uid]
//     */
//    private final static String DICE_INTIMATE_INIT = "activity:richMan:intimate:init";
//
//    /**
//     * 恋爱基金 hash，
//     * key activity:richMan:intimate:coin:receiver:number:[fromUid]
//     * item :  [toUid]
//     */
//    private final static String DICE_INTIMATE_COIN_RECEIVER_NUMBER = "activity:richMan:intimate:coin:receiver:number:%s";
//
//    /**
//     * 恋爱宝箱 hash,
//     * key : activity:richMan:intimate:coin:box:number:[fromUid]
//     * item : [toUid]
//     */
//    private final static String DICE_INTIMATE_COIN_BOX_RECEIVER_NUMBER = "activity:richMan:intimate:coin:box:receiver:number:%s";
//
//
//    /**
//     * 金币领取记录 hash
//     * key: activity:richMan:intimate:coin:record:[fromUid]
//     * item: [toUid]
//     */
//    private final static String DICE_INTIMATE_COIN_RECORD = "activity:richMan:intimate:coin:record:%s";
//
//    /**
//     * 教育对象 hash
//     * <p>
//     * key: activity:richMan:intimate:lead
//     * item: [fromUid]
//     */
//    private final static String DICE_INTIMATE_LEAD = "activity:richMan:intimate:lead";
//
//    /**
//     * 倍率 Hash
//     * key: activity:richMan:double:magnification
//     * item: [small_uid]_[big_uid]
//     * value: "litter","mid","more"
//     */
//    private final static String DICE_MAGNIFICATION = "activity:richMan:double:magnification";
//
//
//    /**
//     * 心动关系（临时）
//     * key: activity:richMan:cardiac
//     * item: [fromUid]_[toUid]
//     */
//    private static final String DICE_CARDIAC = "activity:richMan:cardiac";
//
//
//    /**
//     * 存储给用户补发的金币
//     */
//    private static final String ACTIVITY_REPARATION = "activity:richMan:reparation";
//
//
//    /**
//     * 跳转私聊页面
//     */
//    private static final String IM_ROUTE = "https://router.nuan.chat/conversation?chat_id=%s";
//
//    /**
//     * 跳转活动主页
//     */
//    private static final String PAGE_ROUTE = "two-way?toUid=%s&from=npc_msg";
//
//
//    private static final String BUSINESSKEY = "RICHMAN_ACTIVITY";
//
//    private static final String BUSINESSTYPE = "RICHMAN_ACTIVITY";
//
//    /**
//     * 双方成功奔赴发送通知消息
//     */
//    private static final String SUCCESS_NOTIFY = "您与[%s]奔赴成功，抽取恋爱基金，<a href='%s'>点击查看。</a>";
//
//    private static final String SELF_SUCCESS_NOTIFY = "【双向奔赴】[%s]已到达终点，正在等着你呢 <a href='%s'>点击查看</a>";
//
//
//    private static final String SEND_DICE_NOTIFY = "【双向奔赴】您与[%s]获得骰子 参与双向奔赴 ，奔赴成功最高可返300%%的金币哦，点击 <a href='%s'>去看看</a>";
//
//    private static final String COIN_NOTIFY = "【双向奔赴】您已获得[金币]*%s,奔赴成功最高可返300%%的金币哦，点击 <a href='%s'>去看看</a>";
//
//    /**
//     * 亲密度最低限制
//     */
//    private final static Integer INTIMACY = 2;
//
//    /**
//     * 获取主页信息
//     *
//     * @param toUid
//     * @return
//     */
//    public DicePageInfoVO pageInfo(Long toUid, String chatId) {
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return DicePageInfoVO.builder().build();
//        }
//        Long fromUid = MDCUtil.getCurUserIdByMdc();
//        log.info("RichManager pageInfo Enter,fromUid: {},  toUid : {}, chatId : {}", fromUid, toUid, chatId);
//
//        //优先通过 chatId 获取
//        if (StringUtils.isNotEmpty(chatId)) {
//            //如果targetId 里包含临时会话，进行处理
//            if (chatId.contains("temp")) {
//                toUid = Long.parseLong(chatId.replaceAll("temp", ""));
//            } else {
//                // 通过chatId 去获取toUid
//                ImChatInfoVO imChatInfoVO = feignImService.getChatById(Long.parseLong(chatId)).successData();
//                log.info("RichManager pageInfo toUid is null,fromUid : {}, imChatInfoVO : {}", fromUid, imChatInfoVO);
//                if (fromUid.equals(imChatInfoVO.getUid())) {
//                    toUid = imChatInfoVO.getToUid();
//                } else if (fromUid.equals(imChatInfoVO.getToUid())) {
//                    toUid = imChatInfoVO.getUid();
//                }
//            }
//
//        }
//
//        if (Objects.isNull(toUid)) {
//            log.error("RichManager pageInfo toUid is null ,from: {}", fromUid);
//            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR);
//        }
//        if (Objects.equals(fromUid, toUid)) {
//            log.error("RichManager pageInfo from equals to ,from: {}, to :{}", fromUid, toUid);
//            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR);
//        }
//
//        log.info("RichManager pageInfo from: {}, to :{}", fromUid, toUid);
//        Long appId = MDCUtil.getCurAppIdByMdc();
//
//        //赠送骰子
//        Integer diceNum = sendUserDiceNumber(fromUid, toUid);
//
//        //获取自己位置信息
//        DiceModel selfModel = getDicePosition(fromUid, toUid);
//
//        //获取对方位置信息
//        DiceModel toUserModel = getDicePosition(toUid, fromUid);
//
//        //获取自己的信息
//        DiceUserInfoVO self = getUserInfo(selfModel, fromUid, toUid, appId);
//
//        //获取对方的信息
//        DiceUserInfoVO toUser = getUserInfo(toUserModel, toUid, fromUid, appId);
//
//
//        //获取恋爱基金
//        Integer coin = getLoveCoin(fromUid, toUid);
//
//        //获取恋爱宝箱
//        Integer box = getLoveBox(fromUid, toUid);
//
//        //是否展示教育蒙层
//        Boolean hasGuide = hasGuide(fromUid);
//
//        //恋爱基金能否领取,双方是否到达终点。恋爱基金有钱
//        Boolean hasLoveCoin = getLoveCoin(fromUid, toUid) > 0;
//
//        //恋爱宝箱能否领取
//        Boolean hasLoveBox = hasLoveBox(fromUid, toUid);
//
//        //恋爱宝箱还差几次解锁
//        Integer diffTimes = diffTimes(fromUid, toUid);
//
//        //已经领取的金币
//        Integer receiveCoin = getReceiveCoin(fromUid, toUid);
//
//        //是否展示奔赴成功弹框，
//        Boolean hasSuccess = doubleIsFinish(selfModel, toUserModel);
//
//        //是否达成心动关系
//        Boolean hasCardiac = hasCardiac(fromUid, toUid);
//
//        //客户端跳转路由
//        String route = getImRoute(fromUid, toUid);
//
//        //是否是教育对象
//        Boolean hasLead = hasLead(fromUid, toUid);
//
//        //成功奔赴次数
//        Integer successCount = getFinishNum(fromUid, toUid);
//
//        return DicePageInfoVO.builder()
//                .self(self)
//                .toUser(toUser)
//                .hasGuide(hasGuide)
//                .hasLead(hasLead)
//                .loveCoin(coin)
//                .loveBox(box)
//                .hasLoveCoin(hasLoveCoin)
//                .hasLoveBox(hasLoveBox)
//                .diffNum(diffTimes)
//                .receiveCoin(receiveCoin)
//                .hasSuccess(hasSuccess)
//                .hasCardiac(hasCardiac)
//                .route(route)
//                .receive(diceNum)
//                .successCount(successCount)
//                .build();
//
//    }
//
//    /**
//     * 掷骰子
//     *
//     * @param toUid
//     */
//    public DicePageInfoVO playDice(Long toUid) {
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return DicePageInfoVO.builder().build();
//        }
//
//        Long appId = MDCUtil.getCurAppIdByMdc();
//        Long fromUid = MDCUtil.getCurUserIdByMdc();
//
//        if (Objects.equals(fromUid, toUid)) {
//            log.error("RichManager playDice from equals to ,from: {}, to :{}", fromUid, toUid);
//            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR);
//        }
//
//        if (SystemNPC.LANLING_LITTLE.getUserId().equals(toUid)) {
//            log.error("RichManager playDice toUid is error ,from: {}, to :{}", fromUid, toUid);
//            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR, "去和亲密小伙伴玩游戏吧～");
//        }
//
//        log.info("playDice Enter from: {}, to :{}", fromUid, toUid);
//
//
//        //检查骰子能否使用
//        checkUserDicePlay(fromUid, toUid);
//
//        //是否达成心动关系
//        Boolean hasCardiac = hasCardiac(fromUid, toUid);
//
//        //投掷骰子的主要业务逻辑
//        DiceModel selfModel = playRichMan(fromUid, toUid);
//
//        DiceModel toModel = getDicePosition(toUid, fromUid);
//
//        //检查双方是否都到达终点，进行消息通知。
//        selfModel = bizFinish(fromUid, toUid, selfModel, toModel);
//
//        //获取位置信息
//        DiceUserInfoVO self = getUserInfo(selfModel, fromUid, toUid, appId);
//
//        DiceUserInfoVO toUser = getUserInfo(toModel, toUid, fromUid, appId);
//
//        //获取恋爱基金
//        Integer coin = getLoveCoin(fromUid, toUid);
//
//        //获取恋爱宝箱
//        Integer box = getLoveBox(fromUid, toUid);
//
//        //恋爱基金能否领取,双方是否到达终点
//        Boolean hasLoveCoin = doubleIsFinish(selfModel, toModel);
//
//        //恋爱宝箱能否领取
//        Boolean hasLoveBox = getLoveBox(fromUid, toUid) > 0;
//
//        //恋爱宝箱还差几次解锁
//        Integer diffTimes = diffTimes(fromUid, toUid);
//
//        //已经领取的金币
//        Integer receiveCoin = getReceiveCoin(fromUid, toUid);
//
//        //是否展示奔赴成功弹框，
//        Boolean hasSuccess = doubleIsFinish(selfModel, toModel);
//
//
//        //客户端跳转路由
//        String route = getImRoute(fromUid, toUid);
//
//        //成功奔赴次数
//        Integer successCount = getFinishNum(fromUid, toUid);
//
//        return DicePageInfoVO.builder()
//                .self(self)
//                .toUser(toUser)
//                .loveCoin(coin)
//                .loveBox(box)
//                .hasLoveCoin(hasLoveCoin)
//                .hasLoveBox(hasLoveBox)
//                .diffNum(diffTimes)
//                .receiveCoin(receiveCoin)
//                .hasSuccess(hasSuccess)
//                .route(route)
//                .hasCardiac(hasCardiac)
//                .successCount(successCount)
//                .build();
//
//    }
//
//
//    /**
//     * 活动人员列表
//     * 获取用户有亲密度相关的人员
//     * <p>
//     * 1. 从接口中获取用户亲密度大于2 的人员，从大到小排序
//     * 2. 组装数据返回前端
//     *
//     * @return
//     */
//    public PageResult<RichManUserVO> getRichManUser(Integer pageNo, Integer pageSize) {
//        Long uid = MDCUtil.getCurUserIdByMdc();
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return PageResult.of(pageNo, pageSize);
//        }
//
//        List<RichManUserVO> intimacyUserList = getIntimacyUserList(uid, 2, 100L);
//        // 手动分页
//        PageResult page = PageResult.of(pageNo, pageSize);
//        page.setResultList(getListPage(pageNo, pageSize, intimacyUserList));
//        page.setTotalCount(intimacyUserList.size());
//        return page;
//    }
//
//    private List<RichManUserVO> getListPage(Integer pageNo, Integer pageSize, List<RichManUserVO> list) {
//        if (CollectionUtils.isEmpty(list)) {
//            return Lists.newArrayList();
//        }
//        int totalCount = list.size();
//        pageNo = pageNo - 1;
//        int fromIndex = pageNo * pageSize;
//        if (fromIndex >= totalCount) {
//            return Lists.newArrayList();
//        }
//        int toIndex = ((pageNo + 1) * pageSize);
//        if (toIndex > totalCount) {
//            toIndex = totalCount;
//        }
//        return list.subList(fromIndex, toIndex);
//    }
//
//    private List<RichManUserVO> getIntimacyUserList(Long uid, Integer intimacy, Long limit) {
//        List<IntimacyUserInfoVO> intimacyInfos = Lists.newArrayList();
//
//        // from cache
//        String cacheStr = (String) redisManager.get(String.format("richman_intimacy_between_two_cache_%s_%s_%s", uid, intimacy, limit));
//        if (StringUtils.isNotBlank(cacheStr)) {
//            intimacyInfos = JSONObject.parseArray(cacheStr, IntimacyUserInfoVO.class);
//        }
//
//        // from coin
//        if (CollectionUtils.isEmpty(intimacyInfos)) {
////            intimacyInfos = feignCoinService.getUserInfoByLimitIntimacy(uid, intimacy, limit).successData();
//            if (!CollectionUtils.isEmpty(intimacyInfos)) {
//                redisManager.set(String.format("richman_intimacy_between_two_cache_%s_%s_%s", uid, intimacy, limit), JSONObject.toJSONString(intimacyInfos), RedisManager.ONE_MINUTE_SECONDS * 30);
//            }
//        }
//
//        if (CollectionUtils.isEmpty(intimacyInfos)) {
//            return Lists.newArrayList();
//        }
//
//        List<RichManUserVO> result = new ArrayList<>();
//        intimacyInfos.stream().forEach(item -> {
//            UserBasicVO user = item.getUser();
//            if (Objects.isNull(user)) {
//                return;
//            }
//            result.add(RichManUserVO.builder()
//                    .id(user.getId())
//                    .name(user.getName())
//                    .avatar(user.getAvatar())
//                    .sex(user.getSex())
//                    .number(item.getIntimacy().intValue())
//                    .remain(getUserDiceNumberByBetween(uid, item.getUser().getId())).build());
//        });
//        return result;
//    }
//
//    /**
//     * 抽取倍率
//     *
//     * @param toUid
//     * @return
//     */
//    public String magnification(Long toUid) {
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return "";
//        }
//
//        Long fromUid = MDCUtil.getCurUserIdByMdc();
//
//        DiceModel fromModel = getDicePosition(fromUid, toUid);
//        DiceModel toModel = getDicePosition(toUid, fromUid);
//        //判断两人到达终点，才可以抽取倍率
//        if (!doubleIsFinish(fromModel, toModel)) {
//            log.info("magnification is not finish, fromUid : {}, toUid : {},fromModel :{},toModel:{} ", fromUid, toUid, fromModel, toModel);
//            return "";
//        }
//
//        //检查抽取倍率的标识，为true 才可以抽取
//        if (!fromModel.getHasMagnification()) {
//            log.info("magnification is not magnification, fromUid : {},fromModel :{}, ", fromUid, fromModel);
//            return "";
//        }
//
//        //计算金额并存入基金和宝箱中
//        incrLoveCoinAndBox(fromUid, toUid, fromModel, toModel);
//
//        //关闭标识
//        fromModel.setHasMagnification(false);
//        saveDicePosition(fromUid, toUid, fromModel);
//
//        return getDiceMagnification(fromUid, toUid);
//    }
//
//    /**
//     * 领取活动奖品
//     * 两人都领取完毕之后，才能重置路径
//     *
//     * @param toUid
//     * @param type
//     * @return
//     */
//    public Integer reward(Long toUid, String type) {
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return 0;
//        }
//
//        Long fromUid = MDCUtil.getCurUserIdByMdc();
//        Long appId = MDCUtil.getCurAppIdByMdc();
//
//        DiceModel fromModel = getDicePosition(fromUid, toUid);
//        DiceModel toModel = getDicePosition(toUid, fromUid);
//        Integer reward = 0;
//
//
//        Map<String, Object> params = new HashMap<>();
//
//        if ("box".equals(type)) {
//            if (hasLoveBox(fromUid, toUid)) {
//                reward = getLoveBox(fromUid, toUid);
//
//                log.info("send boxCoin forUser, fromUid : {}, toUid : {}, reward : {}", fromUid, toUid, reward);
//                sendUserCoin(appId, fromUid, reward);
//                resetLoveBox(fromUid, toUid);
//                incrReceiveCoin(fromUid, toUid, reward);
//
//                //宝箱埋点
//                params.put("from", "luxury envelope");
//                params.put("much", reward);
//                params.put("type", "coin");
//                producerManager.umpTrack(fromUid, "love_receive_fund", params);
//                return reward;
//            }
//
//        }
//        //
//        // 恋爱金币和积分需要到达终点才能领取
//        // 检查是否到达终点
//        if (!doubleIsFinish(fromModel, toModel)) {
//            log.info("reward is not finish, fromUid : {}, toUid : {},fromModel :{},toModel:{} ", fromUid, toUid, fromModel, toModel);
//            return 0;
//        }
//
//        //不用检查是否领取过，领取过，原有都金额会清空的
//        log.info("reward fromUid : {}, toUid : {}", fromUid, toUid);
//        if ("coin".equals(type)) {
//            reward = getLoveCoin(fromUid, toUid);// 获取金额
//            sendUserCoin(appId, fromUid, reward); //发送金额
//            resetLoveCoin(fromUid, toUid); //重置金额
//            incrReceiveCoin(fromUid, toUid, reward);
//
//            //金币埋点
//            params.put("from", "ordinary envelope");
//            params.put("much", reward);
//            params.put("type", "coin");
//            producerManager.umpTrack(fromUid, "love_receive_fund", params);
//            checkSendCoinByBetween(fromUid, toUid, fromModel, toModel);
//        } else if ("point".equals(type)) {
//            if (hasUserSexWoman(fromUid)) {
//                reward = getLoveCoin(fromUid, toUid);
//                Integer point = reward * 22;//1 金币换 0.22 积分 ，积分要 扩大100倍
//
//                Integer count = reward; //消耗的金币
//
//                reward = point / 100; //显示的积分
//                sendUserPoint(appId, fromUid, point);
//                resetLoveCoin(fromUid, toUid);
//
//                //累计统计的金币
//                incrReceiveCoin(fromUid, toUid, count);
//
//                //积分埋点
//                params.put("from", "ordinary envelope");
//                params.put("much", point / 100);
//                params.put("type", "point");
//                producerManager.umpTrack(fromUid, "love_receive_fund", params);
//                checkSendCoinByBetween(fromUid, toUid, fromModel, toModel);
//            }
//
//        }
//
//
//        return reward;
//    }
//
//    private void checkSendCoinByBetween(Long fromUid, Long toUid, DiceModel fromModel, DiceModel toModel) {
//        //检查双方都领取完毕,重置对方路径,并且都得是抽过倍率的才可以
//        log.info("checkSendCoinByBetween fromUid : {}, toUid : {}", fromUid, toUid);
//        if (0 == getLoveCoin(fromUid, toUid) && 0 == getLoveCoin(toUid, fromUid)
//                && Boolean.FALSE.equals(fromModel.getHasMagnification())
//                && Boolean.FALSE.equals(toModel.getHasMagnification())) {
//            reset(fromUid, toUid);
//            reset(toUid, fromUid);
//            //重置倍率
//            resetDiceMagnification(fromUid, toUid);
//        }
//    }
//
//    /**
//     * 发送金币到用户账户
//     *
//     * @param fromUid
//     * @return
//     */
//    private Boolean sendUserCoin(Long appId, Long fromUid, Integer coin) {
//        String memoCoin = "【双向奔赴】您已获得[金币]*%s";
//
//        if (0 == coin) {
//            return true;
//        }
//        feignCoinService.updateAccountCoin(appId, fromUid, null, 0L, coin.longValue(), BUSINESSKEY, BUSINESSTYPE, String.format(memoCoin, coin), true);
//        return true;
//    }
//
//    /**
//     * 发送积分到用户账户
//     *
//     * @param fromUid
//     * @return
//     */
//    private Boolean sendUserPoint(Long appId, Long fromUid, Integer point) {
//        String memoPoint = "【双向奔赴】您已获得[积分]*%s";
//
//        if (0 == point) {
//            return true;
//        }
//        feignCoinService.updateAccountPoint(appId, fromUid, 0L, point.longValue(), BUSINESSKEY, BUSINESSTYPE, String.format(memoPoint, point / 100), PointFromType.activity.getType());
//        return true;
//    }
//
//    private Boolean hasUserSexWoman(Long uid) {
//        UserBaseVO userBaseVO = feignUserService.getUserBaseVO(ServicesAppIdEnum.lanling.getAppId(), uid).successData();
//        if (Objects.nonNull(userBaseVO)) {
//            return SexType.WOMAN.getCode().equals(userBaseVO.getSex().getCode());
//        }
//        return false;
//    }
//
//    /**
//     * 获取是否展示客户端蒙层
//     *
//     * @param userIds
//     */
//    public IntiMacyUserVO hasIntimacy(String userIds) {
//        log.info("Enter hasIntimacy, userIds : {}", userIds);
//        IntiMacyUserVO intiMacyUserVO = IntiMacyUserVO.builder().path("").build();
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return intiMacyUserVO;
//        }
//
//        if (StringUtils.isEmpty(userIds)) {
//            log.error("Enter hasIntimacy input is null ");
//            userIds = "100000";
//        }
//
//        List<Long> ids = Arrays.asList(userIds.split(",")).stream().map(item -> Optional.of(Long.parseLong(item)).orElse(0L)).collect(Collectors.toList());
//        Long uid = MDCUtil.getCurUserIdByMdc();
//
//        //如果客户端进入过蒙层，则不展示蒙层
//        if (Boolean.TRUE.equals(redisManager.hget(HAS_ENTER_ACTIVITY, uid.toString()))) {
//            log.info("Enter hasIntimacy return is null , uid  : {},userIds : {}", uid, userIds);
//            return intiMacyUserVO;
//        } else {
//            //展示一次蒙层就不再展示了
//            log.info("Enter hasIntimacy show intimacy, uid  : {},userIds : {}", uid, userIds);
//            redisManager.hset(HAS_ENTER_ACTIVITY, uid.toString(), Boolean.TRUE);
//            //获取教育对象
//
//            Long educationId = getEducationObject(uid, ids);
//
//            if (educationId == null) {
//                //没有教育对象
//                log.info("educationId is null, uid : {}", uid);
//                intiMacyUserVO.setPath("secondlayer");
//                return intiMacyUserVO;
//            } else {
//                //有教育对象，但是骰子数量是0，所以也是蒙层2
//                Integer number = getUserDiceNumberByBetween(uid, educationId);
//
//                log.info("educationId , uid : {}, number : {}", uid, number);
//                if (number < 1) {
//                    intiMacyUserVO.setPath("secondlayer");
//                    return intiMacyUserVO;
//                } else {
//                    //
//                    intiMacyUserVO.setPath("firstLayer");
//                    intiMacyUserVO.setUid(educationId);
//                    return intiMacyUserVO;
//                }
//
//            }
//
//        }
//    }
//
//
//    /**
//     * 用户之间进行消息发送
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    public void userChatHandle(Long fromUid, Long toUid) {
//        if (!activityIsEnable()) {
//            return;
//        }
//        Boolean hasDice = hasDicePositionFinish(fromUid, toUid);
//
//        log.info("userChatHandle  fromUid : {},toUid : {},hasDice: {} ", fromUid, toUid, hasDice);
//        if (!hasDice) {
//            //如果行为未完成，检查是不是聊天类型，聊天类型才🔓
//            DiceModel diceModel = getDicePosition(fromUid, toUid);
//
//            log.info("userChatHandle check dice  fromUid : {},toUid : {},diceModel : {}", fromUid, toUid, diceModel);
//            //检查聊天类型
//            if (Objects.equals(RouteGridEnum.I.getIndex(), diceModel.getLocal()) || Objects.equals(RouteGridEnum.VIII.getIndex(), diceModel.getLocal())) {
//                activeFinish(fromUid, toUid);
//            }
//        }
//    }
//
//
//    /**
//     * 更新用户亲密度
//     *
//     * @param fromUid
//     * @param toUid
//     * @param oldIntimacy
//     * @param newIntimacy
//     */
//    public void updateIntimate(Long fromUid, Long toUid, Double oldIntimacy, Double newIntimacy) {
//        if (!activityIsEnable()) {
//            return;
//        }
//        Integer num = newIntimacy.intValue() - oldIntimacy.intValue();
//
//        log.info("update Intimate fromUid : {}, toUid : {}, num : {}, key : {}", fromUid, toUid, num, DICE_INTIMATE_DIFF);
//        redisManager.hincr(String.format(DICE_INTIMATE_DIFF, fromUid), toUid.toString(), num);
//        redisManager.hincr(String.format(DICE_INTIMATE_DIFF, toUid), fromUid.toString(), num);
//    }
//
//    /**
//     * 打开 IM 页处理（更新浮标数字）
//     *
//     * @param toUid
//     * @return java.lang.Boolean
//     */
//    public Boolean openImChatHandle(Long toUid) {
//        if (!activityIsEnable()) {
//            return false;
//        }
//        return true;
//
//        // 获取活动上架的资源
////        List<BuoyInfoItem> buoyItems = Lists.newArrayList(BuoyInfoItem.builder()
////                .activityCode("520_activity_chat_buoy_20220516")
////                .showNum(getUserDiceNumberByBetween(uid, toUid))
////                .build());
////
////        // 推送修改
////        activityAbilityManager.updateBuoyInfo(uid, buoyItems);
////        return true;
//    }
//
//    /**
//     * 活动登录弹窗
//     *
//     * @param uid
//     * @return java.lang.Boolean
//     */
//    public Boolean loginHandle(Long uid) {
//        if (!activityIsEnable()) {
//            return false;
//        }
//        log.info("richman 活动登录处理 uid:{}", uid);
////        popManager.pop(ServicesAppIdEnum.lanling.getAppId(), ServicesAppIdEnum.lanling.getUnionId(), getActivityCode(), uid);
//        return true;
//    }
//
//
//    /**
//     * 获取奔赴结果
//     * 奔赴记录
//     * <p>
//     * 获取用户所有的奔赴记录相关
//     * redis key :
//     * DICE_FINISH_NUM 保存用户的奔赴记录 item [smallUid]_[bigUid]
//     * 1. DICE_NUMBER 获取用户之间的所有骰子，根据骰子取到 toUid
//     * 2. 获取用户所有的奔赴记录，进行统计
//     *
//     * @return
//     */
//    public List<DiceRecordVO> getUserSuccess() {
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return new ArrayList<>();
//        }
//
//
//        Long fromUid = MDCUtil.getCurUserIdByMdc();
//        List<DiceRecordVO> result = Lists.newArrayList();
//
//        // 获取用户骰子 hash
//        Map<Object, Object> diceNumberInfo = redisManager.hmget(String.format(DICE_NUMBER, fromUid));
//
//        // 遍历所有存在骰子数量的用户
//        for (Map.Entry<Object, Object> toUserEntry : diceNumberInfo.entrySet()) {
//            // 拼接 key 依次获取奔赴记录
//            Long toUid = Long.parseLong((String) toUserEntry.getKey());
//            Integer diceNum = (Integer) toUserEntry.getValue();
//            Integer finishNum = (Integer) redisManager.hget(String.format(DICE_FINISH_NUM), getQuiteKey(fromUid, toUid));
//            UserVO toUser = userRemoteService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId(), true);
//            if (Objects.nonNull(finishNum) && finishNum >= 1) {
//                LocalDateTime localDateTime = getFinishNumTime(fromUid, toUid);
//
//                result.add(DiceRecordVO.builder().id(toUser.getId()).name(toUser.getName()).avatar(toUser.getAvatar()).sex(toUser.getSex().getDesc()).number(finishNum).remain(getUserDiceNumberByBetween(fromUid, toUser.getId())).remain(Optional.ofNullable(diceNum).orElse(0)).finishDate(localDateTime.toLocalDate().toString()).finishTime(localDateTime.toLocalTime().toString()).build());
//            }
//        }
//        return result;
//    }
//
//    /**
//     * 获取两个人之间的骰子数量
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    public Integer getUserDiceNumberByBetween(Long fromUid, Long toUid) {
//        log.info("getUserDiceNumberByBetween info fromUid : {}, toUid : {},env : {}", fromUid, toUid, env);
//        int defaultNum = 0;
//
//        if (!activityIsEnable()) {
//            log.warn("520 活动尚未开始呢！");
//            return 0;
//        }
//
//        if (fromUid == null || toUid == null || toUid.equals(SystemNPC.LANLING_LITTLE.getAppId())) {
//            return defaultNum;
//        }
//        String diceNumKey = String.format(DICE_NUMBER, fromUid);
//
//        int num = NumberUtil.getInt(redisManager.hget(diceNumKey, toUid.toString()), defaultNum);
//        log.info("getUserDiceNumberByBetween result : {} , fromUid : {}, toUid : {}, key : {}", num, fromUid, toUid, diceNumKey);
//        return num;
//    }
//
//    /**
//     * 获取骰子位置的行为是否完成【发消息、抽奖、金币】
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    public Boolean hasDicePositionFinish(Long fromUid, Long toUid) {
//        if (!activityIsEnable()) {
//            return false;
//        }
//        Object obj = redisManager.hget(HAS_DICE_PLAY, getConcatKey(fromUid, toUid));
//
//        log.info("hasDicePositionFinish, fromUid : {}, toUid : {} obj : {}", fromUid, toUid, obj);
//        if (Objects.isNull(obj)) {
//            return true;
//        }
//        return Boolean.TRUE.equals(obj);
//    }
//
//
//    /**
//     * 是否达成心动关系
//     * <p>
//     * 1. 在 play 方法中，检测位置是心动抽奖
//     * 2. 设置 redis 属性为 true 表示通过。
//     * 3. 在play 当次会获取到更新之前的值
//     * 4. 后续都会获取到更新之后到值
//     * 5. 加入 redis 中的值是 true，则不获取真实的心动关系，否则使用蓝领的心动关系
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    public Boolean hasCardiac(Long fromUid, Long toUid) {
//
//        log.info("Enter hasCardiac ,fromUid : {}, toUid : {}", fromUid, toUid);
//        Boolean result = getCardiac(fromUid, toUid);
//
//        if (result) {
//            return result;
//        } else {
//            log.info("Enter hasUserCardiacRelationship ,fromUid : {}, toUid : {}", fromUid, toUid);
//            return feignLanlingService.hasUserCardiacRelationship(fromUid, toUid).successData();
//        }
//    }
//
////    public Boolean hasCardiac(Long fromUid, Long toUid) {
////        log.info("Enter hasCardiac ,fromUid : {}, toUid : {}", fromUid, toUid);
////        Boolean result = Boolean.FALSE;
////
////        // 获取是否结成心动关系
////        Boolean cardiac = Optional.ofNullable(feignLanlingService.hasUserCardiacRelationship(fromUid, toUid).successData()).orElse(Boolean.FALSE);
////        log.info("debug- cardiac:{}", cardiac);
////
////
////        // 没结成 & 没弹过
////        if (!cardiac && Objects.isNull(redisManager.hget(CARDIAC_POP_LOG_DAILY, getConcatKey(fromUid, toUid)))) {
////            result = Boolean.TRUE;
////            log.info("debug- result");
////        }
////
////        // 弹出
////        if (result) {
////            log.info("debug- 弹出");
////            // 记录弹出日志
////            redisManager.hset(CARDIAC_POP_LOG_DAILY, getConcatKey(fromUid, toUid), RedisManager.ONE_DAY_SECONDS * 30);
////        }
////        return !result;
////    }
//
//    public Boolean getCardiac(Long fromUid, Long toUid) {
//        return Boolean.TRUE.equals(redisManager.hget(DICE_CARDIAC, getConcatKey(fromUid, toUid)));
//    }
//
//    public void setCardiac(Long fromUid, Long toUid) {
//        redisManager.hset(DICE_CARDIAC, getConcatKey(fromUid, toUid), Boolean.TRUE);
//    }
//
//
//    /**
//     * 活动指定行为完成
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    public void activeFinish(Long fromUid, Long toUid) {
//        log.info("RichManager activeFinish , fromUid : {}, toUid : {}", fromUid, toUid);
//        setHasDicePlay(fromUid, toUid, Boolean.TRUE);
//
//    }
//
//
//    /**
//     * 判断是否双方是否奔赴成功
//     *
//     * @param fromDice
//     * @param toDice
//     * @return
//     */
//    private Boolean doubleIsFinish(DiceModel fromDice, DiceModel toDice) {
//
//        return Boolean.TRUE.equals(fromDice.getHasFinish()) && (Boolean.TRUE.equals(toDice.getHasFinish()));
//    }
//
//
//    /**
//     * 判断是否是教育对象
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private Boolean hasLead(Long fromUid, Long toUid) {
//        return Objects.equals(toUid, getEducationObject(fromUid, null));
//    }
//
//
//    /**
//     * 获取是否展示H5蒙层
//     * true 表示展示弹框、false 表示不展示
//     *
//     * @param fromUid
//     * @return
//     */
//    private Boolean hasGuide(Long fromUid) {
//        boolean hasGuide = !Boolean.TRUE.equals(redisManager.hget(HAS_ENTER_WEB_ACTIVITY, fromUid.toString()));
//
//        log.info("RichManager fromUid : {},hasGuide : {}", fromUid, hasGuide);
//        redisManager.hset(HAS_ENTER_WEB_ACTIVITY, fromUid.toString(), Boolean.TRUE);
//
//        return hasGuide;
//    }
//
//
//    /**
//     * 终点业务逻辑处理
//     * <p>
//     * <p>
//     * 1.对于终点，需要锁定。只有两人都成功奔赴才可以解锁
//     * 2.奔赴成功需要做的事情：设置转盘的值为true
//     * 3.
//     *
//     * @param fromUid
//     * @param toUid
//     * @param selfModel
//     * @param toModel
//     */
//    private DiceModel bizFinish(Long fromUid, Long toUid, DiceModel selfModel, DiceModel toModel) {
//        log.info("bizFinish Enter fromUid : {}, toUid : {}, self : {}, toModel : {}", fromUid, toModel, selfModel, toModel);
//        if (doubleIsFinish(selfModel, toModel)) {
//            //1. 锁定用户的行为
//            log.info("bizFinish fromUid: {}, toUid : {}, selfModel: {}, toModel : {}", fromUid, toModel, selfModel, toModel);
//            setHasDicePlay(fromUid, toUid, Boolean.FALSE);
//            setHasDicePlay(toUid, fromUid, Boolean.FALSE);
//
//            // 2. 发送领取通知
//            notifyUserFinish(fromUid, toUid);
//
//            // 3. 累计用户奔赴次数
//            incrDiceFinishNum(fromUid, toUid);
//
//            //4. 设置弹框
//            selfModel.setHasMagnification(true);
//            toModel.setHasMagnification(true);
//            saveDicePosition(fromUid, toUid, selfModel);
//            saveDicePosition(toUid, fromUid, toModel);
//
//            //消息埋点
//            Map<String, Object> params = new HashMap<>();
//            if (hasLoveBox(fromUid, toUid)) {
//                params.put("type", 10);
//            } else {
//                params.put("type", 1);
//            }
//            producerManager.umpTrack(fromUid, "love_go_success", params);
//        } else {
//            //检查当前用户是否到终点，到终点给双方进行通知
//            if (selfModel.getHasFinish()) {
//                log.info("bizFinish lock, selfModel : {}, fromUid : {}", selfModel, fromUid);
//                setHasDicePlay(fromUid, toUid, Boolean.FALSE);
//
//                //给对方发送通知消息
//                npcNotify(toUid, String.format(SELF_SUCCESS_NOTIFY, getUserName(fromUid), getPageRoute(fromUid)));
//
//                //消息埋点
//                Map<String, Object> params = new HashMap<>();
//                producerManager.umpTrack(fromUid, "love_goes_success", params);
//            }
//
//        }
//
//        return selfModel;
//    }
//
//    private void notifyUserFinish(Long fromUid, Long toUid) {
//        log.info("notifyUserFinish fromUid : {}, toUid : {}", fromUid, toUid);
//        npcNotify(fromUid, String.format(SUCCESS_NOTIFY, getUserName(toUid), getPageRoute(toUid)));
//        npcNotify(toUid, String.format(SUCCESS_NOTIFY, getUserName(fromUid), getPageRoute(fromUid)));
//    }
//
//    private String getUserName(Long uid) {
//        log.info("RichManager getUserName uid : {}", uid);
//        UserBaseVO userBaseVO = feignUserService.getUserBaseVO(ServicesAppIdEnum.lanling.getAppId(), uid).successData();
//        if (Objects.nonNull(userBaseVO)) {
//            return userBaseVO.getName();
//        }
//        return "";
//    }
//
//
//    /**
//     * 获取教育对象
//     *
//     * @param fromUid
//     * @param ids
//     * @return
//     */
//    private Long getEducationObject(Long fromUid, List<Long> ids) {
//        log.info("getEducationObject fromUid : {} ,ids : {}", fromUid, ids);
//
//        //优先从redis 中获取教育对象
//        Object object = redisManager.hget(DICE_INTIMATE_LEAD, fromUid.toString());
//        if (Objects.isNull(object)) {
//            Long leadId = null;
//
//            //优先从ids 中获取
//            if (ids != null && ids.size() > 0) {
//                //去掉小助手
//                ids.remove(100000L);
//
//                Long appId = ServicesAppIdEnum.lanling.getAppId();
//                List<Long> intimacys = feignCoinService.getGreaterThanIntimacyFromUidList(appId, fromUid, JSON.toJSONString(ids), INTIMACY).successData();
//                if (intimacys == null || intimacys.size() == 0) {
//                    log.warn("getEducationObject info size is null, fromUid : {},ids : {}", fromUid, ids);
//                    //没有拉倒
//                } else {
//                    log.info("getEducationObject info , fromUid : {},ids : {},intimacys : {}", fromUid, ids, intimacys);
//                    leadId = intimacys.get(0);
//                }
//            } else {
//                //获取用户亲密度最大的大哥
//                Map<String, Object> map = feignCoinService.getMaxIntimacy(fromUid).successData();
//                if (map.size() == 0) {
//                    log.warn("getEducationObject info size is null, fromUid : {}", fromUid);
//                } else {
//                    leadId = Long.parseLong(map.get("uid").toString());
//                }
//            }
//            if (leadId == null) {
//                return null;
//            } else {
//                //保存教育对象
//                log.info("save education info Object : {}, fromUid :{}", leadId, fromUid);
//                redisManager.hset(DICE_INTIMATE_LEAD, fromUid.toString(), leadId);
//                return leadId;
//            }
//        } else {
//            log.info("getEducationObject info fromUid : {},object : {}", fromUid, object);
//            return Long.parseLong(object.toString());
//        }
//
//
//    }
//
//
//    /**
//     * 设置免费骰子数量
//     *
//     * @param fromUid
//     * @param toUid
//     * @param num
//     */
//    private void setDiceFreeNumber(Long fromUid, Long toUid, Integer num) {
//        log.info("RichManager setDiceFreeNumber, fromUid : {}, toUid : {}, num : {}", fromUid, toUid, num);
//
//        //设置免费骰子数量
//        redisManager.hset(String.format(DICE_FREE_NUMBER, fromUid), toUid.toString(), num);
//        redisManager.hset(String.format(DICE_FREE_NUMBER, toUid), fromUid.toString(), num);
//
//        //设置骰子数量
//        redisManager.hincr(String.format(DICE_NUMBER, fromUid), toUid.toString(), num);
//        redisManager.hincr(String.format(DICE_NUMBER, toUid), fromUid.toString(), num);
//
//
//    }
//
//    /**
//     * 用户赠送骰子
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    private Integer sendUserDiceNumber(Long fromUid, Long toUid) {
//        log.info("sendUserDiceNumber fromUid : {}, toUid : {}", fromUid, toUid);
//        //第一次进入活动领取骰子
//        Object obj = redisManager.hget(String.format(DICE_FREE_NUMBER, fromUid), toUid.toString());
//        if (Objects.isNull(obj)) {
//            //如果 obj 是空的，说明没有领取过骰子
//            //执行领取骰子的逻辑
//
//            //读取亲密度的初始值
//            obj = redisManager.hget(DICE_INTIMATE_INIT, getQuiteKey(fromUid, toUid));
//            Double de;
//            if (Objects.isNull(obj)) {
//                //如果没有亲密度的初始值,就调用接口去查询
//                de = Double.valueOf(feignCoinService.getIntimacy(fromUid, toUid, ServicesAppIdEnum.lanling.getAppId()).successData().toString());
//            } else {
//                de = Double.valueOf(obj.toString());
//            }
//            log.info("sendUserDiceNumber double : {},fromUid : {},toUid : {}", de, fromUid, toUid);
//            redisManager.hset(DICE_INTIMATE_INIT, getQuiteKey(fromUid, toUid), de);
//
//            //设置免费赠送的骰子
//            Integer number = 0;
//            if (de < 2) {
//
//            } else if (de < 10) {
//                number = 1;
//            } else if (de < 500) {
//                number = 2;
//            } else {
//                number = 3;
//            }
//
//            setDiceFreeNumber(fromUid, toUid, number);
//
//            //通知客户端
//            log.info("notify cancelPropagandaList , fromUid : {}, toUid : {}, num :{}", fromUid, toUid, number);
//            cancelPropagandaList(fromUid, toUid);
//            return number;
//        } else {
//            //检验亲密度送骰子
//            Double value = Optional.ofNullable(NumberUtil.getInt(redisManager.hget(String.format(DICE_INTIMATE_DIFF, fromUid), toUid.toString()), 0).doubleValue()).orElse(0D);
//
//            return getDiceNumberByIntimate(value, fromUid, toUid);
//        }
//
//    }
//
//    /**
//     * 根据梯度赠送骰子，
//     * 成功奔赴次数	获得骰子一个骰子
//     * X≤5次	每增加5度
//     * 5次<X≤20次	每增加10度
//     * 20次<X	每增加30度
//     *
//     * @param value
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private Integer getDiceNumberByIntimate(Double value, Long fromUid, Long toUid) {
//        log.info("getDiceNumberByIntimate ,fromUid : {}, toUid : {}, value :{}", fromUid, toUid, value);
//
//        //获取成功奔赴次数
//        Integer times = getFinishNum(fromUid, toUid);
//
//        Integer denominator = 5;
//
//        if (times > 5) {
//            denominator = 10;
//        }
//
//        int number = 0;
//        String lockKey = String.format(DICE_LOCK, getConcatKey(fromUid, toUid));
//        Lock lock = redissonClient.getLock(lockKey);
//        lock.lock();
//        try {
//
//            //获取亲密度差值
//            String key = String.format(DICE_INTIMATE_DIFF, fromUid);
//            Integer count = NumberUtil.getInt(redisManager.hget(key, toUid.toString()), 0);
//
//            //清理亲密度差值
//            number = (count / denominator);
//
//            log.info("getDiceNumberByIntimate setNumber  ,fromUid : {}, toUid : {}, number : {}, denominator : {}, count : {}", fromUid, toUid, number, denominator, count);
//            redisManager.hincr(key, toUid.toString(), -(number * denominator));
//
//            //下发骰子
//            redisManager.hincr(String.format(DICE_NUMBER, fromUid), toUid.toString(), number);
//
//        } catch (Exception e) {
//            log.error("getDiceNumberByIntimate is error,fromUid : {}, toUid : {}, ", fromUid, toUid, e);
//        } finally {
//            lock.unlock();
//        }
//
//        if (number > 0) {
//            //通知客户端更新
//            log.info("notify cancelPropagandaList , fromUid : {}, toUid : {}, num :{}", fromUid, toUid, number);
//            cancelPropagandaList(fromUid, toUid);
//            //发送消息小助手
//            String fromName = getUserName(fromUid);
//            String toName = getUserName(toUid);
//
//            log.info("notify cancelPropagandaList info , fromName : {},toName : {}, format : {}", fromName, toName, SEND_DICE_NOTIFY);
//            String fromMsg = String.format(SEND_DICE_NOTIFY, fromName, getPageRoute(fromUid));
//            String toMsg = String.format(SEND_DICE_NOTIFY, toName, getPageRoute(toUid));
//
//            npcNotify(fromUid, toMsg);
//            npcNotify(toUid, fromMsg);
//        }
//        return number;
//    }
//
//
//    /**
//     * 投掷骰子的位置变动处理
//     * <p>
//     * 处理骰子位置变动
//     * 处理骰子数量变动
//     * 处理骰子状态变动
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private DiceModel playRichMan(Long fromUid, Long toUid) {
//
//        log.info("RichManager playRichMan, fromUid : {}, toUid : {} ", fromUid, toUid);
//        DiceModel diceModel = getDicePosition(fromUid, toUid);
//        String concatKey = getConcatKey(fromUid, toUid);
//        //掷骰子
//        String lockKey = String.format(DICE_LOCK, concatKey);
//        Lock lock = redissonClient.getLock(lockKey);
//        lock.lock();
//        try {
//            log.info("playRichMan lock diceModel : {}", diceModel);
//            //获取骰子当前路径类别
//            List<Integer> localWay = DiceWayEnum.getDiceWay(diceModel.getWayCategories());
//            //获取骰子下一步路径的点数
//            Integer wayIndex = diceModel.getNextWay();
//
//            //获取骰子当前位置
//            Integer position = diceModel.getLocal();
//
//            List<Integer> preWay = diceModel.getLocalWay();
//
//            //获取当前路径的点数
//            Integer localNum = localWay.get(wayIndex);
//            // 获取运动轨迹
//            List<Integer> wayItem = doWayItem(position, localNum);
//
//            //终点位置
//            Integer finish = wayItem.get(wayItem.size() - 1);
//
//            //获取终点位置类型
//            RouteGridEnum diceType = RouteGridEnum.getInstanceByIndex(finish);
//
//            //如果不是普通类型的格子，就需要执行一些动作[发消息、抽奖、抽金币]
//            Boolean status = Boolean.TRUE;
//            if (!diceType.getType().equals(RouteGridEnum.getDefaultType())) {
//                status = Boolean.FALSE;
//            }
//            //锁定位置，不允许再掷骰子。直到解锁🔒
//            setHasDicePlay(fromUid, toUid, status);
//
//            //骰子数量 -1
//            log.info("playRichMan dice number decrement,fromUid : {}, toUid : {}", fromUid, toUid);
//            redisManager.hincr(String.format(DICE_NUMBER, fromUid), toUid.toString(), -1);
//
//            //判断骰子位置情况
//            Boolean hasFinish = Boolean.FALSE;
//            if (diceType.getIndex() >= RouteGridEnum.XII.getIndex()) {
//                hasFinish = Boolean.TRUE;
//            }
//
//            //处理心动弹框
//            if (diceType.getIndex() == RouteGridEnum.VII.getIndex()) {
//                setCardiac(fromUid, toUid);
//            }
//
//            //更新位置信息
//            diceModel.setNextWay(wayIndex + 1);
//            diceModel.setLocalWay(wayItem);
//            diceModel.setLocal(finish);
//            diceModel.setShow(Boolean.FALSE);
//            diceModel.setHasFinish(hasFinish);
//            diceModel.setPreWay(preWay);
//            diceModel.setLocalNum(localNum);
//
//            //保存骰子位置信息
//            saveDicePosition(fromUid, toUid, diceModel);
//        } catch (Exception e) {
//            log.error("playRichMan is error , fromUid : {}, toUid : {}", fromUid, toUid, e);
//        } finally {
//            lock.unlock();
//        }
//        log.info("playRichMan end diceModel : {}", diceModel);
//        //通知客户端更新骰子数量
//        cancelPropagandaList(fromUid, toUid);
//
//        //骰子埋点
//        playDiceTrack(fromUid, toUid, diceModel);
//
//        return diceModel;
//    }
//
//
//    private void playDiceTrack(Long fromUid, Long toUid, DiceModel diceModel) {
//        Map<String, Object> params = new HashMap<>();
//
//        params.put("identity", getTrack(fromUid, toUid));
//
//        if (getFinishNum(fromUid, toUid) == 0) {
//            int number = NumberUtil.getInt(redisManager.hget(String.format(DICE_FREE_NUMBER, fromUid), toUid.toString()), 0);
//            if (number >= diceModel.getNextWay()) {
//                //免费骰子埋点
//                params.put("type", "give away");
//            } else {
//                //付费骰子埋点
//                params.put("type", "pay");
//            }
//        } else {
//            //付费骰子埋点
//            params.put("type", "pay");
//        }
//
//        producerManager.umpTrack(fromUid, "love_click_shake", params);
//    }
//
//    private String getTrack(Long fromUid, Long toUid) {
//        //已结成心动关系
//        if (Boolean.TRUE.equals(feignLanlingService.hasUserCardiacRelationship(fromUid, toUid).successData())) {
//            return "relationship";
//        } else {
//            //未结成心动关系
//            return "not relationship";
//        }
//    }
//
//
//    private void setHasDicePlay(Long fromUid, Long toUid, Boolean status) {
//        log.info("setHasDicePlay HAS_DICE_PLAY ,fromUid : {}, toUid : {}, status : {}", fromUid, toUid, status);
//        redisManager.hset(HAS_DICE_PLAY, getConcatKey(fromUid, toUid), status);
//    }
//
//    /**
//     * 获取位置展示信息
//     *
//     * @param diceModel
//     * @param appId
//     * @return
//     */
//    private DiceUserInfoVO getUserInfo(DiceModel diceModel, Long fromUid, Long toUid, Long appId) {
//        //获取位置信息
//        Integer local = diceModel.getLocal();
//        List<Integer> way = diceModel.getLocalWay();
//
//        boolean status = true;
//
//        String type = RouteGridEnum.getInstanceByIndex(local).getType();
//
//        //如果是无动作的格子，status 的状态不变化
//        if (!RouteGridEnum.ZERO.getType().equals(type)) {
//            status = hasDicePositionFinish(fromUid, toUid);
//        }
//
//        DiceUserInfoVO infoVO = DiceUserInfoVO.builder()
//                .index(local)
//                .way(way)
//                .finish(diceModel.getLocal())
//                .user(feignUserService.getBasic(fromUid, appId).successData())
//                .remain(getUserDiceNumberByBetween(fromUid, toUid))
//                .type(type)
//                .status(status)
//                .number(diceModel.getLocalNum())
//                .hasDraw(diceModel.getHasMagnification())
//                .build();
//        log.info("RichManager getOtherUserInfo info : {}", infoVO);
//        return infoVO;
//
//    }
//
//
//    /**
//     * 获取骰子的位置
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private DiceModel getDicePosition(Long fromUid, Long toUid) {
//        //获取骰子的信息和位置
//        String concatKey = getConcatKey(fromUid, toUid);
//        Object obj = redisManager.hget(DICE_WAY, concatKey);
//        DiceModel diceModel = null;
//        if (Objects.nonNull(obj)) {
//            diceModel = JSON.toJavaObject((JSONObject) obj, DiceModel.class);
//        }
//        if (Objects.isNull(diceModel)) {
//            diceModel = DiceModel.getInstance();
//        }
//        log.info("getDicePosition, fromUid : {}, toUid : {} diceModel : {}", fromUid, toUid, diceModel);
//        return diceModel;
//    }
//
//    /**
//     * 保存骰子的位置
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private void saveDicePosition(Long fromUid, Long toUid, DiceModel diceModel) {
//        //获取骰子的信息和位置
//        String concatKey = getConcatKey(fromUid, toUid);
//        if (diceModel == null) {
//            diceModel = DiceModel.getInstance();
//        }
//        log.info("saveDicePosition diceModel : {}", diceModel);
//        redisManager.hset(DICE_WAY, concatKey, diceModel);
//    }
//
//    /**
//     * 获取活动主页
//     *
//     * @param toUid
//     * @return
//     */
//    public String getPageRoute(Long toUid) {
//        return LanlingActivityUtil.getH5BaseUrl(env).concat(String.format(PAGE_ROUTE, toUid));
//    }
//
//    private String getImRoute(Long fromUid, Long toUid) {
//        log.info("getImRoute  Enter fromUid : {}, toUid : {}", fromUid, toUid);
//        ImChatInfoVO chatInfoVO = feignImService.getChat(ServicesAppIdEnum.lanling.getAppId(), fromUid, toUid).successData();
//        if (Objects.nonNull(chatInfoVO)) {
//            log.info("getImRoute, fromUid : {}, toUid : {}, chatInfo : {}", fromUid, toUid, chatInfoVO);
//            return String.format(IM_ROUTE, chatInfoVO.getId().toString());
//        } else {
//            return null;
//        }
//    }
//
//
//    /**
//     * 检查用户是否能掷骰子
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    private void checkUserDicePlay(Long fromUid, Long toUid) {
//
//        //检查骰子的数量
//        int diceNum = getUserDiceNumberByBetween(fromUid, toUid);
//        if (diceNum < 1) {
//            //骰子数量不足，不能掷骰子
//            log.warn("RichManager checkUserDicePlay dice is Not enough , fromUid: {},toUid :{} ,num : {}", fromUid, toUid, diceNum);
//            throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180033);
//        }
//
//        //2. 判断状态是否完成，没完成也不允许掷骰子
//        if (!hasDicePositionFinish(fromUid, toUid)) {
//            //状态没完成,不允许掷骰子
//            throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180034);
//        }
//    }
//
//    /**
//     * 投掷骰子的运动轨迹
//     *
//     * @param index  骰子位置
//     * @param number 骰子点数
//     * @return
//     */
//    private List<Integer> doWayItem(Integer index, Integer number) {
//        if (number < 1 || number > 6 || index < 0 || index > 11) {
//            log.error("RichManager doWay error, index :{} ,number :{}", index, number);
//            throw new ServiceException(ErrorCode.ACTIVITY_PARAM_ERROR);
//        }
//        List<Integer> list = new ArrayList<>();
//
//        //投掷骰子到达的位置
//        Integer endpoint = 12;
//        Integer tag = index + number;
//
//        //如果到达终点
//        if (tag >= endpoint) {
//            tag = endpoint;
//        }
//        for (int i = index; i < tag; ) {
//            list.add(++i);
//        }
//
//        log.info("RichManager doWay way : {}", list);
//        Integer finish = RouteGridEnum.getInstanceByIndex(tag).getFinish();
//        if (finish >= tag) {
//            for (int i = tag; i < finish; ) {
//                list.add(++i);
//            }
//        } else {
//            for (int i = tag; i > finish; ) {
//                list.add(--i);
//            }
//        }
//
//        log.info("RichManager doWay finish way : {}", list);
//        return list;
//    }
//
//    @Override
//    public boolean turnActivityOn() {
//        return super.turnActivityOn();
//    }
//
//    @Override
//    public boolean turnActivityOff() {
//        return super.turnActivityOff();
//    }
//
//    @Override
//    public boolean activityIsEnable() {
//        return super.activityIsEnable();
//    }
//
//    @Override
//    protected String getActivityCode() {
//        return "richman";
//    }
//
//    /**
//     * 获取[fromUid]_[toUid]的拼接
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private String getConcatKey(Long fromUid, Long toUid) {
//        return fromUid.toString().concat("_").concat(toUid.toString());
//    }
//
//    /**
//     * 获取[small_uid]_[big_uid] 的拼接
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private String getQuiteKey(Long fromUid, Long toUid) {
//        String itemKey;
//        if (fromUid > toUid) {
//            itemKey = toUid + "_" + fromUid;
//        } else {
//            itemKey = fromUid + "_" + toUid;
//        }
//        return itemKey;
//    }
//
//    /**
//     * 获取恋爱基金
//     *
//     * @return
//     */
//    private Integer getLoveCoin(Long fromUid, Long toUid) {
//        Integer number = NumberUtil.getInt(redisManager.hget(String.format(DICE_INTIMATE_COIN_RECEIVER_NUMBER, fromUid), toUid.toString()), 0);
//
//        log.info("getLoveCoin , fromUid : {}, toUid : {}, number : {}", fromUid, toUid, number);
//        return number;
//    }
//
//    private void resetLoveCoin(Long fromUid, Long toUid) {
//        log.info("resetLoveCoin fromUid : {}, toUid : {}");
//        redisManager.hset(String.format(DICE_INTIMATE_COIN_RECEIVER_NUMBER, fromUid), toUid.toString(), 0);
//    }
//
//
//    /**
//     * 获取倍率
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private String getDiceMagnification(Long fromUid, Long toUid) {
//        int number;
//
//        //优先从redis 中获取，redis 中没有，则从
//        String type = Optional.ofNullable((String) redisManager.hget(DICE_MAGNIFICATION, getQuiteKey(fromUid, toUid))).orElse(null);
//
//
//        if (!StringUtils.isEmpty(type)) {
//            return type;
//        }
//
//        //判断是否是第一次抽取
//        if (1 == getFinishNum(fromUid, toUid)) {
//            number = 10;
//        } else {
//            number = RandomUtil.getInstance().getNumByLimit(1000);
//        }
//
//        if (number < 99) {
//            type = "mid";
//        } else if (number == 99) {
//            type = "more";
//        } else {
//            type = "little";
//        }
//
//        log.info("getDiceMagnification type : {},fromUid : {},toUid : {}", type, fromUid, toUid);
//        redisManager.hset(DICE_MAGNIFICATION, getQuiteKey(fromUid, toUid), type);
//        return type;
//
//    }
//
//    private void resetDiceMagnification(Long fromUid, Long toUid) {
//        log.info("resetDiceMagnification fromUid : {}, toUid : {}", fromUid, toUid);
//        redisManager.hset(DICE_MAGNIFICATION, getQuiteKey(fromUid, toUid), "");
//    }
//
//    private Integer getCoinRatio(Long fromUid, Long toUid) {
//        String type = getDiceMagnification(fromUid, toUid);
//        if (type.equals("mid")) {
//            return 20;
//        } else if (type.equals("more")) {
//            return 100;
//        } else {
//            return 10;
//        }
//    }
//
//    private Integer getBoxRatio(Long fromUid, Long toUid) {
//        String type = getDiceMagnification(fromUid, toUid);
//        if (type.equals("mid")) {
//            return 10;
//        } else if (type.equals("more")) {
//            return 50;
//        } else {
//            return 5;
//        }
//    }
//
//    /**
//     * 恋爱基金计算
//     * <p>
//     * 所有倍率都要 除以 100
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    private void incrLoveCoinAndBox(Long fromUid, Long toUid, DiceModel selfModel, DiceModel toModel) {
//        //增加恋爱基金和恋爱宝箱
//        Integer times = getFinishNum(fromUid, toUid);
//        // 基础金额按照 50 计算
//        int baseCoin = 50;
//        // 基金
//        int coin = 0;
//        //宝箱
//        int box = 0;
//        int coinRatio = getCoinRatio(fromUid, toUid);
//        int boxRatio = getBoxRatio(fromUid, toUid);
//        //计算消耗的骰子数量
//        int count = DiceWayEnum.getDiceWay(selfModel.getWayCategories()).size() + DiceWayEnum.getDiceWay(toModel.getWayCategories()).size();
//        if (times < 1) {
//            log.warn("incrLoveCoinAndBox times < 1, times : {},from:{},to:{}, selfModel :{}, toModel: {}", times, fromUid, toUid, selfModel, toModel);
//            return;
//        } else if (times == 1) {
//            //第一次
//            //扣除免费的骰子
//            int freeDiceNum = NumberUtil.getInt(redisManager.hget(String.format(DICE_FREE_NUMBER, fromUid), toUid.toString()), 0);
//            count = count - freeDiceNum << 1;
//            coin = (coinRatio * count * baseCoin) >> 1;
//            box = (boxRatio * count * baseCoin) >> 1;
//            log.info("incrLoveCoinAndBox times is one, times : {},from:{},to:{}, selfModel :{}, toModel: {},coin : {},box : {}", times, fromUid, toUid, selfModel, toModel, coin, box);
//        } else if (times < 6) {
//            coin = (coinRatio * count * baseCoin) >> 1;
//            box = (boxRatio * count * baseCoin) >> 1;
//            log.info("incrLoveCoinAndBox times is mid, times : {},from:{},to:{}, selfModel :{}, toModel: {},coin : {},box : {}", times, fromUid, toUid, selfModel, toModel, coin, box);
//        } else {
//            baseCoin = 100;
//            coin = (coinRatio * count * baseCoin) >> 1;
//            box = (boxRatio * count * baseCoin) >> 1;
//            log.info("incrLoveCoinAndBox times is more, times : {},from:{},to:{}, selfModel :{}, toModel: {},coin : {},box : {}", times, fromUid, toUid, selfModel, toModel, coin, box);
//        }
//
//        //保存金额
//        coin = (coin / 100);
//        box = (box / 100);
//        log.info("incrLoveCoinAndBox incr , fromUid : {}, toUid : {},coin : {},box : {}", fromUid, toUid, coin, box);
//        redisManager.hincr(String.format(DICE_INTIMATE_COIN_RECEIVER_NUMBER, fromUid), toUid.toString(), coin);
//        redisManager.hincr(String.format(DICE_INTIMATE_COIN_BOX_RECEIVER_NUMBER, fromUid), toUid.toString(), box);
//
//
//        //埋点
//        /**
//         "5：骰子5亲密度获得
//         10：骰子10亲密度获得"
//         "luxury envelope：豪华红包
//         ordinary envelope：普通红包"
//         */
//
//        Map<String, Object> paramsCoin = new HashMap<>();
//        paramsCoin.put("much", coin);
//        paramsCoin.put("from", baseCoin / 10);
//        paramsCoin.put("type", "ordinary envelope");
//        producerManager.umpTrack(fromUid, "love_increase_fund", paramsCoin);
//
//        Map<String, Object> paramsBox = new HashMap<>();
//        paramsBox.put("much", box);
//        paramsBox.put("from", baseCoin / 10);
//        paramsBox.put("type", "luxury envelope");
//        producerManager.umpTrack(fromUid, "love_increase_fund", paramsBox);
//
//
//    }
//
//
//    /**
//     * 重置路径
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    private void reset(Long fromUid, Long toUid) {
//        DiceModel diceModel = getDicePosition(fromUid, toUid);
//        if (diceModel.getHasFinish()) {
//            List<Integer> list = Optional.ofNullable(diceModel.getFinish()).orElse(new ArrayList<Integer>());
//            list.add(diceModel.getWayCategories());
//            diceModel = DiceModel.getInstance();
//            diceModel.setFinish(list);
//            log.info("RichManager reset fromUid : {},toUid : {}, diceModel : {}", fromUid, toUid, diceModel);
//            saveDicePosition(fromUid, toUid, diceModel);
//            activeFinish(fromUid, toUid);
//        }
//
//    }
//
//    /**
//     * 获取恋爱宝箱
//     *
//     * @return
//     */
//    private Integer getLoveBox(Long fromUid, Long toUid) {
//        Integer number = NumberUtil.getInt(redisManager.hget(String.format(DICE_INTIMATE_COIN_BOX_RECEIVER_NUMBER, fromUid), toUid.toString()), 0);
//        log.info("getLoveBox fromUid : {}, toUid : {}, number : {}", fromUid, toUid, number);
//
//        return number;
//    }
//
//    private void resetLoveBox(Long fromUid, Long toUid) {
//        log.info("resetLoveBox fromUid : {}, toUid : {}", fromUid, toUid);
//        redisManager.hset(String.format(DICE_INTIMATE_COIN_BOX_RECEIVER_NUMBER, fromUid), toUid.toString(), 0);
//    }
//
//
//    private void cancelPropagandaList(Long fromUid, Long toUid) {
//        //通知客户端更新骰子数量
//        ImChatInfoVO chatInfoVO = feignImService.getChat(ServicesAppIdEnum.lanling.getAppId(), fromUid, toUid).successData();
//        if (Objects.nonNull(chatInfoVO)) {
//            feignLanlingService.cancelPropagandaList(ServicesAppIdEnum.lanling.getAppId(), fromUid, toUid, chatInfoVO.getId());
//        } else {
//            log.error("cancelPropagandaList chatInfo is null, fromUid : {}, toUid : {}", fromUid, toUid);
//        }
//    }
//
//    /**
//     * 获取已经领取的金币
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private Integer getReceiveCoin(Long fromUid, Long toUid) {
//        return NumberUtil.getInt(redisManager.hget(String.format(DICE_INTIMATE_COIN_RECORD, fromUid), toUid.toString()), 0);
//    }
//
//    /**
//     * 累计已经领取的金币
//     *
//     * @param fromUid
//     * @param toUid
//     * @param number
//     */
//    private void incrReceiveCoin(Long fromUid, Long toUid, Integer number) {
//        log.info("incrReceiveCoin fromUid : {}, toUid : {}, number : {}", fromUid, toUid, number);
//        redisManager.hincr(String.format(DICE_INTIMATE_COIN_RECORD, fromUid), toUid.toString(), number);
//    }
//
//    /**
//     * 恋爱宝箱能否领取
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private Boolean hasLoveBox(Long fromUid, Long toUid) {
//        int count = getFinishNum(fromUid, toUid);
//        if (count < 1) {
//            return false;
//        }
//        if (getLoveBox(fromUid, toUid) < 1) {
//            return false;
//        }
//        return 0 == (getFinishNum(fromUid, toUid) % 10);
//    }
//
//    /**
//     * 还差几次解锁
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private Integer diffTimes(Long fromUid, Long toUid) {
//        int count = getFinishNum(fromUid, toUid);
//
//        //如果宝箱没有金额，返回十次
//
//        log.info("RichManager diffTimes : {}, fromUid : {}, toUid : {}", count, fromUid, toUid);
//        if (count <= 0 || getLoveBox(fromUid, toUid) == 0) {
//            return 10;
//        } else if ((count % 10) == 0) {
//            return 0;
//        } else {
//            return 10 - (count % 10);
//        }
//    }
//
//    /**
//     * 获取奔赴次数
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private Integer getFinishNum(Long fromUid, Long toUid) {
//        Integer times = NumberUtil.getInt(redisManager.hget(DICE_FINISH_NUM, getQuiteKey(fromUid, toUid)), 0);
//        return times;
//    }
//
//    /**
//     * 获取奔赴的时间戳
//     *
//     * @param fromUid
//     * @param toUid
//     * @return
//     */
//    private LocalDateTime getFinishNumTime(Long fromUid, Long toUid) {
//        Object obj = redisManager.hget(DICE_FINISH_NUM_TIMESTAMP, getQuiteKey(fromUid, toUid));
//        Long timestamp = 0L;
//        if (Objects.nonNull(obj)) {
//            timestamp = Long.parseLong(obj.toString());
//        }
//
//        Instant instant = Instant.ofEpochMilli(timestamp);
//        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
//    }
//
//    /**
//     * 累计奔赴次数
//     *
//     * @param fromUid
//     * @param toUid
//     */
//    private void incrDiceFinishNum(Long fromUid, Long toUid) {
//        log.info("incrDiceFinishNum fromUid : {}, toUid : {}", fromUid, toUid);
//        redisManager.hincr(DICE_FINISH_NUM, getQuiteKey(fromUid, toUid), 1);
//        redisManager.hset(DICE_FINISH_NUM_TIMESTAMP, getQuiteKey(fromUid, toUid), System.currentTimeMillis());
//    }
//
//
//    /**
//     * 用于统计用户金币情况
//     * <p>
//     * 1. 获取用户已经获得的金币
//     * 2. 获取用户恋爱基金的金币
//     * 3. 获取用户恋爱宝箱的金币
//     * 4. 全都加起来
//     * <p>
//     * 5. 得到 [from_uid]_[to_uid] -> Number
//     * 6. 用 [from_uid]_[to_uid] - [to_uid]_[from_uid] 得到差值 diffNumber
//     * <p>
//     * 7. diffNumber 如果大于0,补充 to_uid 金额
//     * 8. diffNumber 如果小于0,补充 from_uid 金额
//     */
//    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
//    public void statisticDamaged() {
//        log.info("statisticDamaged user coin");
//
//        List<Object> keys = getHKeys(DICE_WAY);
//
//        Integer userNumber = 0;
//        Integer coinNumber = 0;
//        for (Object item : keys) {
//            String[] strs = item.toString().split("_");
//            Long fromUid = Long.parseLong(strs[0]);
//            Long toUid = Long.parseLong(strs[1]);
//            int diffNumberFrom = getStatisticDamagedNumber(fromUid, toUid);
//            int diffNumberTo = getStatisticDamagedNumber(toUid, fromUid);
//
//            int value = diffNumberFrom - diffNumberTo;
//
//            if (value > 0) {
//                log.info("incrStatisticDamagedNumber  fromUid : {}, toUid : {}, value : {}", fromUid, toUid, value);
//                incrStatisticDamagedNumber(getConcatKey(fromUid, toUid), value);
//                coinNumber += Math.abs(value);
//                userNumber++;
//            }
//        }
//
//
//        log.info("statisticDamaged count result, coinNumber : {}, userNumber : {}", coinNumber, userNumber);
//    }
//
//
//    private void incrStatisticDamagedNumber(String key, int value) {
//        redisManager.hset(ACTIVITY_REPARATION, key, value);
//    }
//
//    private Integer getStatisticDamagedNumber(Long fromUid, Long toUid) {
//        Integer loveCoin = getLoveCoin(fromUid, toUid);
//        Integer loveBox = getLoveBox(fromUid, toUid);
//        Integer receiveCoin = getReceiveCoin(fromUid, toUid);
//
//        log.info("getStatisticDamagedNumber fromUid : {}, toUid : {}, coin : {}, box : {}, receiveCoin : {}", fromUid, toUid, loveCoin, loveBox, receiveCoin);
//        return loveCoin + loveBox + receiveCoin;
//    }
//
//    /**
//     * 用于给，用户补发金币
//     */
//    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
//    public void reparation() {
//        log.info("Enter reparation");
//        List<Object> keys = getHKeys(ACTIVITY_REPARATION);
//
//        Integer userNumber = 0;
//        Integer coinNumber = 0;
//
//        String msg = "【双向奔赴】补发金币 %s 已到账，请查收";
//        for (Object item : keys) {
//            String[] strs = item.toString().split("_");
//            Long fromUid = Long.parseLong(strs[0]);
//            Long toUid = Long.parseLong(strs[1]);
//
//            Integer value = Integer.parseInt(redisManager.hget(ACTIVITY_REPARATION, item.toString()).toString());
//
//            log.info("reparation fromUid : {}, toUid : {}, value : {}", fromUid, toUid, value);
//            if (value > 0) {
//                //给 toUid 补发
//                log.info("reparation to coin by Uid : {}, value : {}", toUid, value);
//
//                //清空补发记录
//                incrStatisticDamagedNumber(item.toString(), 0);
//                incrReceiveCoin(toUid, fromUid, value);
//                feignCoinService.updateAccountCoin(ServicesAppIdEnum.lanling.getAppId(), toUid, null, 0L, value.longValue(),
//                        BUSINESSKEY, BUSINESSTYPE, "【双向奔赴】活动补发", true);
//                npcNotify(toUid, String.format(msg, value));
//
//                userNumber++;
//                coinNumber += value;
//            }
//
//        }
//
//        log.info("reparation count result, coinNumber : {}, userNumber : {}", coinNumber, userNumber);
//
//    }
//
//    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
//    public void deductionsReparation() {
//        log.info("Enter deductionsReparation");
//        List<Object> keys = getHKeys(ACTIVITY_REPARATION);
//
//        Integer userNumber = 0;
//        Integer coinNumber = 0;
//        for (Object item : keys) {
//            String[] strs = item.toString().split("_");
//            Long fromUid = Long.parseLong(strs[0]);
//            Long toUid = Long.parseLong(strs[1]);
//            Integer value = Integer.parseInt(redisManager.hget(ACTIVITY_REPARATION, item.toString()).toString());
//            log.info("deductionsReparation fromUid : {}, toUid : {}, value : {}", fromUid, toUid, value);
//            if (value > 0) {
//                try {
//                    //给 toUid 扣除
//                    log.info("deductionsReparation to coin by Uid : {}, value : {}", toUid, - value * 2);
//
//                    //清空补发记录
//                    incrStatisticDamagedNumber(item.toString(), 0);
//                    incrReceiveCoin(toUid, fromUid, - value * 2);
//                } catch (Exception e) {
//                    log.error("deductionsReparation error", e);
//                }
//
//            }
//
//        }
//
//        log.info("deductionsReparation count result, coinNumber : {}, userNumber : {}", coinNumber, userNumber);
//
//    }
//
//    private List<Object> getHKeys(String key) {
//        Set<Object> keys = redisManager.hKeys(key);
//        return Arrays.asList(keys.toArray());
//    }
//}
