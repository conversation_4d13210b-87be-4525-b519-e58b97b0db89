package cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.FunctionUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.vo.activity.base.GiftVO;
import cn.yizhoucp.ump.api.vo.girlSDay.LevelInfoVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.girlsDay.GirlSDayEveryDayTaskEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.girlsDay.GirlSDayLevelGiftEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace.AppAdSpaceManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawLogJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.mp.dataobject.GirlSDayGuardInfoDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawLogService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.client.UserFeignService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.girlSDay.GirlSDayHelper.*;

@Slf4j
@Service
public class GirlSBizManager {
    @Resource
    private GirlSDayHelper girlSDayHelper;

    @Resource
    private GirlSDayDataManager girlSDayDataManager;

    @Resource
    private UserFeignService userFeignService;

    @Resource
    private GirlSDayGuardManager girlSDayGuardManager;

    @Resource
    private DrawPoolItemService drawPoolItemService;

    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private ProbStrategy probStrategy;

    @Resource
    private ScenePrizeService scenePrizeService;

    @Resource
    private GirlSDayRankManager girlSDayRankManager;

    @Resource
    private DrawLogService drawLogService;

    @Resource
    private DrawLogJpaDAO drawLogJpaDAO;

    @Resource
    private NotifyComponent notifyComponent;

    @Resource
    private GirlSDayTrackManager girlSDayTrackManager;

    @Resource
    private AppAdSpaceManager appAdSpaceManager;


    // ======= 仙女棒抽奖 =======

    @NoRepeatSubmit(time = 2)
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public PrizeItem fairyWandDraw(BaseParam param) {
        doCheckFairyWandResource(param.getUid());

        return doFairyWandDraw(param, param.getAppId(), param.getUid());
    }

    private PrizeItem doFairyWandDraw(BaseParam param, Long appId, Long uid) {
        // 抽奖
        String poolCode = ACTIVITY_CODE + "_DRAW_POOL";
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(poolCode);
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(drawPoolItemDOList, 1, Boolean.TRUE);
        List<PrizeItem> prizeItemList = drawPoolItems.stream().map(DrawPoolItemDTO::convert2PrizeItem).collect(Collectors.toList());
        // 下发奖励
        sendPrizeManager.sendPrize(param, prizeItemList.stream().map(p -> SendPrizeDTO.of(p, ACTIVITY_CODE)).collect(Collectors.toList()));
        PrizeItem prizeItem = prizeItemList.get(0);

        UserVO userInfo = userFeignService.getBasic(uid, appId).successData();
        String notice = String.format("恭喜%s抽出%s", userInfo.getName(), prizeItem.getPrizeName());
        girlSDayDataManager.addWinNotice(notice);

        drawLogService.save(DrawLogDO.builder()
                .bizId(UUID.randomUUID().toString())
                .unionId(param.getUnionId())
                .appId(appId)
                .activityCode(ACTIVITY_CODE)
                .poolCode(poolCode)
                .toUid(uid)
                .prizeKey(prizeItem.getPrizeKey())
                .targetTimes(1)
                .logJson(JSON.toJSONString(prizeItem))
                .createTime(girlSDayHelper.getNow())
                .updateTime(girlSDayHelper.getNow())
                .build());

        girlSDayTrackManager.drawResult(uid, prizeItem);

        return prizeItem;
    }

    private void doCheckFairyWandResource(Long uid) {
        Long remainFairyWand = girlSDayDataManager.getRemainFairyWand(uid);
        if (remainFairyWand < 1) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前仙女棒个数不够哦~");
        }
        remainFairyWand = girlSDayDataManager.decrFairyWand(uid, 1);
        if (remainFairyWand < 0) {
            log.warn("fairyWandDraw decrFairyWand remainFairyWand < 0, uid:{}", uid);
            girlSDayDataManager.incrFairyWand(uid, 1);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "请求太快了，请稍后再试");
        }
        log.info("fairyWandDraw decrFairyWand remainFairyWand:{}", remainFairyWand);
    }


    // ======= 增加仙女值/守护值 =======

    public void giveGift(Long fromUid, Long toUid, Long validCoin) {
        log.info("giveGift fromUid:{} toUid:{} validCoin:{}", fromUid, toUid, validCoin);
        if (validCoin < 1) {
            return;
        }
        if (!checkFromAndToUser(fromUid, toUid)) {
            return;
        }

        doIncrValue(fromUid, toUid, validCoin);
    }

    private boolean checkFromAndToUser(Long fromUid, Long toUid) {
        Map<Long, UserVO> idUserVOMap = userFeignService.acquireUsersInBulkPost2(fromUid, toUid).successData();
        if (idUserVOMap.size() < 2) {
            log.warn("giveGift acquireUsersInBulkPost2 fromUid:{} toUid:{} idUserVOMap.size:{}", fromUid, toUid, idUserVOMap.size());
            return false;
        }
        UserVO fromUserVO = idUserVOMap.get(fromUid);
        UserVO toUserVO = idUserVOMap.get(toUid);
        if (!SexType.MAN.equals(FunctionUtil.getOrNull(fromUserVO, UserVO::getSex))
                || !SexType.WOMAN.equals(FunctionUtil.getOrNull(toUserVO, UserVO::getSex))) {
            log.warn("giveGift fromUserVO:{} toUserVO:{}", fromUserVO, toUserVO);
            return false;
        }
        return true;
    }

    private void doIncrValue(Long fromUid, Long toUid, Long validCoin) {
        girlSDayGuardManager.incrGuardValue(fromUid, toUid, validCoin);

        doIncrGuardValue(fromUid, validCoin);

        doIncrFairyValue(toUid, validCoin);
    }

    private void doIncrGuardValue(Long uid, Long validCoin) {
        Long afterTodayFairyValue = girlSDayDataManager.incrTodayGuardValue(uid, validCoin);
        if (afterTodayFairyValue >= 30173 && afterTodayFairyValue-validCoin < 30173) {
            doFinishTask(uid, GirlSDayEveryDayTaskEnum.day30173, null);
        }
        if (girlSDayHelper.getNowHour() >= 20) {
            girlSDayDataManager.incrTodayGuardValueAfter8(uid, validCoin);
            BaseParam baseParam = BaseParam.ofMDC();
            baseParam.setUid(uid);
            baseParam.setAppId(ServicesAppIdEnum.lanling.getAppId());
            appAdSpaceManager.refreshUserAdSpace(baseParam);
            Long pkUser = girlSDayDataManager.getPkUser(uid);
            if (pkUser != null) {
                baseParam.setUid(pkUser);
                appAdSpaceManager.refreshUserAdSpace(baseParam);
            }
        }
        girlSDayRankManager.incrGuardRank(uid, validCoin);
    }

    private void doIncrFairyValue(Long uid, Long validCoin) {
        Long afterTodayFairyValue = girlSDayDataManager.incrTodayFairyValue(uid, validCoin);
        if (afterTodayFairyValue >= 30173 && afterTodayFairyValue-validCoin < 30173) {
            doFinishTask(uid, GirlSDayEveryDayTaskEnum.day30173, null);
        }
        girlSDayRankManager.incrFairyRank(uid, validCoin);
    }

    // ======= 完成每日任务 =========

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public void finishDailyTask(Long fromUid, Long toUid, GirlSDayEveryDayTaskEnum task, Integer count) {
        log.info("finishDailyTask fromUid:{} toUid:{} task:{} count:{}", fromUid, toUid, task.getIndex(), count);
        switch (task) {
            case receive36Gift:
                doReceive36GiftTask(fromUid, toUid, count);
                break;
            case receive520Gift:
            case receive1314Gift:
            case receive599Gift:
            case receive999Gift:
                doReceiveGiftTask(fromUid, toUid, task);
                break;
            case day30173:
            case partner:
            case confession:
            case lovePromise:
                doFinishTask(fromUid, toUid, task);
                break;
            default:
                break;
        }
    }

    private void doReceive36GiftTask(Long fromUid, Long toUid, Integer count) {
        if (checkSex(fromUid, SexType.MAN)) {
            doReceive36GiftTask(fromUid, count);
        }
        if (checkSex(toUid, SexType.WOMAN)) {
            doReceive36GiftTask(toUid, count);
        }
    }

    private void doReceive36GiftTask(Long uid, Integer count) {
        Long afterCount = girlSDayDataManager.incrFirstEverDayTask(uid, count);
        if (afterCount >= 10 && afterCount-count < 10) {
            doFinishTask(uid, GirlSDayEveryDayTaskEnum.receive36Gift, null);
        }
    }

    private void doReceiveGiftTask(Long fromUid, Long toUid, GirlSDayEveryDayTaskEnum task) {
        if (checkSex(fromUid, SexType.MAN)) {
            log.info("doReceiveGiftTask fromUid:{} task:{} toUid:{}", fromUid, task.getIndex(), toUid);
            doFinishTask(fromUid, task, toUid);
        }
        if (checkSex(toUid, SexType.WOMAN)) {
            log.info("doReceiveGiftTask toUid:{} task:{} fromUid:{}", toUid, task.getIndex(), fromUid);
            doFinishTask(toUid, task, fromUid);
        }
    }

    private boolean checkSex(Long uid, SexType sexType) {
        UserVO userVO = userFeignService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).successData();
        return userVO == null ? Boolean.FALSE : sexType.equals(userVO.getSex());
    }

    private void doFinishTask(Long fromUid, Long toUid, GirlSDayEveryDayTaskEnum task) {
        doFinishTask(fromUid, task, null);
        doFinishTask(toUid, task, null);
    }

    private void doFinishTask(Long uid, GirlSDayEveryDayTaskEnum task, Long toUid) {
        Integer everyDayTaskStatus = girlSDayDataManager.getEveryDayTaskStatus(uid, task.getIndex());
        if (everyDayTaskStatus == null) {
            girlSDayDataManager.finishTask(uid, task.getIndex());
            if (toUid != null) {
                girlSDayDataManager.finishTaskRecord(uid, task.getIndex(), toUid);
            }
        }
    }

    // ======= 等级礼物 =======

    public void giveLevelGift(Long fromUid, GirlSDayLevelGiftEnum girlSDayLevelGiftEnum, Integer count) {
        log.info("giveLevelGift fromUid:{} girlSDayLevelGiftEnum:{} count:{}", fromUid, girlSDayLevelGiftEnum, count);
        int afterCount = girlSDayDataManager.incrGiveGiftCount(fromUid, girlSDayLevelGiftEnum.getLevel(), count);
        if (afterCount >= girlSDayLevelGiftEnum.getUpLevelNeedCount() && afterCount-count < girlSDayLevelGiftEnum.getUpLevelNeedCount()) {
            log.info("giveLevelGift incrGiveGiftCount afterCount:{} girlSDayLevelGiftEnum:{} count:{}", afterCount, girlSDayLevelGiftEnum, count);
            girlSDayDataManager.setGiftLevel(fromUid, girlSDayLevelGiftEnum.getLevel() + 1);
            if (GirlSDayLevelGiftEnum.THREE.equals(girlSDayLevelGiftEnum)) {
                log.info("giveLevelGift maxLevel afterCount:{} girlSDayLevelGiftEnum:{} count:{}", afterCount, girlSDayLevelGiftEnum, count);
                doMaxGiftLevel(fromUid);
            }
        }
    }

    private void doMaxGiftLevel(Long uid) {
        BaseParam param = BaseParam.ofMDC();
        List<ScenePrizeDO> listBySceneCode = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, SCENE_LEVEL_GIFT_MAX_LEVEL);
        sendPrizeManager.sendPrize(param, listBySceneCode.stream().map(SendPrizeDTO::of).collect(Collectors.toList()));
        String notice = "恭喜您在“守护仙女”活动中成功解锁升级礼物并获得大礼包奖励，您可获得 甜蜜情侣 头像框、 甜蜜情侣 进场特效、告白超跑 座驾和一个价值999金币的礼物“爆炸惊喜”，请注意查收~";
        notifyComponent.npcNotify(uid, notice);
        girlSDayTrackManager.maxGiftLevel(uid, listBySceneCode);
    }

    // ======= 领取今日任务奖励 =======

    @NoRepeatSubmit(time = 2)
    public JSONObject receiveDailyTaskReward(Long uid, GirlSDayEveryDayTaskEnum task) {
        Integer everyDayTaskStatus = girlSDayDataManager.getEveryDayTaskStatus(uid, task.getIndex());
        if (everyDayTaskStatus == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务未完成");
        }
        if (everyDayTaskStatus == 2) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "奖励已领取");
        }
        return doReceiveDailyTaskReward(uid, task);
    }

    private JSONObject doReceiveDailyTaskReward(Long uid, GirlSDayEveryDayTaskEnum task) {
        log.info("receiveDailyTaskReward uid:{} task:{}", uid, JSON.toJSONString(task));
        girlSDayDataManager.receiveTaskReward(uid, task.getIndex());
        girlSDayDataManager.incrFairyWand(uid, task.getFairyWand());

        String notice = String.format("恭喜您在“守护仙女”活动中完成一次任务，成功获得 %s 个仙女棒，快使用仙女棒去城堡许愿池抽奖吧~", task.getFairyWand());
        notifyComponent.npcNotify(uid, notice);

        girlSDayTrackManager.finishTask(uid, task, task.getFairyWand());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("receiveCount", task.getFairyWand());
        // 增加额外守护值
        if (task.getExtraValue() == null) {
            return jsonObject;
        }
        Long toUid = girlSDayDataManager.getFinishTaskRecordToUid(uid, task.getIndex());
        if (toUid == null) {
            log.warn("receiveDailyTaskReward toUid is null, uid:{} task:{}", uid, task);
            return jsonObject;
        }
        GirlSDayGuardInfoDO guardInfo = girlSDayGuardManager.getGuardInfo(uid, toUid, Boolean.FALSE);
        if (guardInfo == null) {
            log.warn("receiveDailyTaskReward guardInfo is null, uid:{} toUid:{} task:{}", uid, toUid, task);
            return jsonObject;
        }
        if (guardInfo.getFromUid().equals(uid)) {
            girlSDayGuardManager.incrGuardValueById(guardInfo.getId(), task.getExtraValue());
            girlSDayRankManager.incrGuardRank(uid, task.getExtraValue().longValue());
            if (girlSDayHelper.getNowHour() >= 20) {
                girlSDayDataManager.incrTodayGuardValueAfter8(uid, task.getExtraValue().longValue());
            }
        } else {
            girlSDayGuardManager.incrFairyValueById(guardInfo.getId(), task.getExtraValue());
            girlSDayRankManager.incrFairyRank(uid, task.getExtraValue().longValue());
        }
        return jsonObject;
    }

    // ======= 提升等级 =======

    public LevelInfoVO upLevel(BaseParam param, Long toUid) {
        Long uid = param.getUid();
        GirlSDayGuardInfoDO guardInfo = girlSDayGuardManager.getBySplicId(uid, toUid);
        if (guardInfo == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "快去升级领取奖励吧");
        }

        boolean guard = false;
        Integer curLevel = guardInfo.getFairyLevel();
        if (guardInfo.getFromUid().equals(uid)) {
            curLevel = guardInfo.getGuardLevel();
            guard = true;
        }

        if (curLevel == MAX_LEVEL) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前已达到最大等级了哦~");
        }

        int needValue = GirlSDayHelper.UP_LEVEL_NEED_VALUE[curLevel + 1];
        if (guardInfo.getFairyValue() < needValue) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "快去升级领取奖励吧");
        }

        guardInfo = girlSDayGuardManager.upLevel(guardInfo.getFromUid(), guardInfo.getToUid(), curLevel + 1, guard);

        if (guardInfo == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "点击太快了～");
        }

        afterUpLevel(param, guardInfo, guard ? GUARD : FAIRY);

        return getUserLevelInfo(uid, toUid, guard ? GUARD : FAIRY);
    }

    public static final String UP_LEVEL_NOTICE = "恭喜您在“守护仙女”活动中的%s等级成功升级为%s，您可获得%s，奖励已发放至您的背包，请注意查收~";
    // 仙女/守护 和一个%s金币的礼物“%s”

    private void afterUpLevel(BaseParam param, GirlSDayGuardInfoDO guardInfo, String type) {
        List<ScenePrizeDO> listBySceneCode = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, String.format(SCENE_UPLEVEL, type, GUARD.equalsIgnoreCase(type) ? guardInfo.getGuardLevel() : guardInfo.getFairyLevel()));
        sendPrizeManager.sendPrize(param, listBySceneCode.stream().map(SendPrizeDTO::of).collect(Collectors.toList()));

        npcNotify(param, guardInfo, type, listBySceneCode);

        girlSDayTrackManager.finishTask(param.getUid(), guardInfo, listBySceneCode);
    }

    private void npcNotify(BaseParam param, GirlSDayGuardInfoDO guardInfo, String type, List<ScenePrizeDO> listBySceneCode) {
        List<String> rewardNotice = new ArrayList<>();
        for (ScenePrizeDO scenePrizeDO : listBySceneCode) {
            String prizeType = Optional.ofNullable(scenePrizeDO.getPrizeSubType()).orElse("gift");
            switch (prizeType) {
                case "head_frame":
                    rewardNotice.add(scenePrizeDO.getPrizeDesc() + "头像框");
                    break;
                case "entry_special_effect":
                    rewardNotice.add(scenePrizeDO.getPrizeDesc() + "入场特效");
                    break;
                case "mount":
                    rewardNotice.add(scenePrizeDO.getPrizeDesc() + "座驾");
                    break;
                default:
                    rewardNotice.add("一个" + scenePrizeDO.getPrizeValueGold() + "金币的礼物“" + scenePrizeDO.getPrizeDesc() + "”");
                    break;
            }
        }
        String notice = String.format(UP_LEVEL_NOTICE
                , FAIRY.equalsIgnoreCase(type) ? "仙女" : "守护"
                , FAIRY.equalsIgnoreCase(type)
                        ? FAIRY_LEVEL_NAME[guardInfo.getFairyLevel()]
                        : GUARD_LEVEL_NAME[guardInfo.getGuardLevel()]
                , String.join("、", rewardNotice));
        notifyComponent.npcNotify(param.getUid(), notice);
    }


    public LevelInfoVO getUserLevelInfo(Long uid, @Nullable Long toUid, String activityType) {
        GirlSDayGuardInfoDO guardInfo = null;
        if (toUid != null) {
            guardInfo = girlSDayGuardManager.getBySplicId(uid, toUid);
        }
        Integer myLevel = 0;
        Long myValue = 0L;
        String curLevelTitle = GirlSDayHelper.FAIRY.equals(activityType) ? FAIRY_LEVEL_NAME[0] : GUARD_LEVEL_NAME[0];
        String nextLevelTitle = GirlSDayHelper.FAIRY.equals(activityType) ? FAIRY_LEVEL_NAME[1] : GUARD_LEVEL_NAME[1];
        if (guardInfo != null) {
            if (GirlSDayHelper.FAIRY.equals(activityType)) {
                myLevel = guardInfo.getFairyLevel();
                myValue = guardInfo.getFairyValue();
                curLevelTitle = FAIRY_LEVEL_NAME[myLevel];
                nextLevelTitle = FAIRY_LEVEL_NAME[myLevel + 1];
            } else {
                myLevel = guardInfo.getGuardLevel();
                myValue = guardInfo.getGuardValue();
                curLevelTitle = GUARD_LEVEL_NAME[myLevel];
                nextLevelTitle = GUARD_LEVEL_NAME[myLevel + 1];
            }
        }
        Integer nextLevelValue = UP_LEVEL_NEED_VALUE[myLevel + 1];
        List<ScenePrizeDO> girlsDay = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, String.format(SCENE_UPLEVEL, activityType, myLevel + 1));
        List<GiftVO> collect = girlsDay.stream()
                .map(scenePrizeDO -> GiftVO.builder().icon(scenePrizeDO.getPrizeIcon()).name(scenePrizeDO.getPrizeDesc()).value(scenePrizeDO.getPrizeValueGold()).build())
                .distinct()
                .collect(Collectors.toList());
        return LevelInfoVO.builder()
                .curLevel(curLevelTitle)
                .nextLevel(nextLevelTitle)
                .nextLevelValue(nextLevelValue)
                .curValue(myValue)
                .upLevelReward(collect)
                .build();
    }

    public Integer getGiftLevel(Long uid) {
        return girlSDayDataManager.getGiftLevel(uid);
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    @Async(value = ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void sendGiftHandler(BaseParam baseParam, List<CoinGiftGivedModel> coinGiftGivedModels) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModels) {
            if (!GiftWay.NORMAL.getCode().equals(coinGiftGivedModel.getGiftWay())) {
                return;
            }
            Long fromUid = coinGiftGivedModel.getFromUid();
            Long toUid = coinGiftGivedModel.getToUid();
            Long count = coinGiftGivedModel.getProductCount();
            this.giveGift(fromUid, toUid, coinGiftGivedModel.getCoin());
            String giftKey = !StringUtils.isEmpty(coinGiftGivedModel.getLotteryGiftKey())
                    ? coinGiftGivedModel.getLotteryGiftKey()
                    : coinGiftGivedModel.getGiftKey();
            switch (giftKey) {
                case "HS_GIFT" : {
                    this.giveLevelGift(fromUid, GirlSDayLevelGiftEnum.ONE, Math.toIntExact(count));
                    break;
                }
                case "XHCP_GIFT" : {
                    this.giveLevelGift(fromUid, GirlSDayLevelGiftEnum.TWO, Math.toIntExact(count));
                    this.finishDailyTask(fromUid, toUid, GirlSDayEveryDayTaskEnum.receive599Gift, Math.toIntExact(count));
                    break;
                }
                case "WG_GIFT" : {
                    this.giveLevelGift(fromUid, GirlSDayLevelGiftEnum.THREE, Math.toIntExact(count));
                    this.finishDailyTask(fromUid, toUid, GirlSDayEveryDayTaskEnum.receive999Gift, Math.toIntExact(count));
                    break;
                }
                case "HYJXH_GIFT": {
                    this.finishDailyTask(fromUid, toUid, GirlSDayEveryDayTaskEnum.receive520Gift, Math.toIntExact(count));
                    break;
                }
                case "LAMFH_GIFT": {
                    this.finishDailyTask(fromUid, toUid, GirlSDayEveryDayTaskEnum.receive1314Gift, Math.toIntExact(count));
                    break;
                }
                case "NSZY_GIFT": {
                    this.finishDailyTask(fromUid, toUid, GirlSDayEveryDayTaskEnum.receive36Gift, Math.toIntExact(count));
                    break;
                }
                default: break;
            }
        }
    }

    public List<DrawLogItem> drawLogList(Long uid) {
        List<DrawLogDO> list = drawLogJpaDAO.findByToUidAndActivityCodeAndPoolCodeOrderByCreateTimeDesc(uid, ACTIVITY_CODE, ACTIVITY_CODE + "_DRAW_POOL");
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    private DrawLogItem convert(DrawLogDO drawLogDO) {
        JSONObject jsonObject = JSON.parseObject(drawLogDO.getLogJson());
        return DrawLogItem.builder()
                .icon(jsonObject.getString("prizeIcon"))
                .text(jsonObject.getString("prizeName"))
                .time(drawLogDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .createTime(drawLogDO.getCreateTime())
                .build();
    }
}
