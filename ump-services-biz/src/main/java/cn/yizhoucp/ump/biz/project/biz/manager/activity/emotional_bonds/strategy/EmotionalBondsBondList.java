package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.PendingBondsVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondRelationshipDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 获取未缔结关系的列表，如果已经过期则不展示
 */
@Service
@Slf4j
public class EmotionalBondsBondList implements ExecutableStrategy {

    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private UserFeignManager userFeignManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        Long userId = buttonEventParam.getBaseParam().getUid();
        log.info("Getting unbonded relationships for user: {}", userId);

        Date currentDate = new Date();

        // 查询原始表中的未缔结关系
        LambdaQueryWrapper<EmotionalBondRelationshipDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmotionalBondRelationshipDO::getUserId, userId)
                .eq(EmotionalBondRelationshipDO::getStatus, 0) // 未缔结状态
                .gt(EmotionalBondRelationshipDO::getExpireTime, currentDate); // 未过期

        List<EmotionalBondRelationshipDO> relationships = emotionalBondRelationshipService.list(queryWrapper);


        if (relationships == null || relationships.isEmpty()) {
            log.info("No active non-expired unbonded relationships found for user: {}", userId);
            return PendingBondsVO.builder().pendingBonds(new ArrayList<>()).build();
        }

        List<PendingBondsVO.BondsVO> bondsVOList = new ArrayList<>();

        // 处理原始表中的关系
        if (relationships != null && !relationships.isEmpty()) {
            List<Long> userIds = relationships.stream()
                    .flatMap(relationship -> Stream.of(relationship.getUserId(), relationship.getOppositeId()))
                    .filter(id -> id != null) // 过滤空值
                    .distinct() // 去重
                    .collect(Collectors.toList());

            // 获取用户信息
            Map<Long, UserVO> userMap = userIds.stream()
                    .map(id -> userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), id))
                    .collect(Collectors.toMap(UserVO::getId, user -> user, (existing, replacement) -> existing));

            // 构建原始表关系的响应
            List<PendingBondsVO.BondsVO> originalBondsVOList = relationships.stream()
                    .map(relationship -> {
                        EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(relationship.getEffectKey());

                        PendingBondsVO.BondUserVO userA = new PendingBondsVO.BondUserVO();
                        userA.setUid(relationship.getUserId());
                        UserVO userAInfo = userMap.get(relationship.getUserId());
                        if (userAInfo != null) {
                            userA.setName(userAInfo.getName());
                            userA.setAvatar(userAInfo.getAvatar());
                        }
                        userA.setRole(rolePairEnum.getRoleA());

                        PendingBondsVO.BondUserVO userB = new PendingBondsVO.BondUserVO();
                        userB.setUid(relationship.getOppositeId());
                        UserVO userBInfo = userMap.get(relationship.getOppositeId());
                        if (userBInfo != null) {
                            userB.setName(userBInfo.getName());
                            userB.setAvatar(userBInfo.getAvatar());
                        }
                        userB.setRole(rolePairEnum.getRoleB());

                        return PendingBondsVO.BondsVO.builder()
                                .bondId(relationship.getId().toString())
                                .userA(userA)
                                .userB(userB)
                                .build();
                    })
                    .collect(Collectors.toList());

            bondsVOList.addAll(originalBondsVOList);
        }

        log.info("Found {} active non-expired unbonded relationships for user: {}", bondsVOList.size(), userId);
        return PendingBondsVO.builder().pendingBonds(bondsVOList).build();
    }
}
