package cn.yizhoucp.ump.biz.project.dal.mybatis.mapper;

import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondRelationshipDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 情感纽带关系Mapper接口
 */
@Mapper
public interface EmotionalBondRelationshipMapper extends BaseMapper<EmotionalBondRelationshipDO> {
    // 可在此添加自定义查询方法
    /**
     * 根据用户ID和状态查询情感纽带关系
     * @param userId 用户ID
     * @param status 状态
     * @return 情感纽带关系列表
     */
    // List<EmotionalBondRelationshipDO> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Integer status);
}