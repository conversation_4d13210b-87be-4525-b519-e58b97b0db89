package cn.yizhoucp.ump.biz.project.biz.manager;

import cn.yizhoucp.ump.biz.project.biz.enums.MissionFinishTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.factory.MissionFactory;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.param.MissionParam;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityMissionJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityMissionDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.TwinsConstant.ACTIVITY_CODE;

/**
 * 任务统一推进入口
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class MissionProcessManager {

    @Resource
    private ActivityMissionJpaDAO activityMissionJpaDAO;
    @Resource
    private MissionFactory missionFactory;

    public Boolean process(MissionParam param) {
        if (Objects.isNull(param.getTaskCode()) && Objects.isNull(param.getType())) {
            log.debug("参数错误 param:{}", JSON.toJSONString(param));
            return Boolean.FALSE;
        }

        // 临时处理
        if (ACTIVITY_CODE.equals(param.getActivityCode())) {
            param.setUnionId("wGF30Qq8c3");
        }

        // 获取可推进任务配置清单
        List<ActivityMissionDO> configList = getConfigList(param);
        log.debug("可推进任务 configList:{}", JSON.toJSONString(configList));
        if (CollectionUtils.isEmpty(configList)) {
            log.debug("无可推进任务 param:{}", JSON.toJSONString(param));
            return Boolean.FALSE;
        }

        // 依次推进
        Long uid = param.getUid();
        Long toUid = param.getToUid();
        configList.stream().filter(c -> StringUtils.isNotBlank(c.getBelongActivityCode())).forEach(c -> {
            if ("toUid".equals(c.getProcessType())) {
                log.debug("debug-推进 toUid 类型任务 uid:{}, toUid:{}, taskCode:{}", param.getUid(), param.getToUid(), c.getCode());
                param.setUid(toUid);
            } else {
                log.debug("debug-推进 uid 类型任务 uid:{}, toUid:{}, taskCode:{}", param.getUid(), param.getToUid(), c.getCode());
                param.setUid(uid);
            }
            param.setTaskCode(c.getCode());
            if (Objects.isNull(param.getBizParam())) {
                param.setBizParam(new JSONObject());
            }
            param.getBizParam().put("missionConfig", c);
            log.debug("process taskCode {}", param.getTaskCode());
            missionFactory.getInstance(c.getBelongActivityCode()).process(param);
            if (MissionFinishTypeEnum.SEND_OR_RECEIVE.getCode().equals(c.getFinishType())) {
                param.setUid(toUid);
                missionFactory.getInstance(c.getBelongActivityCode()).process(param);
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 按类型分组推进 || 按编号推进
     *
     * @param param
     * @return
     */
    private List<ActivityMissionDO> getConfigList(MissionParam param) {
        if (Objects.nonNull(param.getType())) {
            return activityMissionJpaDAO.findAll(Example.of(ActivityMissionDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).type(param.getType().name()).status("enable").build()));
        } else if (StringUtils.isNotBlank(param.getTaskCode())) {
            return activityMissionJpaDAO.findAll(Example.of(ActivityMissionDO.builder().appId(param.getAppId()).unionId(param.getUnionId()).code(param.getTaskCode()).status("enable").build()));
        }
        return Lists.newArrayList();
    }

}
