package cn.yizhoucp.ump.biz.project.biz.manager.activity.princessRoseAndKnight;

import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.GiftTypeEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.userActivity.ActivityManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler.BuoyInfoHandler;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dto.adSpace.BuoyInfoDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.BOX_GIFT_SET;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.BOX_SENT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.COURAGE_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.LOTTERY_GIFT_KEY;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.Task.TASK_2;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.Task.TASK_3;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.Task.TASK_4;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.Task.TASK_5;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.Task.TASK_6;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.Task.TASK_7;

@Service
@Slf4j
public class PrincessRoseAndKnightBizManager implements BuoyInfoHandler {

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;
    @Resource
    private ActivityManager activityManager;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private PrincessRoseAndKnightRankManager princessRoseAndKnightRankManager;
    @Resource
    private PrincessRoseAndKnightTrackManager princessRoseAndKnightTrackManager;

    @ActivityCheck(activityCode = PrincessRoseAndKnightConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        Long uid = param.getUid();
        Long appId = param.getAppId();
        UserVO userVO = feignUserService.getBasic(uid, appId).successData();
        int stage = stage();

        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            if (log.isDebugEnabled()) {
                log.debug("coinGiftGivedModel {}", JSON.toJSONString(coinGiftGivedModel));
            }

            Long toUid = coinGiftGivedModel.getToUid();
            UserVO toUserVO = feignUserService.getBasic(toUid, appId).successData();
            String splicUserId = AppUtil.splicUserId(uid, toUid);

            if (LOTTERY_GIFT_KEY.contains(coinGiftGivedModel.getLotteryGiftKey())) {
                if (userVO != null && toUserVO != null && !userVO.getSex().equals(toUserVO.getSex())) {
                    redisManager.set(String.format(BOX_SENT, splicUserId), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND);
                }

                if (toUserVO != null && SexType.WOMAN.equals(toUserVO.getSex())) {
                    princessRoseAndKnightRankManager.incrRankValue(toUid, coinGiftGivedModel.getCoin(), String.format(RANK, stage));
                }
            }

            if (GiftTypeEnum.relation.getCode().equals(coinGiftGivedModel.getGiftType())) {
                if (userVO != null && SexType.MAN.equals(userVO.getSex())) {
                    if (Boolean.TRUE.equals(redisManager.hasKey(String.format(BOX_SENT, splicUserId)))) {
                        long courageValue = Optional.ofNullable(redisManager.incrLong(String.format(COURAGE_VALUE, splicUserId, stage), coinGiftGivedModel.getCoin(), DateUtil.ONE_MONTH_SECOND)).orElse(0L);
                        for (PrincessRoseAndKnightConstant.CpTask cpTask : PrincessRoseAndKnightConstant.CpTask.values()) {
                            if (courageValue - coinGiftGivedModel.getCoin() < cpTask.getCourageValue().get(stage - 1) && courageValue >= cpTask.getCourageValue().get(stage - 1)) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(cpTask.getTaskTitle(), uid);
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(cpTask.getTaskTitle(), toUid);
                                break;
                            }
                        }
                    }
                }
            }

            // 每日任务
            String lotteryGiftKey = coinGiftGivedModel.getLotteryGiftKey();
            Long productCount = coinGiftGivedModel.getProductCount();
            String giftKey = coinGiftGivedModel.getGiftKey();
            if (userVO != null) {
                if (SexType.MAN.equals(userVO.getSex())) {
                    if ("MGXL_BOX".equals(lotteryGiftKey)) {
                        Long task2 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_2), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task2 >= PrincessRoseAndKnightConstant.Task.TASK_2.getFinishTimes() && task2 - productCount < PrincessRoseAndKnightConstant.Task.TASK_2.getFinishTimes()) {
                            princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_2.getTaskTitle(), uid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task3 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_3), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task3 >= PrincessRoseAndKnightConstant.Task.TASK_3.getFinishTimes() && task3 - productCount < PrincessRoseAndKnightConstant.Task.TASK_3.getFinishTimes()) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_3.getTaskTitle(), uid);
                            }
                        }
                    } else if ("QSJS_BOX".equals(lotteryGiftKey)) {
                        Long task4 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_4), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task4 >= PrincessRoseAndKnightConstant.Task.TASK_4.getFinishTimes() && task4 - productCount < PrincessRoseAndKnightConstant.Task.TASK_4.getFinishTimes()) {
                            princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_4.getTaskTitle(), uid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task5 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_5), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task5 >= PrincessRoseAndKnightConstant.Task.TASK_5.getFinishTimes() && task5 - productCount < PrincessRoseAndKnightConstant.Task.TASK_5.getFinishTimes()) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_5.getTaskTitle(), uid);
                            }
                        }
                    } else if ("XDLA_BOX".equals(lotteryGiftKey)) {
                        Long task6 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_6), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task6 >= PrincessRoseAndKnightConstant.Task.TASK_6.getFinishTimes() && task6 - productCount < PrincessRoseAndKnightConstant.Task.TASK_6.getFinishTimes()) {
                            princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_6.getTaskTitle(), uid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, uid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task7 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, uid, DateUtil.getNowYyyyMMdd(), TASK_7), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task7 >= PrincessRoseAndKnightConstant.Task.TASK_7.getFinishTimes() && task7 - productCount < PrincessRoseAndKnightConstant.Task.TASK_7.getFinishTimes()) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_7.getTaskTitle(), uid);
                            }
                        }
                    }
                }
            }
            if (toUserVO != null) {
                if (SexType.WOMAN.equals(toUserVO.getSex())) {
                    if ("MGXL_BOX".equals(lotteryGiftKey)) {
                        Long task9 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_2), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task9 >= PrincessRoseAndKnightConstant.Task.TASK_2.getFinishTimes() && task9 - productCount < PrincessRoseAndKnightConstant.Task.TASK_2.getFinishTimes()) {
                            princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_2.getTaskTitle(), toUid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task10 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_3), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task10 >= PrincessRoseAndKnightConstant.Task.TASK_3.getFinishTimes() && task10 - productCount < PrincessRoseAndKnightConstant.Task.TASK_3.getFinishTimes()) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_3.getTaskTitle(), toUid);
                            }
                        }
                    } else if ("QSJS_BOX".equals(lotteryGiftKey)) {
                        Long task11 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_4), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task11 >= PrincessRoseAndKnightConstant.Task.TASK_4.getFinishTimes() && task11 - productCount < PrincessRoseAndKnightConstant.Task.TASK_4.getFinishTimes()) {
                            princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_4.getTaskTitle(), toUid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task12 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_5), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task12 >= PrincessRoseAndKnightConstant.Task.TASK_5.getFinishTimes() && task12 - productCount < PrincessRoseAndKnightConstant.Task.TASK_5.getFinishTimes()) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_5.getTaskTitle(), toUid);
                            }
                        }
                    } else if ("XDLA_BOX".equals(lotteryGiftKey)) {
                        Long task13 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_6), productCount, DateUtil.ONE_MONTH_SECOND);
                        if (task13 >= PrincessRoseAndKnightConstant.Task.TASK_6.getFinishTimes() && task13 - productCount < PrincessRoseAndKnightConstant.Task.TASK_6.getFinishTimes()) {
                            princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_6.getTaskTitle(), toUid);
                        }
                        Long addNum = redisManager.sSetExpire(String.format(BOX_GIFT_SET, toUid, DateUtil.getNowYyyyMMdd(), lotteryGiftKey), DateUtil.ONE_MONTH_SECOND, giftKey);
                        if (addNum != null && addNum != 0) {
                            Long task14 = redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, toUid, DateUtil.getNowYyyyMMdd(), TASK_7), productCount, DateUtil.ONE_MONTH_SECOND);
                            if (task14 >= PrincessRoseAndKnightConstant.Task.TASK_7.getFinishTimes() && task14 - productCount < PrincessRoseAndKnightConstant.Task.TASK_7.getFinishTimes()) {
                                princessRoseAndKnightTrackManager.allActivityTaskFinish(TASK_7.getTaskTitle(), toUid);
                            }
                        }
                    }
                }
            }
        }

        return Boolean.TRUE;
    }

    public int stage() {
        ActivityDO activityDO = activityManager.getActivityInfo(BaseParam.builder().unionId("wGF30Qq8c3").build(), ACTIVITY_CODE);
        LocalDateTime startTime = activityDO.getStartTime().truncatedTo(ChronoUnit.DAYS);
        LocalDateTime now = LocalDateTime.now();
        log.debug("startTime {} now {}", startTime, now);

        long between = ChronoUnit.DAYS.between(startTime, now);
        if (between < 7) {
            return 1;
        } else if (between < 14) {
            return 2;
        } else if (between < 22) {
            return 3;
        } else {
            return 4;
        }
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public Function<HandlerContext, BuoyInfoDTO> getBuoyInfoHandler() {
        return handlerContext -> {
            if (handlerContext.getToUid() == null) {
                return BuoyInfoDTO.builder().build();
            }

            BaseParam param = BaseParam.ofMDC();
            ActivityDO activityDO = activityManager.getActivityInfo(param, this.getActivityCode());
            return BuoyInfoDTO.builder().routerUrl(this.getActivityUrl(param, env, environment, activityDO.getActivityUrl()) + "&toUid=" + handlerContext.getToUid()).build();
        };
    }

    private String getActivityUrl(BaseParam param, String env, Environment environment, String activityUrl) {
        return String.format(
                ActivityUrlUtil.getH5BaseUrl(param.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + activityUrl + "?uid=%s&from=chat_buoy",
                param.getUid()
        );
    }

}
