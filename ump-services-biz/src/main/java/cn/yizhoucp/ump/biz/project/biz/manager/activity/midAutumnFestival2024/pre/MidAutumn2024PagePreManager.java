package cn.yizhoucp.ump.biz.project.biz.manager.activity.midAutumnFestival2024.pre;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonUserVO;
import cn.yizhoucp.ms.core.vo.imservices.ImChatInfoVO;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageAttrsCardSystemMsg;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.midAutumnFestival2024.MidAutumn2024PreIndexVO;
import cn.yizhoucp.ump.api.vo.activity.midAutumnFestival2024.MidAutumnPairingUser;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.midAutumnFestival2024.MidAutumn2024Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.midAutumnFestival2024.MidAutumn2024RankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 2024中秋节活动预约
 *
 * <AUTHOR>
 * @version V1.0
 * @since 21:56 2024/9/1
 */
@Slf4j
@Service
public class MidAutumn2024PagePreManager implements IndexManager {

    @Lazy
    @Resource
    private FeignLanlingService feignLanlingService;

    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private ServerPushManager serverPushManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private Environment environment;
    @Resource
    private MidAutumn2024TrackPreManager midAutumn2024TrackPreManager;
    @Resource
    private FeignImService feignImService;
    @Resource
    private MidAutumn2024RankManager midAutumn2024RankManager;

    @Value("${spring.profiles.active}")
    private String env;


    @Override
    @ActivityCheck(activityCode = MidAutumn2024Constant.ACTIVITY_CODE_PRE)
    public MidAutumn2024PreIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        MidAutumn2024PreIndexVO midAutumn2024IndexVO = new MidAutumn2024PreIndexVO();
        Long uid = param.getUid();
        String pairingKey = MidAutumn2024Constant.createPairingKey(uid);
        Map<Object, Object> pairingMap = redisManager.hmget(pairingKey);
        Optional<Map.Entry<Object, Object>> latestEntry = pairingMap.entrySet().stream().min((e1, e2) -> Long.compare((Long) e2.getValue(), (Long) e1.getValue()));
        Boolean isReserve = false;
        MidAutumn2024PreIndexVO.UserPairingInfo userPairingInfo = new MidAutumn2024PreIndexVO.UserPairingInfo();
        if (latestEntry.isPresent()) {
            Long latestUid = Long.valueOf(latestEntry.get().getKey().toString());
            if (toUid != null) {
                latestUid = toUid;
            }
            UserVO toUserVO = feignUserService.getBasic(latestUid, ServicesAppIdEnum.lanling.getAppId()).getData();
            userPairingInfo.setPairedUser(MidAutumn2024PreIndexVO.UserVO.builder()
                    .userId(toUserVO.getId())
                    .userName(toUserVO.getName())
                    .userAvatar(toUserVO.getAvatar())
                    .build());
            isReserve = true;
        }
        UserVO userVO = feignUserService.getBasic(param.getUid(), ServicesAppIdEnum.lanling.getAppId()).getData();
        userPairingInfo.setUser(MidAutumn2024PreIndexVO.UserVO.builder()
                .userId(userVO.getId())
                .userName(userVO.getName())
                .userAvatar(userVO.getAvatar())
                .build());
        midAutumn2024IndexVO.setUserPairingInfo(userPairingInfo);
        midAutumn2024IndexVO.setIsReserve(isReserve);
        return midAutumn2024IndexVO;
    }


    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return MidAutumn2024Constant.ACTIVITY_CODE_PRE;
    }

    /**
     * 获取好友列表
     *
     * @return
     */

    public List<MidAutumnPairingUser> getFriendList(Long start, Integer num) {
        start = 0L;
        num = 100;
        SecurityUser user = SecurityUtils.getCurrentUser();
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
        PageVO<WrapUserVO> subFriendList = feignLanlingService.getSubscribeList(start, num).getData();
        PageVO<WrapUserVO> fansList = feignLanlingService.getFansList(start, num).getData();
        List<WrapUserVO> list = new ArrayList<>();
        list.addAll(friendList.getList());
        list.addAll(subFriendList.getList());
        list.addAll(fansList.getList());
        log.info("获取好友列表：{}", list);
        return list.stream().map(wrapUserVO -> {
                    CommonUserVO userVO = wrapUserVO.getUser();
                    String relationKey = MidAutumn2024Constant.createPairingKey(user.getUserId());
                    Object paired = redisManager.hget(relationKey, userVO.getId().toString());
                    ImChatInfoVO imChatINfoVO = feignImService.getChat(ServicesAppIdEnum.lanling.getAppId(), user.getUserId(), userVO.getId()).getData();
                    Date lastChatTime = null;
                    if (ObjectUtil.isNotNull(imChatINfoVO)) {
                        lastChatTime = imChatINfoVO.getUpdateTime();
                    }
                    String status = null;
                    Double moonValue = 0d;
                    if (ObjectUtil.isNotNull(paired)) {
                        status = "已预约";
                        moonValue = Optional.ofNullable(redisManager.score(MidAutumn2024Constant.MOON_TOUR_RANKING, AppUtil.splicUserId(user.getUserId(), userVO.getId()))).orElse(0d);
                        String value;
                        if (moonValue > 10000) {
                            value = String.format("%.1f", moonValue / 10000.0) + "w";
                        } else {
                            value = moonValue.toString();
                        }
                        if (moonValue > 0) {
                            status = "月光值：" + value;
                        }
                    }
                    return MidAutumnPairingUser.builder()
                            .userId(userVO.getId())
                            .userName(userVO.getName())
                            .userAvatar(userVO.getAvatar())
                            .isPaired(ObjectUtil.isNotNull(paired))
                            .pairingTime(String.valueOf(paired))
                            .moonValue(moonValue.longValue())
                            .status(status)
                            .lastChatTime(lastChatTime)
                            .build();
                })
                .sorted(Comparator.comparing(MidAutumnPairingUser::getMoonValue, Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(MidAutumnPairingUser::getPairingTime, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(MidAutumnPairingUser::getLastChatTime, Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(MidAutumnPairingUser::getIsPaired, Comparator.nullsLast(Comparator.naturalOrder())))
                .limit(50)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<MidAutumnPairingUser> getReservedFriend() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        String pairingKey = MidAutumn2024Constant.createPairingKey(user.getUserId());
        return redisManager.hKeys(pairingKey).stream().map(uid -> {
            UserVO userVO = feignUserService.getBasic(Long.valueOf(uid.toString()), ServicesAppIdEnum.lanling.getAppId()).getData();
            return MidAutumnPairingUser.builder()
                    .userId(userVO.getId())
                    .userName(userVO.getName())
                    .userAvatar(userVO.getAvatar())
                    .isPaired(true)
                    .build();
        }).collect(Collectors.toList());
    }

    public Boolean reserveFriend(Long toUid) {
        if (toUid == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "未选择结伴对象");
        }
        SecurityUser user = SecurityUtils.getCurrentUser();
        String pairingKey = MidAutumn2024Constant.createPairingKey(user.getUserId());
        String toUserPairingKey = MidAutumn2024Constant.createPairingKey(toUid);
        String allPairingKey = MidAutumn2024Constant.createAllPairingKey();
        if (redisManager.hget(allPairingKey, AppUtil.splicUserId(user.getUserId(), toUid)) != null) {
            return Boolean.TRUE;
        }
        redisManager.hset(pairingKey, toUid.toString(), System.currentTimeMillis());
        redisManager.hset(toUserPairingKey, user.getUserId().toString(), System.currentTimeMillis());
        redisManager.hset(allPairingKey, AppUtil.splicUserId(user.getUserId(), toUid), System.currentTimeMillis());
        //预约活动提醒
        sendMessageToUser(user.getUserId(),toUid);
        return Boolean.TRUE;
    }

    @ActivityCheck(activityCode = MidAutumn2024Constant.ACTIVITY_CODE_PRE)
    private void sendMessageToUser(Long uid,Long toUid){
        UserVO fromUser = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).getData();
        UserVO toUser = feignUserService.getBasic(toUid, ServicesAppIdEnum.lanling.getAppId()).getData();
        String msg = "“%s“邀请你参加“相思明月寄”中秋主题活动活动期间（9.9-9.18）积累“相思雀”最高可兑换价值15778金币的中秋限定礼包";
        String fromUserMsg = "您已完成和“%s”的中秋活动预约，活动将于9月9日17:00开启，预祝您与心爱的TA共度美好佳节， <a href=\"%s\">立即查看</a>";
        String url = getActivityUrl();
        //发送小助手消息
        notifyComponent.npcNotify(
                uid,
                String.format(fromUserMsg, toUser.getName(), url + "?toUid=" + toUid));
        notifyComponent.npcNotify(
                toUser.getId(),
                String.format(msg, fromUser.getName()) + "，" + String.format("<a href=\"%s\">立即查看</a>", url + "?toUid=" + uid));
        //发送卡片
        Long appId = ServicesAppIdEnum.lanling.getAppId();
        SystemMessageAttrsCardSystemMsg attrs = createCardMessage(String.format(msg, fromUser.getName()), uid);
        log.info("发送中秋邀请卡片消息 fromUid:{},toUid:{} attrs {}", fromUser.getId(), toUid, attrs);
        ImChatInfoVO imChatINfoVO = feignImService.getChat(ServicesAppIdEnum.lanling.getAppId(), uid, toUser.getId()).getData();
        if (ObjectUtil.isNotNull(imChatINfoVO)) {
            midAutumn2024TrackPreManager.cardSend(uid, toUid, imChatINfoVO.getId().toString());
        }
        serverPushManager.sendOfficialMidAutumnCardMsg(BaseParam.builder().appId(appId).unionId(ServicesAppIdEnum.convertFormAppId(appId).getUnionId()).uid(uid).build(), toUid, attrs);
    }

    private SystemMessageAttrsCardSystemMsg createCardMessage(String msg, Long toUid) {
        SystemMessageAttrsCardSystemMsg attrs = new SystemMessageAttrsCardSystemMsg();
        attrs.setKey("initiateOfficialCombine");
        JSONObject cardMessage = new JSONObject();
        String url = getActivityUrl() + "?from=moonlight_book" + "&toUid=" + toUid;
        cardMessage.put("content", msg);
        cardMessage.put("url", url);
        cardMessage.put("background", "https://res-cdn.nuan.chat/admin-v2/files/dev/2024-09/1207005c4bd3ebe0b8fe809f2027b2b0.png");
        attrs.setCardMessage(cardMessage);
        return attrs;
    }

    public String getActivityUrl() {
        return ActivityUrlUtil.getH5BaseUrl(MDCUtil.getCurUnionIdByMdc(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "mingyue-xs-pre-0908";
    }

}
