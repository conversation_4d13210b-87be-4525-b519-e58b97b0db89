package cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.FoolsDay2025TrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.common.FoolsDay2025Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.common.FoolsDay2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.common.FoolsDay2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.vo.FoolsDay2025IndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.fools_day_2025.vo.FoolsDay2025ProceedResponse;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FoolsDay2025Proceed implements ExecutableStrategy {
    @Resource
    private FoolsDay2025RedisManager foolsDay2025RedisManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LogComponent logComponent;

    @Resource
    private FoolsDay2025TrackManager foolsDay2025TrackManager;
    @Resource
    private SendPrizeManager sendPrizeManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        String bizKey = buttonEventParam.getBizKey();
        Long uid = buttonEventParam.getBaseParam().getUid();
        Long stemp = Long.parseLong(bizKey);
        Integer nodeIndex = foolsDay2025RedisManager.getCurrentNodeId(uid);
        Integer value = foolsDay2025RedisManager.getValue(uid);
        if (value < stemp * 500) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前愚人值不足哦～");
        }

        // 计算新位置，考虑循环
        int newNodeIndex = (nodeIndex + stemp.intValue()) % 16;
        log.info("用户[{}]从位置[{}]前进[{}]步到达位置[{}]", uid, nodeIndex, stemp, newNodeIndex);

        // 初始化奖励列表
        List<FoolsDay2025IndexVO.Reward> rewards = new ArrayList<>();

        List<String> rewardList = new ArrayList<>();
        // 检查经过的每个节点是否有奖励
        for (int i = 1; i <= stemp.intValue(); i++) {
            int currentNodeIndex = (nodeIndex + i) % 16;
            String nodeCode = FoolsDay2025Enums.NodeEnum.getNodeCodeByNodeId(currentNodeIndex);
            rewardList.add(nodeCode);
        }
        if (CollUtil.isNotEmpty(rewardList)) {
            List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCodeList(
                    ServicesAppIdEnum.lanling.getAppId(),
                    FoolsDay2025Constant.ACTIVITY_CODE,
                    rewardList);

            if (scenePrizeDOList != null && !scenePrizeDOList.isEmpty()) {
                for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
                    FoolsDay2025IndexVO.Reward reward = FoolsDay2025IndexVO.Reward.builder()
                            .giftCode(scenePrizeDO.getPrizeValue())
                            .giftIcon(scenePrizeDO.getPrizeIcon())
                            .giftName(scenePrizeDO.getPrizeDesc())
                            .giftNum(scenePrizeDO.getPrizeNum())
                            .giftValue(scenePrizeDO.getPrizeValueGold().intValue())
                            .build();
                    rewards.add(reward);
                    //埋点
                    foolsDay2025TrackManager.allActivityReceiveAward("fool_theater",scenePrizeDO.getPrizeValue(),scenePrizeDO.getPrizeValueGold(),scenePrizeDO.getPrizeNum(),uid);
                }
                recordDrawLog(buttonEventParam.getBaseParam(),FoolsDay2025Constant.ACTIVITY_CODE,scenePrizeDOList);
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, uid)).collect(Collectors.toList())
                );
            }
        }


        // 更新用户位置
        foolsDay2025RedisManager.incrementCurrentNodeId(uid, stemp);
        foolsDay2025RedisManager.decrementValue(uid, stemp * 500);
        return FoolsDay2025ProceedResponse.builder()
                .currentNodeId(newNodeIndex)
                .rewardList(rewards) // 修改为返回奖励列表
                .build();
    }

    protected void recordDrawLog(BaseParam baseParam, String activityCode, List<ScenePrizeDO> prizeItemList) {
        List<DrawPoolItemDTO> drawPoolItemDTOS = buildExchangeItemList(prizeItemList);
        logComponent.putDrawLog(baseParam, activityCode, drawPoolItemDTOS);
    }

    private List<DrawPoolItemDTO> buildExchangeItemList(List<ScenePrizeDO> scenePrizeDOList) {
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        for(ScenePrizeDO scenePrizeDO: scenePrizeDOList) {
            DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                    .itemName(String.format("%s*%s",scenePrizeDO.getPrizeDesc(),scenePrizeDO.getPrizeNum()))
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .itemKey(scenePrizeDO.getPrizeValue())
                    .itemNum(1)
                    .itemValueGold(scenePrizeDO.getPrizeValueGold())
                    .status(1)
                    .poolCode(FoolsDay2025Constant.REWARD_RECORD)
                    .build();
            DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                    .targetTimes(1)
                    .drawPoolItemDO(drawPoolItemDO).build();
            prizeItemList.add(drawPoolItemDTO);
        }
        return prizeItemList;
    }

}
