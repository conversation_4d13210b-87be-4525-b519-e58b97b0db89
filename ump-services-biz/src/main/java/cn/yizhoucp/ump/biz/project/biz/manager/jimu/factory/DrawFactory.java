package cn.yizhoucp.ump.biz.project.biz.manager.jimu.factory;


import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.ActivityInfoKeyEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.DrawManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.beans.Introspector;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 抽奖玩法工厂
 *
 * @author: lianghu
 */
@Slf4j
@Component
public class DrawFactory {

    @Resource
    private List<DrawManager> drawManagers;

    private static final Map<String, DrawManager> instanceMap = new ConcurrentHashMap();

    @PostConstruct
    public void init() {
        for (DrawManager drawManager : drawManagers) {
            //获取实现类的beanName
            String simpleName = AopUtils.getTargetClass(drawManager).getSimpleName();
            String beanName = Introspector.decapitalize(simpleName);
            //放入缓存
            instanceMap.put(beanName,drawManager);
        }
        log.debug("instanceMap {}", instanceMap);
    }

    public DrawManager getInstance() {
        String strategy = (String) ActivityInfoUtil.getConfig().get(ActivityInfoKeyEnum.DRAW_STRATEGY.getCode());
        log.debug("strategy {}", strategy);
        if (StringUtils.isBlank(strategy)) {
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug("drawManager {}", instanceMap.get(strategy));
        }
        return instanceMap.get(strategy);
    }

}
