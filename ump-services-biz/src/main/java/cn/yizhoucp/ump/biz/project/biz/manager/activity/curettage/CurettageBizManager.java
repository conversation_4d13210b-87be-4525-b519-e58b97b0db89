package cn.yizhoucp.ump.biz.project.biz.manager.activity.curettage;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawEvent;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.curettage.CurettageConstant.*;

@Service
@Slf4j
public class CurettageBizManager {

    @Resource
    private CurettageDataManager curettageDataManager;

    @Resource
    private CurettageRankManager curettageRankManager;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private CurettageTrackManager curettageTrackManager;

    @Resource
    private CurettageDateHelpManager helper;

//    @EventListener
    @ActivityCheck(activityCode = "curettage", isThrowException = false)
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void onDraw(AstrologyDrawEvent event) {
        AstrologyDrawMessage drawInfo = event.getSource();
        Long uid = drawInfo.getUid();
        Integer times = drawInfo.getConsumeTimes();

        // 加入已占星用户中
        curettageDataManager.addDrawPerson(uid);

        // 累计今日占星次数
        curettageDataManager.addDrawCount(uid, times);

        // 累计今日全服占星次数
        Integer todayIndex = helper.getTodayIndex();
        if (todayIndex == null) {
            log.error("冲刺活动已结束");
            return;
        }
        // 累计普通任务
        long taskAddDrawTimes = times;
        int valueCoin = CHANGE_RULE_VALUE_COIN[todayIndex % ONE_STEP_DAYS];
        Long allAddTimes = 0L;
        if (valueCoin == -1) {
            allAddTimes = (long) times;
        } else {
            for (DrawPoolItemDTO drawPoolItemDTO : drawInfo.getDrawResult()) {
                if (drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold().equals((long) valueCoin)) {
                    allAddTimes += drawPoolItemDTO.getTargetTimes();
                }
            }
        }

        if (allAddTimes > 0) {
            Long afterTimes = curettageDataManager.incrTodayChangeTimes(allAddTimes);
            int changeRuleNeedTime = CHANGE_RULE_NEED_TIMES[todayIndex % ONE_STEP_DAYS];
            if (afterTimes - allAddTimes < changeRuleNeedTime && afterTimes >= changeRuleNeedTime) {
                afterFinishNormalTask(todayIndex);
                if (valueCoin == -1) {
                    taskAddDrawTimes = afterTimes - changeRuleNeedTime;
                }
            }
        }

        log.info("times -> {}, taskAddDrawTimes-> {}", times, taskAddDrawTimes);
        // 累计超级任务
        if (todaySuperChangeOpen()) {
            int superValueCoin = SUPER_CHANGE_RULE_VALUE_COIN[todayIndex % ONE_STEP_DAYS];
            Long superAllAddTimes = 0L;
            if (valueCoin == -1) {
                superAllAddTimes = taskAddDrawTimes;
            } else {
                for (DrawPoolItemDTO drawPoolItemDTO : drawInfo.getDrawResult()) {
                    if (drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold().equals((long) superValueCoin)) {
                        superAllAddTimes += drawPoolItemDTO.getTargetTimes();
                    }
                }
            }

            if (superAllAddTimes > 0) {
                Long afterTimes = curettageDataManager.incrTodaySuperChangeTimes(superAllAddTimes);
                int changeRuleNeedTime = SUPER_CHANGE_RULE_NEED_TIMES[todayIndex % ONE_STEP_DAYS];
                if (afterTimes - superAllAddTimes < changeRuleNeedTime && afterTimes >= changeRuleNeedTime) {
                    afterFinishSuperTask(todayIndex);
                }
            }
        }

        // 累计榜单
        curettageRankManager.incrRank(uid, times.longValue());
    }

    private void afterFinishSuperTask(Integer todayIndex) {
        // 替换礼物
        List<ScenePrizeDO> listBySceneCode = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, String.format("change-task-super-%s", todayIndex));
        ScenePrizeDO scenePrizeDO = listBySceneCode.get(0);
        curettageDataManager.setReplaceGift(scenePrizeDO.getPrizeValueGold(),
                PrizeItem.builder()
                .prizeKey(scenePrizeDO.getPrizeValue())
                .prizeIcon(scenePrizeDO.getPrizeIcon())
                .prizeName(scenePrizeDO.getPrizeDesc())
                .valueGold(scenePrizeDO.getPrizeValueGold().intValue()).build());

        curettageDataManager.recordReplaceSuper(todayIndex);

        curettageTrackManager.finishTodaySuperTask(MDCUtil.getCurUserIdByMdc());
    }

    private void afterFinishNormalTask(Integer todayIndex) {
        // 替换礼物
        List<ScenePrizeDO> listBySceneCode = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, String.format("change-task-normal-%s", todayIndex));
        ScenePrizeDO scenePrizeDO = listBySceneCode.get(0);
        curettageDataManager.setReplaceGift(scenePrizeDO.getPrizeValueGold(),
                PrizeItem.builder()
                        .prizeKey(scenePrizeDO.getPrizeValue())
                        .prizeIcon(scenePrizeDO.getPrizeIcon())
                        .prizeName(scenePrizeDO.getPrizeDesc())
                        .valueGold(scenePrizeDO.getPrizeValueGold().intValue()).build());

        curettageDataManager.recordReplaceNormal(todayIndex);

        curettageTrackManager.finishTodayNormalTask(MDCUtil.getCurUserIdByMdc());
    }

    @ActivityCheck(activityCode = "curettage", isThrowException = false)
    public Map<Long, PrizeItem> getReplacePrize() {
        return curettageDataManager.getReplaceGiftMap();
    }



    public boolean todaySuperChangeOpen() {
        if (todayNormalFinish()) {
            int hour = helper.getHour();
            if (hour >= SUPER_START_HOUR && hour < SUPER_END_HOUR) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    private boolean todayNormalFinish() {
        Long todayChangeTimes = curettageDataManager.getTodayChangeTimes();
        if (todayChangeTimes >= CHANGE_RULE_NEED_TIMES[helper.getTodayIndex() % ONE_STEP_DAYS]) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
