package cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.goddess_pageant.common.GoddessPageantRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 女神评选排行榜类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 15:19 2025/3/18
 */
@Slf4j
@Service
public class GoddessPageantRankManager extends AbstractRankManager {
  @Resource private GoddessPageantRedisManager goddessPageantRedisManager;
  @Resource private ScenePrizeJpaDAO scenePrizeJpaDAO;
  @Resource private NotifyComponent notifyComponent;
  @Resource private SendPrizeManager sendPrizeManager;

  @Resource private GoddessPageantTrackManager goddessPageantTrackManager;

  @Override
  protected void postProcess(RankContext rankContext) {}

  @Override
  protected void doPreProcess(RankContext rankContext) {}

  @Override
  public Boolean sendPrize(RankContext rankContext) {
    String rank = rankContext.getRankKey();
    if (rank != null && rank.contains(GoddessPageantConstant.DAILY_RANK_CODE)) {
      // 下发日榜奖励
      sendRankPrize(
          GoddessPageantConstant.WOMAN_DAILY_RANK_CODE,
          goddessPageantRedisManager.getDailyRankKey(GoddessPageantConstant.WOMAN_DAILY_RANK_CODE));
      sendRankPrize(
          GoddessPageantConstant.MAN_DAILY_RANK_CODE,
          goddessPageantRedisManager.getDailyRankKey(GoddessPageantConstant.MAN_DAILY_RANK_CODE));
    } else {
      // 下发总榜奖励
      sendRankPrize(
          GoddessPageantConstant.WOMAN_TOTAL_RANK_CODE,
          goddessPageantRedisManager.getTotalRankKey(GoddessPageantConstant.WOMAN_TOTAL_RANK_CODE));
      sendRankPrize(
          GoddessPageantConstant.MAN_TOTAL_RANK_CODE,
          goddessPageantRedisManager.getTotalRankKey(GoddessPageantConstant.MAN_TOTAL_RANK_CODE));
    }

    return Boolean.TRUE;
  }

  private Boolean sendRankPrize(String rankCode, String rankKey) {
    List<ScenePrizeDO> scenePrizeDOList =
        scenePrizeJpaDAO.getListBySceneCode(1L, GoddessPageantConstant.ACTIVITY_CODE, rankCode);
    log.info("scenePrizeDOList {}", JSON.toJSONString(scenePrizeDOList));
    if (CollectionUtils.isEmpty(scenePrizeDOList)) {
      return Boolean.FALSE;
    }

    RankVO rankVO =
        this.getRank(
            RankContext.builder()
                .activityCode(GoddessPageantConstant.ACTIVITY_CODE)
                .rankKey(rankKey)
                .rankLen(10L)
                .type(RankContext.RankType.user)
                .build());
    log.info("rankVO {}", rankVO);
    if (rankVO == null) {
      return Boolean.FALSE;
    }

    List<RankItem> rankList = rankVO.getRankList();
    if (CollectionUtils.isEmpty(rankList)) {
      return Boolean.FALSE;
    }

    Long rankValueLimit = GoddessPageantEnums.RankValueLimit.getLimitByRank(rankCode);
    if (rankValueLimit == null) {
      log.info("rank:{} 未找到对应的奖励值限制", rankCode);
      return Boolean.FALSE;
    }
    for (RankItem rankItem : rankList) {
      Long rank = rankItem.getRank();
      Long value = rankItem.getValue();
      if (value < rankValueLimit) {
        log.info("rank:{} 小于最小奖励值 {}", rankItem, rankValueLimit);
        continue;
      }
      List<ScenePrizeDO> scenePrizeDOs =
          scenePrizeDOList.stream()
              .filter(scenePrizeDO -> (Objects.equals(rank, scenePrizeDO.getPrizeBelongToRank())))
              .collect(Collectors.toList());
      log.info("GoddessPagentRankManager#sendPrize cpRankItem {}", rankItem);
      if (CollUtil.isEmpty(scenePrizeDOs)) {
        continue;
      }
      sendPrizeManager.sendPrize(
          BaseParam.builder()
              .appId(ServicesAppIdEnum.lanling.getAppId())
              .unionId(ServicesAppIdEnum.lanling.getUnionId())
              .uid(rankItem.getId())
              .build(),
          scenePrizeDOs.stream()
              .map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, rankItem.getId()))
              .collect(Collectors.toList()));
      String msg = GoddessPageantEnums.RankValueLimit.getMsgByRankCode(rankCode);
      if (msg != null) {
        notifyComponent.npcNotify(
            ServicesAppIdEnum.lanling.getUnionId(),
            rankItem.getId(),
            String.format(msg, rankItem.getRank()));
      }
      String trackCode = GoddessPageantEnums.RankValueLimit.getTrackCodeByRankCode(rankCode);
      for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
        // 埋点
        goddessPageantTrackManager.allActivityReceiveAward(
            trackCode,
            scenePrizeDO.getPrizeValue(),
            scenePrizeDO.getPrizeValueGold(),
            scenePrizeDO.getPrizeNum(),
            rankItem.getId());
      }
    }
    return Boolean.TRUE;
  }
}
