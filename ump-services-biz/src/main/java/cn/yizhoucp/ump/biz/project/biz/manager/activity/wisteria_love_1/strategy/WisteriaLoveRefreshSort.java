package cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.AccountReduceType;
import cn.yizhoucp.ms.core.base.enums.AppScene;
import cn.yizhoucp.ms.core.base.enums.CoinBusinessType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.AccountBalanceChangeResultVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.WisteriaLove1TrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.common.WisteriaLove1Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.common.WisteriaLove1RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.wisteria_love_1.vo.WisteriaLove1IndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WisteriaLoveRefreshSort implements ExecutableStrategy {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    
    @Resource
    private WisteriaLove1RedisManager wisteriaLove1RedisManager;
    @Resource
    private UserCoinAccountManager userCoinAccountManager;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private RedisManager redisManager;

    @Resource
    private WisteriaLove1TrackManager wisteriaLove1TrackManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        // 获取用户ID
        Long uid = buttonEventParam.getBaseParam().getUid();
        String bizKey = buttonEventParam.getBizKey();
        BaseParam baseParam = buttonEventParam.getBaseParam();
        if(!"free".equals(bizKey)){
            //扣减金币
            AccountBalanceChangeResultVO changeResultVO = userCoinAccountManager.reduceCoinWithResult(baseParam.getUnionId(), baseParam.getAppId(), baseParam.getUid(), null, AppScene.activity, LoveInProgressConstant.ACTIVITY_CODE,
                    500L, 0L, AccountReduceType.fix, LoveInProgressConstant.ACTIVITY_CODE, CoinBusinessType.DEDUCT_COIN.getCode(), cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressConstant.MEMO);
            if (null == changeResultVO || !changeResultVO.getResult()) {
                throw new ServiceException(ErrorCode.COIN_ACCOUNT_BALANCE_LESS, LoveInProgressConstant.COIN_LESS_TOAST);
            }
            wisteriaLove1TrackManager.allActivityTaskFinish(uid,"sucessfully_change_gifts");
            //每天只能刷新两次
            if(wisteriaLove1RedisManager.incrementShortRefreshCount(uid) > 2){
                throw new ServiceException(ErrorCode.INVALID_PARAM, "今日已刷新两次");
            }
        }
        // 获取商品池中的所有商品
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(
                ServicesAppIdEnum.lanling.getAppId(),
                WisteriaLove1Constant.ACTIVITY_CODE, 
                WisteriaLove1Constant.PURCHASE_SCENE_CODE
        );
        
        if (CollUtil.isEmpty(scenePrizeDOS)) {
            log.error("刷新商店失败，商品池为空, uid:{}", uid);
            return new ArrayList<>();
        }
        
        // 获取当前商店中的商品
        List<ScenePrizeDO> currentGifts = wisteriaLove1RedisManager.getShortGiftList(uid);
        
        // 过滤掉当前已有的商品
        List<ScenePrizeDO> availableGifts = scenePrizeDOS;
        if (CollUtil.isNotEmpty(currentGifts)) {
            List<String> currentGiftKeys = currentGifts.stream()
                    .map(ScenePrizeDO::getPrizeValue)
                    .collect(Collectors.toList());
            
            availableGifts = scenePrizeDOS.stream()
                    .filter(gift -> !currentGiftKeys.contains(gift.getPrizeValue()))
                    .collect(Collectors.toList());
        }
        
        // 如果过滤后的商品池小于4个，则使用原始商品池（可能会出现重复商品）
        if (availableGifts.size() < 4) {
            availableGifts = scenePrizeDOS;
        }
        
        // 随机选择4个商品
        List<ScenePrizeDO> newGifts = RandomUtil.randomEleList(availableGifts, 4);

        //刷新商品
        wisteriaLove1RedisManager.refreshShortGiftList(uid, newGifts);
        //构建广播消息
        wisteriaLove1RedisManager.recoredBroadCast(uid,newGifts);
        // 构建返回结果
        return newGifts.stream().map(gift -> WisteriaLove1IndexVO.GiftVO.builder()
                .giftIcon(gift.getPrizeIcon())
                .giftKey(gift.getPrizeValue())
                .giftName(gift.getPrizeDesc())
                .giftValue(gift.getPrizeValueGold().intValue())
                .desc("礼物卡")
                .maxPurchaseLimit(10)
                .purchasedCount(wisteriaLove1RedisManager.getGiftPurchasedCount(uid, gift.getPrizeValue()))
                .build()).collect(Collectors.toList());
    }
}