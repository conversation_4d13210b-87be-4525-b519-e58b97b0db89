package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.AccountReduceType;
import cn.yizhoucp.ms.core.base.enums.AppScene;
import cn.yizhoucp.ms.core.base.enums.CoinBusinessType;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.AccountBalanceChangeResultVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.EmotionalBondsIndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.EmotionalBondsTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.EmotionalBondsBuyResponse;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.manager.userCoinAccount.UserCoinAccountManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondRelationshipDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.ArrayListMultimap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmotionalBondsBuyStrategy implements ExecutableStrategy {

    @Resource
    private UserCoinAccountManager userCoinAccountManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private DrawPoolService drawPoolService;
    @Resource
    private EmotionalBondsIndexManager emotionalBondsIndexManager;
    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;

    private static final String ALL_PACKAGE_CODE = "all";

    @Resource
    private EmotionalBondsTrackManager emotionalBondsTrackManager;


    @Override
    public EmotionalBondsBuyResponse execute(ButtonEventParam buttonEventParam) {
        BaseParam baseParam = buttonEventParam.getBaseParam();
        String bizKey = buttonEventParam.getBizKey();
        Long coin = 0L;
        List<DrawPoolDO> packageList;
        if (ALL_PACKAGE_CODE.equals(bizKey)) {
            packageList = drawPoolService.findByActivityCode(EmotionalBondsConstant.ACTIVITY_CODE);
            coin = EmotionalBondsConstant.ALL_GIFT_PRICE;
        } else {
            packageList = drawPoolService.getByActivityCodeAndBizKey(EmotionalBondsConstant.ACTIVITY_CODE, bizKey);
            boolean earlyStage = emotionalBondsIndexManager.isEarlyStage();
            if (!earlyStage) {
                coin = packageList.stream().mapToLong(DrawPoolDO::getSingleCoin).sum();
            } else {
                coin = packageList.stream().map(item -> {
                    String extData = item.getExtData();
                    JSONObject jsonObject = JSONObject.parseObject(extData);
                    return jsonObject.getLong(EmotionalBondsConstant.DISCOUNT_PRICE_CODE);
                }).mapToLong(item -> item).sum();
            }
        }
        if (CollectionUtils.isEmpty(packageList)) {
            log.error(" EmotionalBondsBuyStrategy packageList is empty");
            throw new ServiceException(ErrorCode.MISS_PARAM, "奖池列表为空");
        }
        List<String> poolCodes = packageList.stream().map(DrawPoolDO::getPoolCode).collect(Collectors.toList());

        // 扣减金币
        AccountBalanceChangeResultVO changeResultVO =
                userCoinAccountManager.reduceCoinWithResult(
                        baseParam.getUnionId(),
                        baseParam.getAppId(),
                        baseParam.getUid(),
                        null,
                        AppScene.activity,
                        EmotionalBondsConstant.ACTIVITY_CODE,
                        coin,
                        0L,
                        AccountReduceType.fix,
                        EmotionalBondsConstant.ACTIVITY_CODE,
                        CoinBusinessType.DEDUCT_COIN.getCode(),
                        EmotionalBondsConstant.MEMO);
        if (null == changeResultVO || !changeResultVO.getResult()) {
            throw new ServiceException(
                    ErrorCode.COIN_ACCOUNT_BALANCE_LESS, LoveInProgressConstant.COIN_LESS_TOAST);
        }
        try {
            //获取奖励
            ArrayListMultimap<String, DrawPoolItemDO> byPoolCodes = drawPoolItemService.getByPoolCodes(poolCodes);
            if (ObjectUtil.isEmpty(byPoolCodes)) {
                log.error("目标奖池不存在 packageCode:{}", bizKey);
                throw new ServiceException(ErrorCode.MISS_PARAM, "奖池列表为空");
            }
            List<DrawPoolItemDO> drawPoolItemDOList = new ArrayList<>(byPoolCodes.values());
            //过滤掉装扮
            drawPoolItemDOList = drawPoolItemDOList.stream().filter(item -> !DressUpType.entry_special_effect.getCode().equals(item.getItemSubType())).collect(Collectors.toList());

            List<DrawPoolItemDTO> prizeList=new ArrayList<>();
            for(DrawPoolItemDO drawPoolItemDO : drawPoolItemDOList){
                prizeList.add(DrawPoolItemDTO.builder().drawPoolItemDO(drawPoolItemDO)
                                .targetTimes(1)
                        .build());
            }
            //下发奖励
            sendPrizeManager.sendPrize(buttonEventParam.getBaseParam(), prizeList.stream().map(SendPrizeDTO::of).collect(Collectors.toList()));
            for (DrawPoolDO drawPoolDO : packageList) {
                EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(drawPoolDO.getPoolCode());
                if(rolePairEnum==null){
                    continue;
                }
                Instant newExpireTime = Instant.ofEpochMilli(new Date().getTime())
                        .plus(rolePairEnum.getExpireTime(), ChronoUnit.DAYS);
                //插入新关系
                EmotionalBondRelationshipDO newRelationship = new EmotionalBondRelationshipDO();
                newRelationship.setUserId(buttonEventParam.getBaseParam().getUid());
                newRelationship.setExpireTime(Date.from(newExpireTime));
                newRelationship.setEffectKey(rolePairEnum.name());
                newRelationship.setStatus(0);
                emotionalBondRelationshipService.save(newRelationship);
                if(!ALL_PACKAGE_CODE.equals(bizKey)){
                    return EmotionalBondsBuyResponse.builder().bondId(newRelationship.getId()).build();
                }
            }
            //添加埋点
            if(ALL_PACKAGE_CODE.equals(bizKey)){
                emotionalBondsTrackManager.allActivityTaskFinish(baseParam.getUid(),"suceessfully_buy_all");
            }else{
                EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(bizKey);
                emotionalBondsTrackManager.allActivityTaskFinish(baseParam.getUid(),rolePairEnum.getTrackKey());
            }
        } catch (Exception e) {
            log.warn("下发奖励失败", e);
            return EmotionalBondsBuyResponse.builder().build();
        }
        return EmotionalBondsBuyResponse.builder().build();
    }
}
