package cn.yizhoucp.ump.biz.project.biz.manager.activity.mayConfession;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class MayConfessionTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    public void allActivityTaskFinish(String awardModule, String taskType, String awardKey, String award, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "romantic_june");
        params.put("attribute_type", "official_announcement_activity");
        params.put("award_module", awardModule);
        params.put("task_type", taskType);
        params.put("award_key", awardKey);
        params.put("award", award);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    public void allActivityReceiveAward(String awardModule, String taskType, String awardKey, Long awardAmount, Integer awardCount, String poolCode, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(8);
        params.put("activity_type", "Date_on_qixi");
        params.put("attribute_type", "official_announcement_activity");
        params.put("award_module", awardModule);
        params.put("task_type", taskType);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        params.put("pool_code", poolCode);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

}
