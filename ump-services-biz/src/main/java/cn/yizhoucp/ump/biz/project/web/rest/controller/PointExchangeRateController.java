package cn.yizhoucp.ump.biz.project.web.rest.controller;

import cn.yizhoucp.ump.biz.project.biz.manager.pointExchangeRate.PointExchangeRateManager;
import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.ump.api.vo.redPacket.PointExchangeRateExamineVO;
import cn.yizhoucp.ump.api.vo.redPacket.PointExchangeRateVO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积分汇率相关接口
 *
 * <AUTHOR>
 */
@RestController
public class PointExchangeRateController {

    @Resource
    PointExchangeRateManager pointExchangeRateManager;

    /**
     * 根据场景获取积分汇率（最多支持 3 位小数）
     *
     * @param unionId 应用唯一标识
     * @param scene   场景
     * @return Double
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @RequestMapping("/api/inner/point-exchange-rate/get-rate-by-scene")
    public Result<Double> getPointExchangeRateByScene(String unionId, String scene) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.getPointExchangeRateByScene(unionId, scene));
    }

    /**
     * 获取所有积分汇率场景
     *
     * @return List<JSONObject>
     */
    @GetMapping("/api/admin/point-exchange-rate/get-scene")
    public Result<List<JSONObject>> getPointExchangeRateScene() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.getPointExchangeRateScene());
    }

    /**
     * 根据 unionId 分页获取积分汇率
     *
     * @param page    当前页码
     * @param size    每页条数
     * @param unionId 应用唯一标识
     * @param scene   场景
     * @param status  涨停
     * @return AdminPageVO<PointExchangeRateVO>
     */
    @GetMapping("/api/admin/point-exchange-rate/get-list")
    public Result<AdminPageVO<PointExchangeRateVO>> getPointExchangeRateList(Integer page, Integer size,
                                                                             @RequestParam(value = "unionId", required = false) String unionId,
                                                                             @RequestParam(value = "scene", required = false) String scene,
                                                                             @RequestParam(value = "status", required = false) String status) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.getPointExchangeRateList(page, size, unionId, scene, status));
    }

    /**
     * 新增或更新积分汇率
     *
     * @param perVo 积分汇率
     * @return CommonResultVO
     */
    @PostMapping("/api/admin/point-exchange-rate/save-or-update")
    public Result<CommonResultVO> saveOrUpdate(@RequestBody PointExchangeRateVO perVo) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.saveOrUpdate(perVo));
    }

    /**
     * 删除积分汇率记录
     *
     * @param perId    积分汇率id
     * @param reason   理由
     * @param operator 操作者
     * @return CommonResultVO
     */
    @GetMapping("/api/admin/point-exchange-rate/delete")
    public Result<CommonResultVO> deletePerInfo(Long perId, String reason, String operator) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.deletePerInfo(perId, reason, operator));
    }

    /**
     * 根据 unionId 分页获取积分汇率
     *
     * @param page    当前页码
     * @param size    每页条数
     * @param unionId 应用唯一标识
     * @param status  状态
     * @return AdminPageVO<PointExchangeRateVO>
     */
    @GetMapping("/api/admin/point-exchange-rate-examine/get-list")
    public Result<AdminPageVO<PointExchangeRateExamineVO>> getPerExamineList(Integer page, Integer size,
                                                                             @RequestParam(value = "unionId", required = false) String unionId,
                                                                             @RequestParam(value = "status", required = false) String status) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.getPerExamineList(page, size, unionId, status));
    }

    /**
     * 审核
     *
     * @param id       审核记录id
     * @param type     操作类型 0-通过；1-拒绝
     * @param operator 审核人
     * @return CommonResultVO
     */
    @GetMapping("/api/admin/point-exchange-rate-examine/examine")
    public Result<CommonResultVO> examineOperate(Long id, Integer type, String operator) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> pointExchangeRateManager.examineOperate(id, type, operator));
    }

}
