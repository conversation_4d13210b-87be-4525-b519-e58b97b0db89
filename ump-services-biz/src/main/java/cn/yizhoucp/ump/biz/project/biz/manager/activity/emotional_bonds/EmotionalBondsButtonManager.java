package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonRankListParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RewardVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy.EmotionalBondsStrategyChoose;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractButtonManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
@Slf4j
public class EmotionalBondsButtonManager extends AbstractButtonManager {

    @Resource
    private EmotionalBondsStrategyChoose strategyChoose;
    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;
    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;





    @Override
    public Object event(ButtonEventParam buttonEventParam) {
        return strategyChoose.executeStrategy(buttonEventParam);
    }

    @Override
    public RankVO rankList(ButtonRankListParam buttonRankListParam) {
        ButtonRankListParam buttonFriendParam = new ButtonRankListParam();
        Long rankLen= emotionalBondsRedisManager.getRankLength();
        buttonFriendParam.setBaseParam(buttonRankListParam.getBaseParam());
        buttonFriendParam.setRankKey(emotionalBondsRedisManager.getRankKey());
        buttonFriendParam.setSupportDiff(Boolean.TRUE);
        buttonFriendParam.setRankLen(rankLen);
        buttonFriendParam.setActivityCode(buttonFriendParam.getActivityCode());
        buttonFriendParam.setListType(RankContext.RankType.cp.getType());
        Long otherUid= emotionalBondsRedisManager.getTopPairedFriend(buttonFriendParam.getBaseParam().getUid());
        buttonFriendParam.setOtherUid(otherUid);
        RankVO rankVO = super.rankList(buttonFriendParam);
        hideAvatar(rankVO, buttonRankListParam.getBaseParam());
        //设置盲盒展示
        setReward(rankVO, buttonRankListParam.getBaseParam());
        return rankVO;
    }

    private void setReward(RankVO rankVO, BaseParam baseParam) {
        List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
        if (CollectionUtil.isEmpty(cpRankItemList)) {
            return;
        }

        for (CpRankItem cpRankItem : cpRankItemList) {
            if (cpRankItem == null || cpRankItem.getRelationId() == null) {
                continue;
            }


            Long userId1 = cpRankItem.getMaleUid();
            Long userId2 = cpRankItem.getFemaleUid();

            // 查询关系记录
            List<EmotionalBondSummaryDO> relationSummaryList = emotionalBondSummaryService.getByMergedUserPair(cpRankItem.getRelationId());
            if (relationSummaryList == null||  relationSummaryList.isEmpty()) {
                continue;
            }
            // 构建奖励信息
            List<RewardVO> rewardVOS = new ArrayList<>();
            relationSummaryList = new ArrayList<>(relationSummaryList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            EmotionalBondSummaryDO::getEffectKey,
                            e -> e,
                            (e1, e2) -> e1
                    ))
                    .values());
            for(EmotionalBondSummaryDO relationSummary : relationSummaryList) {

                // 获取关系的key
                String effectKey = relationSummary.getEffectKey();
                if (effectKey == null) {
                    continue;
                }

                // 获取关系名称
                EmotionalBondsEnums.RolePairEnum rolePair = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effectKey);
                String iconA = EmotionalBondsEnums.RoleIconEnum.getIconByRole(rolePair.getRoleA());
                String relationName = rolePair.getEffectName();

                RewardVO rewardVO = new RewardVO();
                rewardVO.setRewardName(relationName);
                rewardVO.setIcon(String.format(EmotionalBondsConstant.BASE_ICON_URL, iconA));
                rewardVO.setValue(0L);
                rewardVOS.add(rewardVO);
            }
            cpRankItem.setRewardVOList(rewardVOS);
        }
    }

    private Long getOtherUid(Long uid) {
        if (uid == null) {
            return null;
        }
        Long relationshipId=emotionalBondsRedisManager.getEquippedRelationship(uid);
        if(ObjectUtil.isEmpty(relationshipId)){
            return null;
        }
        EmotionalBondSummaryDO emotionalBondSummaryDO = emotionalBondSummaryService.getById(relationshipId);
        if(emotionalBondSummaryDO!=null){
            Long oppositeUid = emotionalBondSummaryDO.getOppositeId();
            Long userId = emotionalBondSummaryDO.getUserId();
            return Objects.equals(uid, userId) ? oppositeUid : userId;
        }
        return null;
    }


    private void hideAvatar(RankVO rankVO, BaseParam baseParam) {

        try {
            List<CpRankItem> cpRankItemList = rankVO.getCpRankItemList();
            CpRankItem myCpRankItem = rankVO.getMyCpRankItem();
            Boolean isNull = ObjectUtil.isEmpty(myCpRankItem);
            log.info("MainChineseNewYear2025ButtonManager#postProcess cpRankItemList:{}", JSON.toJSONString(cpRankItemList));
            for (CpRankItem cpRankItem : cpRankItemList) {
                if (cpRankItem.getMaleUid() == null || cpRankItem.getFemaleUid() == null) {
                    continue;
                }
                if (Boolean.TRUE.equals(isNull) || (!cpRankItem.getMaleUid().equals(baseParam.getUid()) && !cpRankItem.getFemaleUid().equals(baseParam.getUid()))) {
                    cpRankItem.setMaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                    cpRankItem.setFemaleAvatar("https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png");
                    cpRankItem.setMaleUserName("神秘嘉宾");
                    cpRankItem.setFemaleUserName("神秘嘉宾");
                }
            }
        } catch (Exception e) {
            log.warn("LoveInBloomFestivalButtonManager#postProcess error {}", e.getMessage());
        }
    }





}
