package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSeaTour;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.CRYSTAL_NUM;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.REPLACE_GIFT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.STAR_EXPLORE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.STAR_EXPLORE_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarSeaTourConstant.SUPPLY;

@Service
@Slf4j
public class StarSeaTourDrawManager extends AbstractDrawTemplate {

    public static final List<String> STAR_POOL_CODE = Arrays.asList("1_STAR",
            "2_STAR",
            "3_STAR",
            "4_STAR",
            "5_STAR",
            "6_STAR",
            "7_STAR",
            "8_STAR",
            "9_STAR",
            "10_STAR");

    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private RedisManager redisManager;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;
    @Resource
    private StarSeaTourBizManager starSeaTourBizManager;
    @Resource
    private StarSeaTourIndexManager starSeaTourIndexManager;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        if (STAR_POOL_CODE.contains(drawParam.getPoolCode())) {
            drawParam.setNoSendPrize(Boolean.TRUE);

            Integer crystalNum = Optional.ofNullable(redisManager.getInteger(String.format(CRYSTAL_NUM, drawParam.getUid(), starSeaTourBizManager.getDate()))).orElse(1);
            if (crystalNum <= 0) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "您当前没有水晶，无法进行守护");
            }

//            Integer crystalStar = Optional.ofNullable(redisManager.getInteger(String.format(CRYSTAL_STAR, drawParam.getUid(), starSeaTourBizManager.getDate()))).orElse(1);
//            drawParam.setPoolCode(String.format(STAR_POOL_CODE, crystalStar));

            redisManager.decrLong(String.format(CRYSTAL_NUM, drawParam.getUid(), starSeaTourBizManager.getDate()), 1L, DateUtil.ONE_MONTH_SECOND);
//            redisManager.delete(String.format(CRYSTAL_STAR, drawParam.getUid(), starSeaTourBizManager.getDate()));

            return;
        }

        for (StarSeaTourConstant.Star star : StarSeaTourConstant.Star.values()) {
            if (star.name().equals(drawParam.getPoolCode())) {
                int supply = Optional.ofNullable(redisManager.getInteger(String.format(SUPPLY, drawParam.getUid(), starSeaTourBizManager.getDate()))).orElse(0);
                if (supply < star.getSupply()) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您的占星次数不足，请先占星吧～");
                }

                if (!Boolean.TRUE.equals(redisManager.setnx(
                        String.format(STAR_EXPLORE, drawParam.getUid(), starSeaTourBizManager.getDate(), star.name()),
                        System.currentTimeMillis(),
                        DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您今日已开启此宝箱，看看其他宝箱吧～");
                }

                redisManager.decrLong(String.format(SUPPLY, drawParam.getUid(), starSeaTourBizManager.getDate()), star.getSupply(), DateUtil.ONE_MONTH_SECOND);

                return;
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void deductResource(DrawContext context) {
    }

    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        for (StarSeaTourConstant.Star star : StarSeaTourConstant.Star.values()) {
            if (star.name().equals(drawParam.getPoolCode())) {
                Long starExploreTimes = redisManager.incrLong(String.format(STAR_EXPLORE_TIMES, drawParam.getUid(), star.name()), 1L, DateUtil.ONE_MONTH_SECOND);
                if (Objects.equals(starExploreTimes, star.getMaxFinishTimes())) {
                    starSeaTourBizManager.allActivityTaskFinish(String.format("sea_star_task_exploration_%s_10", star.name().toLowerCase()), drawParam.getUid());
                }

                notifyComponent.npcNotify(drawParam.getUnionId(), drawParam.getUid(),
                        String.format("恭喜您在“星海巡游记”活动中成功打开【%s宝箱】并获得%s金币礼物“%s”，礼物已发放至您的背包，请注意查收哦~",
                                star.getDesc(),
                                context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemValueGold(),
                                context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemName()));
                starSeaTourIndexManager.allActivityReceiveAward("exploration_planet", star.name().toLowerCase(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemKey(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemValueGold(), context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemNum(), drawParam.getUid());

                return;
            }
        }

        for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            redisManager.hset(String.format(REPLACE_GIFT, drawParam.getUid(), starSeaTourBizManager.getDate()), String.valueOf(drawPoolItemDO.getItemValueGold()), PrizeItem.builder().prizeKey(drawPoolItemDO.getItemKey()).prizeIcon(drawPoolItemDO.getItemIcon()).prizeName(drawPoolItemDO.getItemName()).valueGold(Math.toIntExact(drawPoolItemDO.getItemValueGold())).build());

            replaceGiftTrack(drawPoolItemDO.getItemKey(), drawPoolItemDO.getItemValueGold(), drawParam.getUid());
        }
    }

    private void replaceGiftTrack(String afterGift, Long giftAmount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(3);
        params.put("activity_type", "sea_star_parade_activity");
        params.put("after_gift", afterGift);
        params.put("gift_amount", giftAmount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "replace_gift", params, ServicesNameEnum.ump_services.getCode());
    }

}
