package cn.yizhoucp.ump.biz.project.biz.manager.activity.qixiActivity;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 七夕活动埋点管理
 * <AUTHOR>
 * @since 下午6:47 2024/7/24
 * @version V1.0
 */
@Service
public class QiXiFestivalTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动抽奖
     * @param uid
     * @param poolCode
     * @param taskType
     * @param awardKey
     * @param awardAmount
     */
    public void allActivityLottery(Long uid,String poolCode,String taskType,String awardKey,Long awardAmount){
        Map<String,Object> params= Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type","destiny_on_qixi");
        params.put("pool_code",poolCode);
        params.put("task_type",taskType);
        params.put("award_key",awardKey);
        params.put("award_amount",awardAmount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动任务完成
     * @param uid
     * @param taskType
     * @param poolCode
     * @param awardKey
     * @param awardCount
     */
    public void allActivityTaskFinish(Long uid,String taskType,String poolCode,String awardKey,Integer awardCount)
    {
        Map<String,Object> params= Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type","destiny_on_qixi");
        params.put("attribute_type","platform_activity");
        params.put("task_type",taskType);
        params.put("pool_code",poolCode);
        params.put("award_key",awardKey);
        params.put("award_count",awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(String taskType, String awardKey, Long awardAmount, Integer awardCount, String poolCode, Long uid)
    {
        Map<String,Object> params= Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type","destiny_on_qixi");
        params.put("attribute_type","platform_activity");
        params.put("pool_code",poolCode);
        params.put("award_key",awardKey);
        params.put("award_amount",awardAmount);
        params.put("award_count",awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }
}
