package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.hutool.core.util.StrUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.VestChannelEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.WearTypeEnum;
import cn.yizhoucp.ms.core.dto.dressup.WearDressUpDTO;
import cn.yizhoucp.product.client.UserDressUpFeignService;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
public class EmotionalBondCostume implements ExecutableStrategy {

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;
    @Resource
    private UserDressUpFeignService userDressUpFeignService;
    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        String bizKey = buttonEventParam.getBizKey();
        Long uid = buttonEventParam.getBaseParam().getUid();
        if (StrUtil.isBlank(bizKey) || "null".equals(bizKey)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "没有可以佩戴的特效");
        }

        // 获取要装备的关系记录
        EmotionalBondSummaryDO summaryDO = emotionalBondSummaryService.getById(Long.valueOf(bizKey));
        if (summaryDO == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "关系记录不存在");
        }

        String effectKey = summaryDO.getEffectKey();
        Long relationshipId = Long.valueOf(bizKey);


        // 应用特效
        Boolean result = dressUp(uid, effectKey);
        if (!result) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "特效已过期");
        } else {
            // 将关系 ID 存储到 Redis
            emotionalBondsRedisManager.setEquippedRelationship(uid, relationshipId);
        }
        return Boolean.TRUE;
    }


    public Boolean dressUp(Long uid, String uniqueKey) {
        if (Objects.isNull(uid)) {
            log.warn("unloaded params is blank :{}", uniqueKey);
            return Boolean.FALSE;
        }
        try {
            // 入场特效
            WearDressUpDTO wearDressUp = WearDressUpDTO.builder()
                    .uniqueKey(uniqueKey)
                    .vestChannel(VestChannelEnum.nuanliao)
                    .appId(ServicesAppIdEnum.lanling.getAppId())
                    .uid(uid)
                    .wearType(WearTypeEnum.put_on.getCode())
                    .build();
            return (boolean) userDressUpFeignService.wearDressUp(wearDressUp).getData().getResult();
        } catch (Exception e) {
            log.error("头像下发或穿戴失败 {} uniqueKey:{} caused by {}", uid, uniqueKey, e.toString());
            return Boolean.FALSE;
        }
    }

}
