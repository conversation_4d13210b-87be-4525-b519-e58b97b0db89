package cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DrawWithPoolKeyEnum {

    LUCKY_ENABLE("luckyEnable", "幸运值奖励开关"),
    LUCKY_PRIZE_KEY("luckyPrizeKey", "幸运值奖励KEY"),
    DEFAULT_PRIZE_KEY("defaultPrizeKey", "兜底奖励KEY"),
    LUCKY_LIMIT("luckyLimit", "幸运值阈值"),
    LUCKY_PER_TIME("luckyPerTime", "幸运值累计步长值"),
    POOL_SIZE("poolSize", "奖池大小"),
    ;
    private String code;
    private String desc;

}
