package cn.yizhoucp.ump.biz.project.biz.manager.ruleEngine.ruleBase;

import cn.yizhoucp.ump.biz.project.biz.util.RuleLinkedList;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础关系节点
 * 关系节点不能为叶子节点
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class BaseRelation extends BaseNode {

  /** 关系节点的子节点 */
  private RuleLinkedList<BaseNode> children;

  /**
   * 循环次数
   * 1. 默认 0 不循环
   * 2. 在 and/any 下循环将在 false/true 时终止
   * 3. < 0 是用在and/any下无限循环直至 false/true 终止
   */
  private int loop;

  protected BaseRelation() {
    children = new RuleLinkedList<>();
  }
}
