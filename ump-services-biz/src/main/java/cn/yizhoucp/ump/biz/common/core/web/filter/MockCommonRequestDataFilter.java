package cn.yizhoucp.ump.biz.common.core.web.filter;

import cn.yizhoucp.ump.biz.common.core.web.CommonRequestDataHolder;

import javax.servlet.*;

/**
 *
 * <AUTHOR>
 * @version 0.1 : CommonRequestDataBuilderFilter v0.1 2017/12/5 上午10:55 yanlv Exp $
 */

//@Order(1)
//@Component
//@ServletComponentScan
//@Profile("dev")
//@WebFilter(urlPatterns = "/*", filterName = "mockCommonRequestDataFilter")
public class MockCommonRequestDataFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {

        CommonRequestDataHolder.set(null);

    }

    @Override
    public void destroy() {

    }
}
