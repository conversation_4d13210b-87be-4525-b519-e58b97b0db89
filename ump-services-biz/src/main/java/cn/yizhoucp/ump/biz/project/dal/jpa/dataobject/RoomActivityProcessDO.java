package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.ms.core.base.enums.AuctionProcessStatus;
import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoomActivityProcessDO {

    private Long id;

    /**
     * 语音房id
     */
    private Long roomId;

    /**
     * 竞拍活动步骤
     */
    private AuctionProcessStatus step;

    /**
     * 竞拍内容
     */
    private String content;

    /**
     * 竞拍时长
     */
    private String times;

    /**
     * 对应礼物productId
     */
    private Long giftId;

    /** 状态 */
    private CommonStatus status;

    /**
     * 开始时间戳 10位
     */
    private Long startTime;

    /**
     * 结束时间戳 10位
     */
    private Long endTime;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

}
