package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class EmotionalBondBondStrategy implements ExecutableStrategy {

    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    @Resource
    private NotifyComponent notifyComponent;

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private Environment environment;


    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        // 解析JSON格式的bizKey
        String bizKeyJson = buttonEventParam.getBizKey();
        JSONObject jsonParams = JSONObject.parseObject(bizKeyJson);

        // 从JSON中提取参数
        Long toUid = jsonParams.getLong("toUid");
        String role = jsonParams.getString("role");

        Long userId = buttonEventParam.getBaseParam().getUid();
        String otherRole = EmotionalBondsEnums.RolePairEnum.getPairedRole(role);
        EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnum(role);

        try {
            // 生成邀请ID
            String invitationId = emotionalBondsRedisManager.generateInvitationId();

            // 查询是否存在原始关系记录
            Long originalRelationshipId = jsonParams.getLong("bondId");
            if (originalRelationshipId == null) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "未选择好友");
            }
/*
            // 如果有原始关系表ID，则将其存入Redis
            if (jsonParams.containsKey("bondId")) {
                originalRelationshipId = jsonParams.getLong("bondId");
            } else {
                EmotionalBondRelationshipDO emotionalBondRelationshipDO = emotionalBondRelationshipService.getOne(new LambdaQueryWrapper<EmotionalBondRelationshipDO>()
                        .eq(EmotionalBondRelationshipDO::getUserId, userId)
                        .eq(EmotionalBondRelationshipDO::getEffectKey, rolePairEnum.name())
                        .eq(EmotionalBondRelationshipDO::getStatus, 0)
                        .last("LIMIT 1") // 添加限制条件，确保只返回一条记录
                );
                originalRelationshipId = emotionalBondRelationshipDO != null ? emotionalBondRelationshipDO.getId() : null; // 确保空值处理
            }
*/

            // 将邀请信息存入Redis
            boolean saveResult = emotionalBondsRedisManager.saveInvitation(
                    invitationId,
                    userId,
                    toUid,
                    role,
                    otherRole,
                    rolePairEnum.name(),
                    originalRelationshipId
            );

            if (!saveResult) {
                log.error("保存邀请记录到Redis失败 userId: {}, toUid: {}, role: {}", userId, toUid, role);
                return Boolean.FALSE;
            }
            // 生成活动URL，带上邀请ID
            String url = getActivityUrl(invitationId);
            // 发送消息给被邀请方
            notifyComponent.chatNotify(userId, toUid, String.format("我想和你缔结天生羁绊，快来加入吧～<a href=\"%s\">传送门</a>（点击传送门跳转到活动）", url));
        } catch (Exception e) {
            log.warn("缔结关系失败 {}", e.toString());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private String getActivityUrl(String invitationId) {
        String url = ActivityUrlUtil.getH5BaseUrl(ServicesAppIdEnum.lanling.getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "fetters" + "?invitationId=%s";
        return String.format(url, invitationId);
    }


}
