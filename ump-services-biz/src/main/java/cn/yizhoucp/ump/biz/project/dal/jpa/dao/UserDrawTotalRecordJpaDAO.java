package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.UserDrawTotalRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 用户福袋抽奖记录
 *
 * @author: dongming
 */
public interface UserDrawTotalRecordJpaDAO extends JpaRepository<UserDrawTotalRecordDO, Long> {

    /**
     * 根据用户id 获取抽奖记录
     *
     * @param uid 用户id
     * @return UserDrawTotalRecordDO
     */
    UserDrawTotalRecordDO findByUid(Long uid);

}
