package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveLetterLY;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.loveLetterLY.LoveLetterLYIndexVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.loveLetterLY.LoveLetterLYConstant.*;


/**
 * 写给你的情书活动
 */
@Slf4j
@Service
public class LoveLetterLYIndexManager implements IndexManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private LoveLetterLYConstant constant;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private LoveLetterLYRankManager loveLetterLYRankManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private LoveLetterLYBizManager loveLetterLYBizManager;


    @Override
    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public LoveLetterLYIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        if (!param.check()) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        LoveLetterLYIndexVO lyIndexVO = new LoveLetterLYIndexVO();
        Long uid = param.getUid();
        toUid = accompanyToUid(uid, toUid);
        lyIndexVO.setLetterCount(constant.acquireLetterCount(uid, toUid));
        lyIndexVO.setUser(userIcon(uid, param.getAppId()));
        lyIndexVO.setPartner(userIcon(toUid, param.getAppId()));
        lyIndexVO.setProductList(shopList());
        lyIndexVO.setWarmGiftStatus(constant.warmGiftStatus(uid));
        lyIndexVO.setChocolateList(chocolates(uid));
        lyIndexVO.setMaterialsList(materialsList(uid));
        lyIndexVO.setRank(loveLetterLYRankManager.obtainedCpList(param, toUid, 10L));

        return lyIndexVO;
    }

    /**
     * toUid校验
     */
    public Long accompanyToUid(Long uid, Long toUid) {
       return loveLetterLYBizManager.isFriend(uid, toUid) ? toUid : loveLetterLYRankManager.topOneToUid(uid);
    }

    /**
     * 获取用户
     */
    public LoveLetterLYIndexVO.UserItem userIcon(Long uid, Long appId) {
        if (Objects.isNull(uid)) {
            return null;
        }
        UserVO userVO = feignUserService.getBasic(uid, appId).successData();
        if (Objects.nonNull(userVO)) {
            return LoveLetterLYIndexVO.UserItem.builder()
                    .userId(userVO.getId().toString())
                    .avatar(userVO.getAvatar())
                    .userName(userVO.getName())
                    .build();
        }
        return null;
    }

    /**
     * 获取用户巧克力状态
     * @return
     */
    public List<LoveLetterLYIndexVO.Chocolate> chocolates(Long uid) {
        return Arrays.stream(ChocolateEnum.values())
                .map(chocolateEnum -> {
                    String key = String.format(CHOCOLATE_KEY, uid, chocolateEnum.getChocolateKey());
                    boolean hasKey = redisManager.hasKey(key);
                    return LoveLetterLYIndexVO.Chocolate.builder()
                            .chocolateKey(chocolateEnum.getChocolateKey())
                            .chocolateStatus(hasKey ? 1 : 0)
                            .associatedMaterialList(hasKey ? chocolateEnum.getAssociatedMaterials() : null)
                            .build();
                })
                .collect(Collectors.toList());
    }

    /**
     * 材料
     * @return
     */
    public List<LoveLetterLYIndexVO.Material> materialsList(Long uid) {
        return Arrays.stream(MaterialsEnum.values()).map(
                materialsEnum -> {
                    Long materialsNum = constant.acquireMaterialsCount(uid, materialsEnum.getMaterialsKey());
                    return LoveLetterLYIndexVO.Material.builder()
                            .materialsKey(materialsEnum.getMaterialsKey())
                            .materialsName(materialsEnum.getMaterialsName())
                            .materialsNum(materialsNum)
                            .build();
        }).collect(Collectors.toList());
    }


    /**
     * 情书商店
     * @return
     */
    public List<LoveLetterLYIndexVO.Product> shopList() {
        String shop = Optional.ofNullable(redisManager.getString(SHOP_KEY)).orElse("false");
        if (!"false".equals(shop)) {
            return JSON.parseObject(shop, new TypeReference<List<LoveLetterLYIndexVO.Product>>() {});
        }
        List<LoveLetterLYIndexVO.Product> collect = null;
        try {
            collect = Arrays.stream(ProductEnum.values()).map(productEnum -> {
                        ScenePrizeDO scenePrizeDO = scenePrizeJpaDAO.getListBySceneCode(1L, ACTIVITY_CODE, productEnum.getProductKey()).get(0);
                        return LoveLetterLYIndexVO.Product.builder()
                                .key(productEnum.getProductKey())
                                .name(scenePrizeDO.getPrizeDesc())
                                .price(scenePrizeDO.getPrizeValueGold())
                                .quantityRequired(productEnum.getQuantityRequired())
                                .icon(scenePrizeDO.getPrizeIcon())
                                .build();
                    }
            ).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("scenePrizeDO error:{}",e.toString());
            return null;
        }
        redisManager.set(SHOP_KEY, JSON.toJSONString(collect), DateUtil.ONE_MONTH_SECOND);
        return collect;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }
}
