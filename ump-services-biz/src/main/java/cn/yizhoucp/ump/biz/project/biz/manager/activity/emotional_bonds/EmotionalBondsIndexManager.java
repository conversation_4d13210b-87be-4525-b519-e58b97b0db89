package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.EmotionalBondsIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.EmotionalBondsIndexVO.PopInfo;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.EmotionalBondsIndexVO.PopInfo.RoleInfo;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.EmotionalBondsIndexVO.PopInfo.UserInfo;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.GiftPackage;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ActivityJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ActivityDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondRelationshipDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 天生羁绊首页
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:18 2025/4/22
 */
@Slf4j
@Service
public class EmotionalBondsIndexManager implements IndexManager {

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private ActivityJpaDAO activityJpaDAO;

    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;

    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;

    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;

    @Resource
    private FeignUserService feignUserService;

    @Override
    public EmotionalBondsIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        // 构建基本的返回对象
        EmotionalBondsIndexVO indexVO = EmotionalBondsIndexVO.builder()
                .giftPackages(buildGiftPackages())
                .comboDiscount(buildComboDiscount())
                .build();

        // 根据extData中的邀请ID构建PopInfo
        if (extData == null || extData.isEmpty()) {
            // 没有邀请ID，直接设置默认无弹窗
            indexVO.setPopInfo(buildDefaultPopInfo(false));
            return indexVO;
        }

        Map<Object, Object> invitationData = emotionalBondsRedisManager.getInvitation(extData);
        if (invitationData == null || invitationData.isEmpty()) {
            log.warn("邀请记录不存在或已过期 invitationId: {}", extData);
            indexVO.setPopInfo(buildDefaultPopInfo(true));
            return indexVO;
        }

        Long fromUid = Long.valueOf(invitationData.get("fromUid").toString());
        if (param.getUid().equals(fromUid)) {
            // 当前用户是邀请发起者，不显示弹窗
            indexVO.setPopInfo(buildDefaultPopInfo(false));
        } else {
            // 构建邀请弹窗信息
            indexVO.setPopInfo(buildPopInfoFromInvitation(param, extData));
        }
        return indexVO;
    }

    private PopInfo buildDefaultPopInfo(Boolean isExpired) {
        String expiredMsg = null;
        if (isExpired) {
            expiredMsg = "邀请已过期";
        }
        return PopInfo.builder()
                .showPopup(false)
                .expiredMessage(expiredMsg)
                .build();
    }

    /**
     * 根据邀请ID构建PopInfo对象
     *
     * @param param        基础参数
     * @param invitationId 邀请ID
     * @return PopInfo对象
     */
    private PopInfo buildPopInfoFromInvitation(BaseParam param, String invitationId) {
        // 从Redis获取邀请信息
        Map<Object, Object> invitationData = emotionalBondsRedisManager.getInvitation(invitationId);
        if (invitationData == null || invitationData.isEmpty()) {
            log.warn("邀请记录不存在或已过期 invitationId: {}", invitationId);
            return buildDefaultPopInfo(true);
        }

        // 解析邀请信息
        Long fromUid = Long.valueOf(invitationData.get("fromUid").toString());
        Long toUid = Long.valueOf(invitationData.get("toUid").toString());
        String role = invitationData.get("role").toString();
        String otherRole = invitationData.get("otherRole").toString();
        String effectKey = invitationData.get("effectKey").toString();

        // 检查原始关系表ID是否存在
        Long originalRelationshipId = null;
        if (invitationData.containsKey("originalRelationshipId")) {
            originalRelationshipId = Long.valueOf(invitationData.get("originalRelationshipId").toString());
        }

        // 获取角色对信息
        EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effectKey);
        if (rolePairEnum == null) {
            log.warn("角色对信息不存在 effectKey: {}", effectKey);
            return PopInfo.builder()
                    .showPopup(false)
                    .build();
        }

        // 获取发起方用户信息
        UserBaseVO fromUserBaseVO = feignUserService.getUserBaseVO(param.getAppId(), fromUid).successData();

        // 构建UserInfo
        UserInfo userInfo = UserInfo.builder()
                .uid(fromUid.toString())
                .userName(fromUserBaseVO.getName())
                .userAvatar(fromUserBaseVO.getAvatar())
                .build();

        // 构建RoleInfo列表
        List<RoleInfo> roleInfoList = new ArrayList<>();

        // 添加发起方角色信息
        roleInfoList.add(RoleInfo.builder()
                .name(role)
                .icon(String.format(EmotionalBondsConstant.BASE_ICON_URL, EmotionalBondsEnums.RoleIconEnum.getIconByRole(role)))
                .build());

        // 添加接收方角色信息
        roleInfoList.add(RoleInfo.builder()
                .name(otherRole)
                .icon(String.format(EmotionalBondsConstant.BASE_ICON_URL, EmotionalBondsEnums.RoleIconEnum.getIconByRole(otherRole)))
                .build());

        // 构建消息文本
        String message = "我想和你缔结[%s]关系，你是[%s]，我是[%s]，快来加入吧～";
        String expiredMessage = "";
        boolean showPopup = true;

        // 检查邀请是否已失效
        boolean invitationExpired = false;

        // 如果有原始关系表ID，先检查该关系是否已经缔结
        if (originalRelationshipId != null) {
            EmotionalBondRelationshipDO relationshipDO = emotionalBondRelationshipService.getById(originalRelationshipId);
            if (relationshipDO != null && relationshipDO.getStatus() == 1) {
                // 原始关系已经缔结，说明该邀请已失效
                invitationExpired = true;
                expiredMessage = "该邀请已失效，可能是因为对方已经与其他用户缔结了关系";
                return buildDefaultPopInfo(Boolean.TRUE);
            }
        }
        message = String.format(message, rolePairEnum.getEffectName(), otherRole, role);

        // 构建PopInfo
        return PopInfo.builder()
                .userInfo(userInfo)
                .showPopup(showPopup)
                .message(message)
                .roleInfo(roleInfoList)
                .isExpired(invitationExpired)
                .expiredMessage(expiredMessage)
                .build();
    }

    private EmotionalBondsIndexVO.ComboDiscount buildComboDiscount() {
        return EmotionalBondsIndexVO.ComboDiscount.builder()
                .description("立省")
                .savingAmount(2997)
                .build();
    }

    private List<GiftPackage> buildGiftPackages() {
        // 新增：计算活动前三天逻辑
        boolean isEarlyStage = isEarlyStage();

        List<GiftPackage> packages = new ArrayList<>();
        List<ScenePrizeDO> scenePrizeDOS = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), EmotionalBondsConstant.ACTIVITY_CODE, EmotionalBondsConstant.GIFT_PACKAGE_SCENE);
        if (scenePrizeDOS == null || scenePrizeDOS.isEmpty()) {
            return packages;
        }
        return scenePrizeDOS.stream().map(scenePrizeDO -> {
            String extData = scenePrizeDO.getExtData();
            Integer originalPrice = scenePrizeDO.getPrizeValueGold().intValue();
            Integer discountPrice = isEarlyStage ? getDiscountPrice(extData) : null;
            String femaleRole = getFemaleRole(extData);
            String maleRole = getMaleRole(extData);
            return GiftPackage.builder()
                    .key(scenePrizeDO.getPrizeValue())
                    .name(scenePrizeDO.getPrizeDesc())
                    .originalPrice(originalPrice)
                    .discountedPrice(discountPrice)
                    .discountTag(isEarlyStage ? EmotionalBondsConstant.GIFT_DISCOUNT_TAG : null)
                    .maleRole(maleRole)
                    .femaleRole(femaleRole)
                    .build()
                    ;
        }).collect(Collectors.toList());
    }

    private String getMaleRole(String extData) {
        if (extData == null) {
            return null;
        }
        JSONObject extDataMap = JSONObject.parseObject(extData);
        if (extDataMap != null) {
            return extDataMap.getString("maleRole");
        }
        return null;
    }

    private String getFemaleRole(String extData) {
        if (extData == null) {
            return null;
        }
        JSONObject extDataMap = JSONObject.parseObject(extData);
        if (extDataMap != null) {
            return extDataMap.getString("femaleRole");
        }
        return null;
    }

    private Integer getDiscountPrice(String extData) {
        if (extData == null) {
            return null;
        }
        JSONObject extDataMap = JSONObject.parseObject(extData);
        if (extDataMap != null) {
            return extDataMap.getInteger("discountPrice");
        }
        return null;
    }

    /**
     * 判断是否处于前三天
     *
     * @return true: 是，false: 否
     */
    public boolean isEarlyStage() {
        List<ActivityDO> activityDO = activityJpaDAO.getByAppIdAndUnionIdAndActivityCode(ServicesAppIdEnum.lanling.getAppId(), ServicesAppIdEnum.lanling.getUnionId(), EmotionalBondsConstant.ACTIVITY_CODE);
        if (activityDO != null && !activityDO.isEmpty()) {
            ActivityDO activity = activityDO.get(0);
            LocalDateTime now = LocalDateTime.now();
            long between = ChronoUnit.DAYS.between(activity.getStartTime(), now);
            return between < 3;
        }
        return false;
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return EmotionalBondsConstant.ACTIVITY_CODE;
    }
}
