package cn.yizhoucp.ump.biz.project.biz.manager.activity.princessRoseAndKnight;

import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.productServices.HeadFrameProductVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.princessRoseKnight.BuyMagicPotionResult;
import cn.yizhoucp.ump.api.vo.activity.princessRoseKnight.DrawResult;
import cn.yizhoucp.ump.api.vo.activity.princessRoseKnight.IndexVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeReturn;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskItem;
import cn.yizhoucp.ump.api.vo.jimu.task.TaskVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.labourDay2024.LabourDay2024IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.BOX_SENT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.BROADCAST;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.COURAGE_VALUE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.CP_TASK_TAKE_PRIZE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.DRAW_COIN;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.INDEX;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.PROP;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.RANK;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.TASK_TAKE_PRIZE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.PrincessRoseAndKnightConstant.TICKET;

@Service
@Slf4j
public class PrincessRoseAndKnightIndexManager implements IndexManager {

    private static final Random RANDOM = new Random();
    @Resource
    private PrincessRoseAndKnightBizManager princessRoseAndKnightBizManager;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private LabourDay2024IndexManager labourDay2024IndexManager;
    @Resource
    private DepthFeignService depthFeignService;
    @Resource
    private PrincessRoseAndKnightRankManager princessRoseAndKnightRankManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private PrincessRoseAndKnightTrackManager princessRoseAndKnightTrackManager;

    @Override
    @ActivityCheck(activityCode = ACTIVITY_CODE)
    public IndexVO getIndex(BaseParam param, Long toUid, String extData) {
        IndexVO indexVO = new IndexVO();
        indexVO.setStage(princessRoseAndKnightBizManager.stage());
        indexVO.setUser(feignUserService.getBasic(param.getUid(), param.getAppId()).successData());

        if (toUid == null) {
            toUid = labourDay2024IndexManager.getMaxIntimacyOppositeSexCloseFriend(param);
        } else {
            Boolean cardiac = depthFeignService.hasUserCardiacRelationship(param.getUid(), toUid).successData();
            if (!Boolean.TRUE.equals(cardiac)) {
                toUid = null;
            }
        }
        indexVO.setToUser(feignUserService.getBasic(toUid, param.getAppId()).successData());

        // TODO 上线前修改
        indexVO.setWitchAdventureShow(witchAdventureCheck(indexVO.getUser()));

        indexVO.setBoxSent(Boolean.TRUE.equals(redisManager.hasKey(String.format(BOX_SENT, AppUtil.splicUserId(param.getUid(), toUid)))));

        indexVO.setCourageValue(Math.min(19999, Optional.ofNullable(redisManager.getInteger(String.format(COURAGE_VALUE, AppUtil.splicUserId(param.getUid(), toUid), indexVO.getStage()))).orElse(0)));

        indexVO.setButtonStatus(new ArrayList<>());
        for (PrincessRoseAndKnightConstant.CpTask cpTask : PrincessRoseAndKnightConstant.CpTask.values()) {
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(CP_TASK_TAKE_PRIZE, param.getUid(), indexVO.getStage(), cpTask.name(), toUid)))) {
                indexVO.getButtonStatus().add(-1);
            } else if (indexVO.getCourageValue() < cpTask.getCourageValue().get(indexVO.getStage() - 1)) {
                indexVO.getButtonStatus().add(0);
            } else {
                indexVO.getButtonStatus().add(1);
            }
        }

        indexVO.setBroadcast(Optional.ofNullable(redisManager.lGet(BROADCAST, 0, 20)).orElse(Collections.emptyList()));

        indexVO.setProps(new ArrayList<>());
        for (PrincessRoseAndKnightConstant.Prop prop : PrincessRoseAndKnightConstant.Prop.values()) {
            indexVO.getProps().add(Optional.ofNullable(redisManager.getInteger(String.format(PROP, param.getUid(), prop.name()))).orElse(0));
        }

        indexVO.setIndex(Optional.ofNullable(redisManager.getInteger(String.format(INDEX, param.getUid()))).orElse(0));

        indexVO.setTimesLeft(Optional.ofNullable(redisManager.getInteger(String.format(TICKET, param.getUid()))).orElse(0));

        indexVO.setTask(TaskVO.builder().taskItemList(new ArrayList<>()).build());
        for (PrincessRoseAndKnightConstant.Task task : PrincessRoseAndKnightConstant.Task.values()) {
            TaskItem taskItem = new TaskItem();
            taskItem.setTaskCode(task.name());
            taskItem.setCurFinishTimes(Math.min(task.getFinishTimes(), Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), DateUtil.getNowYyyyMMdd(), task.name()))).orElse(0)));
            int buttonStatus;
            if (Boolean.TRUE.equals(redisManager.hasKey(String.format(TASK_TAKE_PRIZE, param.getUid(), DateUtil.getNowYyyyMMdd(), taskItem.getTaskCode())))) {
                buttonStatus = -1;
            } else if (taskItem.getCurFinishTimes() < task.getFinishTimes()) {
                buttonStatus = 0;
            } else {
                buttonStatus = 1;
            }
            taskItem.setButtonStatus(buttonStatus);
            indexVO.getTask().getTaskItemList().add(taskItem);
        }

        indexVO.setRank(princessRoseAndKnightRankManager.getRank(
                RankContext.builder()
                        .activityCode(ACTIVITY_CODE)
                        .rankKey(String.format(RANK, princessRoseAndKnightBizManager.stage()))
                        .type(RankContext.RankType.user)
                        .param(param)
                        .supportDiff(Boolean.TRUE)
                        .build()));

        return indexVO;
    }

    @NoRepeatSubmit(time = 3)
    public BuyMagicPotionResult buyMagicPotion(BaseParam param, Long toUid) {
//        if (!witchAdventureCheck(param)) {
//            return null;
//        }

        int courageValue = Optional.ofNullable(redisManager.getInteger(String.format(COURAGE_VALUE, AppUtil.splicUserId(param.getUid(), toUid), princessRoseAndKnightBizManager.stage()))).orElse(0);
        if (courageValue <= 520) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前勇气值还未达到购买标准噢~");
        }

        int random = RANDOM.nextInt(2);

        int meiguixiangshui = Optional.ofNullable(redisManager.getInteger(String.format(PROP, param.getUid(), PrincessRoseAndKnightConstant.Prop.meiguixiangshui.name()))).orElse(0);
        if (meiguixiangshui < 2) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        redisManager.decrLong(String.format(PROP, param.getUid(), PrincessRoseAndKnightConstant.Prop.meiguixiangshui.name()), 2L, DateUtil.ONE_MONTH_SECOND);

        String toast = null;
        if (random == 0) {
            courageValue  = courageValue - courageValue / 10;
            toast = "很遗憾，未获得女巫帮助";
        } else {
            courageValue = courageValue + (int) (courageValue * 0.15);
            toast = "恭喜骑士，获得女巫帮助";
        }
        redisManager.set(String.format(COURAGE_VALUE, AppUtil.splicUserId(param.getUid(), toUid), princessRoseAndKnightBizManager.stage()), courageValue, DateUtil.ONE_MONTH_SECOND);

        return BuyMagicPotionResult.builder().courageValue(courageValue).toast(toast).build();
    }

    public TakePrizeReturn takePrize(BaseParam param, Long toUid, String taskCode) {
        if (StringUtils.isBlank(taskCode)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        int stage = princessRoseAndKnightBizManager.stage();
        for (PrincessRoseAndKnightConstant.CpTask cpTask : PrincessRoseAndKnightConstant.CpTask.values()) {
            if (cpTask.name().equals(taskCode)) {
                int courageValue = Optional.ofNullable(redisManager.getInteger(String.format(COURAGE_VALUE, AppUtil.splicUserId(param.getUid(), toUid), stage))).orElse(0);
                if (cpTask.getCourageValue().get(stage - 1) > courageValue) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您尚未完成该任务，继续完成吧～");
                }

                if (!Boolean.TRUE.equals(redisManager.setnx(String.format(CP_TASK_TAKE_PRIZE, param.getUid(), stage, cpTask.name(), toUid), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您已完成该任务，看看其他任务吧～");
                }

                UserVO userVO = feignUserService.getBasic(param.getUid(), param.getAppId()).successData();
                if (userVO != null && userVO.getSex() != null) {
                    if (SexType.WOMAN.equals(userVO.getSex())) {
                        List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, "cp_task-" + stage + "-" + cpTask.name());
                        sendPrizeManager.sendPrize(
                                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                                scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                        );
                        checkAndRefresh(param, toUid, stage);
                        for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
                            princessRoseAndKnightTrackManager.allActivityReceiveAward(cpTask.getTaskTitle(), scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), null, null, param.getUid());
                        }
                        return TakePrizeReturn.builder().prizeItemList(this.scenePrizeDO2PrizeItem(scenePrizeDOList)).build();
                    } else if (SexType.MAN.equals(userVO.getSex())) {
                        List<String> prizeList = cpTask.getPrize().get(stage - 1);
                        Object o = redisManager.get(String.format(TASK_CUR_FINISH_TIMES, AppUtil.splicUserId(param.getUid(), toUid), stage, cpTask.name()));
                        int random = 1;
                        if (o == null) {
                            random = RANDOM.nextInt(2);
                        }
                        long taskCurFinishTimes = Optional.ofNullable(redisManager.incrLong(String.format(TASK_CUR_FINISH_TIMES, AppUtil.splicUserId(param.getUid(), toUid), stage, cpTask.name()), random, DateUtil.ONE_MONTH_SECOND)).orElse(0L);
                        String prize = prizeList.get((int) taskCurFinishTimes % prizeList.size());
                        redisManager.incrLong(String.format(PROP, param.getUid(), prize), 1L, DateUtil.ONE_MONTH_SECOND);
                        PrincessRoseAndKnightConstant.Prop prop = PrincessRoseAndKnightConstant.Prop.getByName(prize);
                        checkAndRefresh(param, toUid, stage);
                        princessRoseAndKnightTrackManager.allActivityReceiveAward(cpTask.getTaskTitle(), prize, null, 1, null, null, param.getUid());
                        return TakePrizeReturn.builder().prizeItemList(Collections.singletonList(cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem.builder().prizeName(prop.getDesc()).prizeIcon(prop.getIcon()).prizeNum(1).build())).build();
                    }
                }
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @NoRepeatSubmit(time = 3)
    public DrawReturn draw(BaseParam param) {
        int timesLeft = Optional.ofNullable(redisManager.getInteger(String.format(TICKET, param.getUid()))).orElse(0);
        if (timesLeft <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前骰子次数不够噢~");
        }
        redisManager.decrLong(String.format(TICKET, param.getUid()), 1L, DateUtil.ONE_MONTH_SECOND);

        Set<Integer> stepSet = new HashSet<>();
        int step = 0;
        int index = Optional.ofNullable(redisManager.getInteger(String.format(INDEX, param.getUid()))).orElse(0);
        PrincessRoseAndKnightConstant.Map map = null;
        Integer drawCoin = redisManager.getInteger(String.format(DRAW_COIN, param.getUid()));
        if (drawCoin == null) {
            redisManager.set(String.format(DRAW_COIN, param.getUid()), 20, DateUtil.ONE_MONTH_SECOND);
            drawCoin = 20;
        }
        int minStep = 0;
        long minItemValueGold = Long.MAX_VALUE;
        while (stepSet.size() < 6) {
            step = RANDOM.nextInt(6) + 1;
            if (stepSet.contains(step)) {
                continue;
            }

            int newIndex = (index + step) % PrincessRoseAndKnightConstant.Map.values().length;
            map = PrincessRoseAndKnightConstant.Map.values()[newIndex];
            if (map.getItemValueGold() < minItemValueGold) {
                minItemValueGold = map.getItemValueGold();
                minStep = step;
            }
            if (map.getItemValueGold() > drawCoin) {
                stepSet.add(step);
            } else {
                break;
            }
        }
        if (stepSet.size() == 6) {
            // 最小
            step = minStep;
        }
        index = (index + step) % PrincessRoseAndKnightConstant.Map.values().length;
        map = PrincessRoseAndKnightConstant.Map.values()[index];

        List<PrizeItem> prizeItemList = new ArrayList<>();
        if ("gift".equals(map.getPrizeType())) {
            CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(param.getAppId(), map.getItemKey()).successData();
            if (coinGiftProductVO != null) {
                prizeItemList = Collections.singletonList(PrizeItem.builder().prizeName(coinGiftProductVO.getName()).prizeKey(coinGiftProductVO.getGiftKey()).prizeType("gift").prizeIcon(coinGiftProductVO.getIcon()).valueGold(Math.toIntExact(coinGiftProductVO.getNeedCoin())).prizeNum(1).build());
            }
        } else if ("head_frame".equals(map.getPrizeType())) {
            HeadFrameProductVO headFrameProductVO = feignProductService.getHfProductByKey(param.getAppId(), map.getItemKey()).successData();
            if (headFrameProductVO != null) {
                prizeItemList = Collections.singletonList(PrizeItem.builder().prizeName(headFrameProductVO.getName()).prizeKey(headFrameProductVO.getUniqueKey()).prizeType("dress_up").prizeSubType("head_frame").prizeIcon(headFrameProductVO.getIcon()).valueGold(0).effectiveDays(14).build());
            }
        }

        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                prizeItemList.stream().map(p -> SendPrizeDTO.of(p, ACTIVITY_CODE)).collect(Collectors.toList())
        );

        redisManager.decrLong(String.format(DRAW_COIN, param.getUid()), map.getItemValueGold(), DateUtil.ONE_MONTH_SECOND);
        redisManager.set(String.format(INDEX, param.getUid()), index, DateUtil.ONE_MONTH_SECOND);

        for (PrizeItem prizeItem : prizeItemList) {
            princessRoseAndKnightTrackManager.allActivityReceiveAward("rose_coronet", prizeItem.getPrizeKey(), Long.valueOf(prizeItem.getValueGold()), prizeItem.getPrizeNum(), step, null, param.getUid());
        }

        return DrawReturn.builder()
                .hasRealPrize(Boolean.FALSE)
                .prizeItemList(prizeItemList)
                .extData(DrawResult.builder().index(index).step(step).build()).build();
    }

    @Override
    public String getActivityCode() {
        return ACTIVITY_CODE;
    }

    @Override
    public String getTemplateType() {
        return null;
    }

    private List<cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem> scenePrizeDO2PrizeItem(List<ScenePrizeDO> params) {
        List<cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(params)) {
            for (ScenePrizeDO item : params) {
                list.add(cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem.builder()
                        .valueGold(Math.toIntExact(item.getPrizeValueGold()))
                        .prizeIcon(item.getPrizeIcon())
                        .prizeName(item.getPrizeDesc()).build());
            }
        }
        return list;
    }

    private boolean witchAdventureCheck(UserVO userVO) {
        LocalDateTime now = LocalDateTime.now();
        if (now.getDayOfMonth() == 20 && now.getMonthValue() == 5) {
            return userVO != null && SexType.MAN.equals(userVO.getSex());
        }
        return false;
    }

    private void checkAndRefresh(BaseParam param, Long toUid, Integer stage) {
        for (PrincessRoseAndKnightConstant.CpTask cpTask : PrincessRoseAndKnightConstant.CpTask.values()) {
            if (!Boolean.TRUE.equals(redisManager.hasKey(String.format(CP_TASK_TAKE_PRIZE, param.getUid(), stage, cpTask.name(), toUid)))) {
                return;
            }
            if (!Boolean.TRUE.equals(redisManager.hasKey(String.format(CP_TASK_TAKE_PRIZE, toUid, stage, cpTask.name(), param.getUid())))) {
                return;
            }
        }

        for (PrincessRoseAndKnightConstant.CpTask cpTask : PrincessRoseAndKnightConstant.CpTask.values()) {
            redisManager.delete(String.format(CP_TASK_TAKE_PRIZE, param.getUid(), stage, cpTask.name(), toUid));
            redisManager.delete(String.format(CP_TASK_TAKE_PRIZE, toUid, stage, cpTask.name(), param.getUid()));
        }
        redisManager.delete(String.format(COURAGE_VALUE, AppUtil.splicUserId(param.getUid(), toUid), stage));
    }

}
