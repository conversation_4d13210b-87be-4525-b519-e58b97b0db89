package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre.stragey;

import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.pre.common.PreChineseNewYear2025RedisManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PreSwitchSceneStrategy implements PreChineseNewYear2025ButtonStrategy {
    @Resource
    private PreChineseNewYear2025RedisManager preChineseNewYear2025RedisManager;

    @Override
    public Object event(ButtonEventParam param) {
        String bizType = param.getBizKey();
        preChineseNewYear2025RedisManager.setCurrentScene(param.getBaseParam().getUid(), bizType);
        return Boolean.TRUE;
    }
}
