package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonUserVO;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.EmotionalBondsFriendVO;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmotionalBondGetFriends implements ExecutableStrategy {
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private FeignLanlingService feignLanlingService;
    @Resource
    private DepthFeignService depthFeignService;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        List<CommonUserVO> commonUserVOList = friendList();
        if(commonUserVOList == null){
            return null;
        }
        BaseParam baseParam = buttonEventParam.getBaseParam();
        return commonUserVOList.stream().map(commonUserVO -> {
            return EmotionalBondsFriendVO.builder()
                    .uid(commonUserVO.getId())
                    .userName(commonUserVO.getName())
                    .userAvatar(commonUserVO.getAvatar())
                    .build();
        }).collect(Collectors.toList());
    }
    public List<CommonUserVO> friendList() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        UserVO userVO = feignUserService.getBasic(uid, ServicesAppIdEnum.lanling.getAppId()).successData();
        if (Objects.isNull(userVO) || Objects.isNull(userVO.getSex())) {
            return null;
        }
        String userSex = userVO.getSex().getCode();
        Long start = 0L;
        Integer num = 100;
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
/*
        PageVO<WrapUserVO> subFriendList = feignLanlingService.getSubscribeList(start, num).getData();
        PageVO<WrapUserVO> fansList = feignLanlingService.getFansList(start, num).getData();
        List<WrapUserVO> closeFriends = getCloseFriends(uid);
*/

/*
        if (Objects.isNull(friendList) && Objects.isNull(subFriendList) && Objects.isNull(fansList)) {
            return Collections.emptyList();
        }
*/
        if(Objects.isNull(friendList)){
            return Collections.emptyList();
        }

        List<WrapUserVO> list = new ArrayList<>(friendList.getList());
        /*
        if (subFriendList != null) {
            list.addAll(subFriendList.getList());
        }
        if (fansList != null) {
            list.addAll(fansList.getList());
        }
        if (closeFriends != null) {
            list.addAll(closeFriends);
        }
*/

        // 去重操作
        List<WrapUserVO> uniqueList = list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                wrapUserVO -> wrapUserVO.getUser().getId(),
                                wrapUserVO -> wrapUserVO,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        log.info("获取好友列表：{}", uniqueList);

        return uniqueList.stream().map(WrapUserVO::getUser)
                .limit(500)
                .filter(Objects::nonNull)
                .filter(toUserVO -> !toUserVO.getSex().equals(userSex))
                .collect(Collectors.toList());
    }

    public List<WrapUserVO> getCloseFriends(Long uid) {
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(MDCUtil.getCurUserIdByMdc()).successData();
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return null;
        }
        List<WrapUserVO> list = new ArrayList<>();
        allUserDepth.forEach((k, v) -> {
            UserVO userVO = feignUserService.getBasic(k, MDCUtil.getCurAppIdByMdc()).successData();
            if (userVO != null && userVO.getSex() != null) {
                CommonUserVO commonUserVO = new CommonUserVO();
                commonUserVO.setId(userVO.getId());
                commonUserVO.setName(userVO.getName());
                commonUserVO.setAvatar(userVO.getAvatar());
                commonUserVO.setSex(userVO.getSex().getCode());
                WrapUserVO wrapUserVO = new WrapUserVO();
                wrapUserVO.setUser(commonUserVO);
                list.add(wrapUserVO);
            }
        });

        return list;
    }


}
