package cn.yizhoucp.ump.biz.project.biz.manager.activity.christmasBell;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.base.enums.GiftWay;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.enums.sns.UserRelationType;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.CommonListVO;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.productServices.HeadFrameProductVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem;
import cn.yizhoucp.ms.core.vo.umpServices.activity.goddess.PrizeInfo;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.activity.christmasBell.BellListVO;
import cn.yizhoucp.ump.api.vo.activity.christmasBell.BellSceneVO;
import cn.yizhoucp.ump.api.vo.activity.christmasBell.ChristmasPrizeVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.api.vo.jimu.rank.RewardVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.ExpiredTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.christmasBell.ChristmasBellEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.christmasBell.ChristmasBellRedisEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.christmasBell.ChristmasBellSceneEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignImService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.FeignSnsService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.christmasBell.ChristmasBellBackpackJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.christmasBell.ChristmasBellSceneJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.christmasBell.ChristmasBellBackpackDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.christmasBell.ChristmasBellSceneDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 圣诞铃铛
 * @create 2024-12-13
 **/
@Service
@Slf4j
public class ChristmasBellSupport {

    @Resource
    RedisManager redisManager;

    @Resource
    ChristmasBellBackpackJpaDAO bellBackpackJpaDAO;

    @Resource
    SendPrizeManager sendPrizeManager;

    @Resource
    FeignProductService feignProductService;

    @Resource
    FeignSnsService feignSnsService;

    @Resource
    FeignUserService feignUserService;

    @Resource
    ChristmasBellSceneJpaDAO christmasBellSceneJpaDAO;

    @Resource
    FeignImService feignImService;

    @Resource
    YzKafkaProducerManager yzKafkaProducerManager;

    // 抽奖消耗积分
    private static final int LOTTERY_INTEGRAL = 100;
    // 隐藏用户头像
    private static final String HIDE_USER_AVATAR = "https://res-cdn.nuan.chat/nuanliao-fronted/activity/guanxuan-jun/default-avatar.png";
    // 隐藏用户昵称
    private static final String HIDE_USER_NAME = "隐藏用户";
    // redis 过期时间
    private static final long REDIS_KEY_EXPIRED_TIME = DateUtil.ONE_DAY_SECOND * 20;
    // 铃铛抽奖概率
    private static final List<ChristmasPrizeVO> BELL_LOTTERY_LIST = new ArrayList<>();
    // 铃铛对应的礼物
    private static final Map<String, List<String>> BELL_GIFT_MAP = new HashMap<>();
    // 面板礼物
    private static final List<String> PANEL_GIFT_LIST = new ArrayList<>();
    // 下发奖励小助手消息
    private static final String REWARD_MSG = "恭喜您的房间在“铃儿响叮当”活动中，荣获活动榜单第%s！礼物奖励已下发至您的背包，请注意查收哦~";
    // 榜单礼物信息
    private static final List<RewardVO> RANK_GIFT_LIST = new ArrayList<>();

    static {
        BELL_LOTTERY_LIST.add(new ChristmasPrizeVO(ChristmasBellEnum.nuandong.getKey(), 95.82));
        BELL_LOTTERY_LIST.add(new ChristmasPrizeVO(ChristmasBellEnum.mengqu.getKey(), 3.83));
        BELL_LOTTERY_LIST.add(new ChristmasPrizeVO(ChristmasBellEnum.tianxin.getKey(), 0.29));
        BELL_LOTTERY_LIST.add(new ChristmasPrizeVO(ChristmasBellEnum.fuyou.getKey(), 0.06));
        //BELL_LOTTERY_LIST.add(new ChristmasPrizeVO(ChristmasBellEnum.shuijin.getKey(), 0.00));

        BELL_GIFT_MAP.put(ChristmasBellEnum.nuandong.getKey(), Arrays.asList("MGJL_GIFT", "AXYS_GIFT"));
        BELL_GIFT_MAP.put(ChristmasBellEnum.mengqu.getKey(), Arrays.asList("YYHZ_GIFT", "ZFJ_GIFT"));
        BELL_GIFT_MAP.put(ChristmasBellEnum.tianxin.getKey(), Arrays.asList("TECB_GIFT", "AMXS_GIFT"));
        BELL_GIFT_MAP.put(ChristmasBellEnum.fuyou.getKey(), Arrays.asList("MGSN_GIFT", "CLQK_GIFT"));
        BELL_GIFT_MAP.put(ChristmasBellEnum.shuijin.getKey(), Arrays.asList("QBZJ_GIFT", "SJZL_GIFT"));

        PANEL_GIFT_LIST.add("PPAA_GIFT");
        PANEL_GIFT_LIST.add("SDHH_GIFT");
        PANEL_GIFT_LIST.add("SDRQQ_GIFT");
        PANEL_GIFT_LIST.add("SDYH_GIFT");

        RewardVO g1 = RewardVO.builder().giftKey("YHLAJ_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-11/1732673919003509.png").value(5200L).build();
        RewardVO g2 = RewardVO.builder().giftKey("AFEZY_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-07/1722315471688884.png").value(3344L).build();
        RewardVO g3 = RewardVO.builder().giftKey("AYS_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-11/1732847254488356.png").value(1314L).build();
        RewardVO g4 = RewardVO.builder().giftKey("AXSJP_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-08/1724668047584750.png").value(888L).build();
        RewardVO g5 = RewardVO.builder().giftKey("HLDG_GIFT").icon("https://res-cdn.nuan.chat/gift-image/2024-05/1715141014773822.png").value(888L).build();
        RANK_GIFT_LIST.add(g1);
        RANK_GIFT_LIST.add(g2);
        RANK_GIFT_LIST.add(g3);
        RANK_GIFT_LIST.add(g4);
        RANK_GIFT_LIST.add(g5);

    }

    /**
     * 获取用户积分
     *
     * @param userId id
     * @return Long
     */
    public Double getUserIntegral(Long userId) {
        if (null == userId) {
            return 0d;
        }
        String key = ChristmasBellRedisEnum.bell_user_integral.getKey();
        Object result = redisManager.hget(key, Convert.toStr(userId));
        return Convert.toDouble(result, 0d);
    }

    /**
     * 更新用户积分
     *
     * @param userId   用户 id
     * @param integral 积分 扣除传负数
     */
    public void updateUserIntegral(Long userId, Double integral) {
        if (null == userId || null == integral) {
            return;
        }
        String key = ChristmasBellRedisEnum.bell_user_integral.getKey();
        redisManager.hincr(key, Convert.toStr(userId), integral, REDIS_KEY_EXPIRED_TIME);
    }

    /**
     * 获取用户礼物领取状态
     *
     * @return boolean
     */
    public Boolean getUserGiftReceiveStatus(Long uid, Long toUid, Long giftId) {
        if (null == uid || null == toUid || null == giftId) {
            return true;
        }
        String userIdStr = AppUtil.splicUserId(uid, toUid);
        String key = String.format(ChristmasBellRedisEnum.bell_user_gift_receive_list.getKey(), userIdStr, uid);
        Boolean member = redisManager.setIsMember(key, giftId);
        return member == null ? true : member;
    }

    /**
     * 更新用户领取状态
     */
    public void updateUserGiftReceiveStatus(Long uid, Long toUid, Long giftId) {
        if (null == uid || null == toUid || null == giftId) {
            return;
        }
        String userIdStr = AppUtil.splicUserId(uid, toUid);
        String key = String.format(ChristmasBellRedisEnum.bell_user_gift_receive_list.getKey(), userIdStr, uid);
        redisManager.sSetExpire(key, REDIS_KEY_EXPIRED_TIME, giftId);
    }

    /**
     * 更新用户和好友的关系
     */
    public void updateUserFriendRelation(Long uid, Long toUid) {
        if (null == uid || null == toUid) {
            return;
        }
        String key = ChristmasBellRedisEnum.bell_user_friend_relation.getKey();
        redisManager.hset(key, Convert.toStr(uid), toUid);
    }

    /**
     * 获取用户的好友
     */
    public Long getUserFriendRelation(Long uid) {
        if (null == uid) {
            return null;
        }
        String key = ChristmasBellRedisEnum.bell_user_friend_relation.getKey();
        Object hget = redisManager.hget(key, Convert.toStr(uid));
        return Convert.toLong(hget, null);
    }

    /**
     * 获取用户的背包铃铛
     *
     * @param userId 用户 id
     * @return List
     */
    public List<BellListVO.BellVO> getBellList(Long userId) {
        List<BellListVO.BellVO> result = new ArrayList<>();
        if (null == userId) {
            return result;
        }
        // 获取用户背包中铃铛
        List<ChristmasBellBackpackDO> bellBackpackList = bellBackpackJpaDAO.findByUid(userId);
        Map<String, Integer> bellMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(bellBackpackList)) {
            bellMap = bellBackpackList.stream().collect(Collectors.toMap(ChristmasBellBackpackDO::getBellKey, ChristmasBellBackpackDO::getBellCount));
        }
        for (ChristmasBellEnum bell : ChristmasBellEnum.values()) {
            BellListVO.BellVO bellVO = BellListVO.BellVO.builder()
                    .bellKey(bell.getKey())
                    .bellName(bell.getName())
                    .bellCount(bellMap.getOrDefault(bell.getKey(), 0))
                    .upgradeCount(bell.getUpgradeCount())
                    .bellIcon(bell.getIcon())
                    .build();
            result.add(bellVO);
        }
        return result;
    }


    /**
     * 铃铛抽奖
     *
     * @param userId  用户 id
     * @param extDate 扩展数据
     */
    public PrizeInfo bellLottery(Long userId, String extDate) {
        if (null == userId) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Integer times = 1;
        if (StringUtils.isNotBlank(extDate)) {
            JSONObject jsonObject = JSON.parseObject(extDate);
            times = Convert.toInt(jsonObject.get("times"), 1);
        }
        PrizeInfo result = new PrizeInfo();
        long integral = times * LOTTERY_INTEGRAL;
        // 扣除用户积分
        double userIntegral = getUserIntegral(userId);
        if (userIntegral < integral) {
            return result;
        }
        updateUserIntegral(userId, -Convert.toDouble(integral, 0d));
        // 抽奖
        List<String> prizeList = drawPrize(BELL_LOTTERY_LIST, times);
        // 下发奖品
        Map<String, Integer> resultMap = prizeList.stream()
                .collect(Collectors.groupingBy(
                        s -> s,
                        Collectors.summingInt(s -> 1)
                ));
        // 获取用户当前是否有铃铛
        List<ChristmasBellBackpackDO> bellBackpackList = bellBackpackJpaDAO.findByUid(userId);
        Map<String, ChristmasBellBackpackDO> backpackMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(bellBackpackList)) {
            backpackMap = bellBackpackList.stream().collect(Collectors.toMap(ChristmasBellBackpackDO::getBellKey, v -> v));
        }
        List<PrizeItem> prizeItemList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : resultMap.entrySet()) {
            String bellKey = entry.getKey();
            Integer count = entry.getValue();
            if (backpackMap.containsKey(bellKey)) {
                ChristmasBellBackpackDO backpackDO = backpackMap.get(bellKey);
                bellBackpackJpaDAO.updateBellCountById(backpackDO.getId(), count);
            } else {
                ChristmasBellBackpackDO backpackDO = new ChristmasBellBackpackDO();
                backpackDO.setUid(userId);
                backpackDO.setBellKey(bellKey);
                backpackDO.setBellCount(count);
                backpackDO.setUpdateTime(DateTime.now());
                bellBackpackJpaDAO.save(backpackDO);
            }
            ChristmasBellEnum christmasBellEnum = ChristmasBellEnum.convertFormCode(bellKey);
            if (null != christmasBellEnum) {
                PrizeItem prizeItem = PrizeItem.builder()
                        .prizeName(christmasBellEnum.getName())
                        .prizeKey(bellKey)
                        .prizeIcon(christmasBellEnum.getIcon())
                        .prizeNum(count).build();
                prizeItemList.add(prizeItem);
            }
        }
        // 返回奖品，按照奖品数量排序
        if (!CollectionUtil.isEmpty(prizeItemList) && prizeItemList.size() > 1) {
            prizeItemList.sort(Comparator.comparingInt(PrizeItem::getPrizeNum).reversed());
        }
        result.setPrizeItemList(prizeItemList);
        return result;
    }

    /**
     * 抽奖铃铛
     *
     * @return String
     */
    private List<String> drawPrize(List<ChristmasPrizeVO> prizeList, Integer times) {
        if (CollectionUtil.isEmpty(prizeList)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        double totalProbability = 100;
        // 将概率转换为累积区间
        double[] cumulativeProbabilities = new double[prizeList.size()];
        double cumulative = 0;
        for (int i = 0; i < prizeList.size(); i++) {
            // 转换成 [0, 1)
            cumulative += prizeList.get(i).getProbability() / totalProbability;
            cumulativeProbabilities[i] = cumulative;
        }
        for (int count = 0; count < times; count++) {
            // 生成随机数 [0, 1) 的随机数
            String giftKey = ChristmasBellEnum.nuandong.getKey();
            double random = ThreadLocalRandom.current().nextDouble();
            // 根据随机数确定奖品
            for (int i = 0; i < cumulativeProbabilities.length; i++) {
                if (random <= cumulativeProbabilities[i]) {
                    giftKey = prizeList.get(i).getKey();
                    break;
                }
            }
            result.add(giftKey);
        }
        return result;
    }


    /**
     * 升级铃铛
     *
     * @param userId  用户 id
     * @param extDate 扩展数据
     * @return CommonResultVO
     */
    public CommonResultVO upgradeBell(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        String bellKey = "";
        if (StringUtils.isNotBlank(extDate)) {
            JSONObject jsonObject = JSON.parseObject(extDate);
            bellKey = Convert.toStr(jsonObject.get("bellKey"));
        }
        ChristmasBellEnum christmasBellEnum = ChristmasBellEnum.convertFormCode(bellKey);
        if (null == christmasBellEnum) {
            return CommonResultVO.fail("铃铛不存在~");
        }
        if (ChristmasBellEnum.shuijin.getKey().equals(bellKey)) {
            return CommonResultVO.fail("水晶铃铛不能升级~");
        }
        // 获取用户当前铃铛数量
        ChristmasBellBackpackDO bellBackpackDO = bellBackpackJpaDAO.findTopByUidAndBellKey(userId, bellKey);
        if (null == bellBackpackDO) {
            return CommonResultVO.fail("铃铛数量不足~");
        }
        Integer upgradeCount = christmasBellEnum.getUpgradeCount();
        if (bellBackpackDO.getBellCount() < upgradeCount) {
            return CommonResultVO.fail("铃铛数量不足~");
        }
        // 扣除当前铃铛
        ChristmasBellEnum nextLevelBell = ChristmasBellEnum.getNextLevel(christmasBellEnum.getLevel());
        bellBackpackJpaDAO.updateBellCountById(bellBackpackDO.getId(), -upgradeCount);
        // 更新升级后铃铛数量
        ChristmasBellBackpackDO nextBackBell = bellBackpackJpaDAO.findTopByUidAndBellKey(userId, nextLevelBell.getKey());
        if (null == nextBackBell) {
            ChristmasBellBackpackDO newBell = new ChristmasBellBackpackDO();
            newBell.setUid(userId);
            newBell.setBellKey(nextLevelBell.getKey());
            newBell.setBellCount(1);
            newBell.setUpdateTime(DateTime.now());
            bellBackpackJpaDAO.save(newBell);
        } else {
            bellBackpackJpaDAO.updateBellCountById(nextBackBell.getId(), 1);
        }
        // 埋点
        allActivityTaskFinish(userId, christmasBellEnum.getPointDesc(), Convert.toLong(upgradeCount, 0L));
        return CommonResultVO.success();
    }


    /**
     * 打开铃铛
     *
     * @param userId  用户 id
     * @param appId   应用 id
     * @param extDate 扩展数据
     * @return CommonListVO
     */
    public CommonListVO openBell(Long userId, Long appId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        if (null == appId) {
            appId = 1L;
        }
        String bellKey = "";
        if (StringUtils.isNotBlank(extDate)) {
            JSONObject jsonObject = JSON.parseObject(extDate);
            bellKey = Convert.toStr(jsonObject.get("bellKey"));
        }
        List<PrizeItem> result = new ArrayList<>();
        ChristmasBellEnum christmasBellEnum = ChristmasBellEnum.convertFormCode(bellKey);
        if (null == christmasBellEnum) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "铃铛不存在~");
        }
        // 扣除铃铛数量
        ChristmasBellBackpackDO bellBackpackDO = bellBackpackJpaDAO.findTopByUidAndBellKey(userId, bellKey);
        if (null == bellBackpackDO || bellBackpackDO.getBellCount() < 1) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "铃铛数量不足~");
        }
        bellBackpackJpaDAO.updateBellCountById(bellBackpackDO.getId(), -1);
        // 随机生成礼物
        List<String> prizes = BELL_GIFT_MAP.get(bellKey);
        String giftKey = "";
        if (CollectionUtil.isNotEmpty(prizes)) {
            int i = ThreadLocalRandom.current().nextInt(prizes.size());
            giftKey = prizes.get(i);
            // 下发礼物到背包
            BaseParam baseParam = BaseParam.builder()
                    .uid(userId).appId(appId).unionId(null).build();
            SendPrizeDTO sendPrizeDTO = SendPrizeDTO.builder()
                    .appId(appId)
                    .prizeValue(giftKey)
                    .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                    .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                    .prizeEffectiveDay(15)
                    .prizeNum(1)
                    .toUid(userId)
                    .scene(ChristmasBellConstant.ACTIVITY_CODE)
                    .fee(false)
                    .build();
            sendPrizeManager.sendSceneGift(baseParam, sendPrizeDTO);
        }
        // 获取礼物信息
        CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(appId, giftKey).successData();
        if (null != coinGiftProductVO) {
            PrizeItem prizeItem = PrizeItem.builder()
                    .prizeKey(giftKey)
                    .prizeName(coinGiftProductVO.getName())
                    .prizeIcon(coinGiftProductVO.getIcon())
                    .valueGold(Convert.toInt(coinGiftProductVO.getNeedCoin(), 0)).build();
            result.add(prizeItem);
            // 埋点
            allActivityReceiveAward(userId, "open-bell", giftKey, coinGiftProductVO.getNeedCoin(), 1);
        }
        return CommonListVO.success(result);
    }

    /**
     * 获取好友列表
     * @param userId 用户 id
     * @param appId 应用 id
     * @param type UserRelationType
     * @return CommonListVO
     */
    public CommonListVO getFriendList(Long userId, Long appId, String type) {
        if (null == userId || null == appId) {
            return new CommonListVO();
        }
        Map<String, List<Long>> followMap = feignSnsService.getUserRelationMap(userId, appId, type).successData();
        if (CollectionUtil.isEmpty(followMap)) {
            return new CommonListVO();
        }
        List<Long> idList = followMap.get(type);
        log.debug("getFriendList userId {} idList {}", userId, JSON.toJSONString(idList));
        if (CollectionUtil.isEmpty(idList)) {
            return new CommonListVO();
        }
        // 获取用户信息，并过滤被拉黑用户
        List<UserVO> userList = feignUserService.acquireUsersBulkListPost(idList).successData();
        List<UserVO> newUserList = userList.stream().filter(user -> CommonStatus.enable.getCode().equals(user.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newUserList)) {
            return new CommonListVO();
        }
        List<cn.yizhoucp.ump.api.vo.activity.base.UserVO> result = new ArrayList<>();
        for (UserVO userVO : newUserList) {
            cn.yizhoucp.ump.api.vo.activity.base.UserVO user = cn.yizhoucp.ump.api.vo.activity.base.UserVO.builder()
                    .uid(userVO.getId())
                    .name(userVO.getName())
                    .avatar(userVO.getAvatar())
                    .build();
            result.add(user);
        }
        return CommonListVO.success(result);
    }


    /**
     * 礼物魔法主页
     * @param userId 用户 Id
     * @param appId 应用  id
     * @param extDate 扩展数据
     * @return BellSceneVO
     */
    public BellSceneVO giftMagic(Long userId, Long appId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        if (null == appId) {
            appId = 1L;
        }
        Long scene = null;
        if (StringUtils.isNotBlank(extDate)) {
            JSONObject jsonObject = JSON.parseObject(extDate);
            scene = Convert.toLong(jsonObject.get("scene"),null);
        }
        BellSceneVO result = new BellSceneVO();
        Long magicCount = 0L;
        Long toUid = getUserFriendRelation(userId);
        // 查询用户信息
        if (null == toUid) {
            // 获取我的信息
            UserVO userVO = feignUserService.getBasic(userId, appId).successData();
            if (null != userVO) {
                cn.yizhoucp.ump.api.vo.activity.base.UserVO selfInfo = cn.yizhoucp.ump.api.vo.activity.base.UserVO.builder()
                        .uid(userVO.getId())
                        .name(userVO.getName())
                        .avatar(userVO.getAvatar()).build();
                result.setSelfInfo(selfInfo);
            }
            // 获取场景信息
            result.setSceneList(getSceneList(userId, 0L));
            // 获取礼物信息
            List<BellSceneVO.GiftVO> sceneGift = getSceneGift(userId, toUid, appId, 1L, 0L);
            result.setGiftList(sceneGift);
            return result;
        }
        String key = String.format(ChristmasBellRedisEnum.bell_user_friend_magic_personal.getKey(), userId);
        magicCount = Convert.toLong(redisManager.score(key, toUid), 0L);
        // 获取用户信息
        List<UserVO> userList = feignUserService.acquireUsersBulkListPost(Arrays.asList(userId, toUid)).successData();
        List<UserVO> newUserList = userList.stream().filter(user -> CommonStatus.enable.getCode().equals(user.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(newUserList)) {
            Map<Long,UserVO> userMap = newUserList.stream().collect(Collectors.toMap(UserVO::getId, v -> v, (k1, k2) -> k1));
            UserVO u1 = userMap.get(userId);
            if (null != u1) {
                cn.yizhoucp.ump.api.vo.activity.base.UserVO selfInfo = cn.yizhoucp.ump.api.vo.activity.base.UserVO.builder()
                        .uid(u1.getId())
                        .name(u1.getName())
                        .avatar(u1.getAvatar()).build();
                result.setSelfInfo(selfInfo);
            }
            UserVO u2 = userMap.get(toUid);
            if (null != u2) {
                cn.yizhoucp.ump.api.vo.activity.base.UserVO friendInfo = cn.yizhoucp.ump.api.vo.activity.base.UserVO.builder()
                        .uid(u2.getId())
                        .name(u2.getName())
                        .avatar(u2.getAvatar()).build();
                result.setFriendInfo(friendInfo);
            }
         }
        result.setMagicCount(magicCount);
        // 查询场景信息，为空则查询当前解锁最高场景
        if (null == scene) {
            // 没有 scene，则默认查询最高场景
            ChristmasBellSceneEnum sceneEnum = ChristmasBellSceneEnum.getByMagicCount(magicCount);
            scene = sceneEnum.getId();
        }
        // 根据场景获取礼物信息
        List<BellSceneVO.GiftVO> sceneGift = getSceneGift(userId, toUid, appId, scene, magicCount);
        result.setGiftList(sceneGift);
        // 获取场景解锁状态
        List<BellSceneVO.SceneVO> sceneList = getSceneList(userId, magicCount);
        result.setSceneList(sceneList);
        return result;
    }

    /**
     * 获取场景礼物
     */
    public List<BellSceneVO.GiftVO> getSceneGift(Long userId, Long toUid, Long appId, Long scene, Long currentMagicCount) {
        List<BellSceneVO.GiftVO> result = new ArrayList<>();
        // 获取场景对应礼物
        List<ChristmasBellSceneDO> sceneList = christmasBellSceneJpaDAO.findBySceneId(scene);
        if (CollectionUtils.isEmpty(sceneList)) {
            return result;
        }
        // 获取礼物基本信息
        List<String> giftKeyList = sceneList.stream()
                .filter(v -> !DressUpType.head_frame.getCode().equals(v.getGiftType()))
                .map(ChristmasBellSceneDO::getGiftKey).collect(Collectors.toList());
        List<CoinGiftProductVO> coinGiftProductList = feignProductService.batchGetGiftByGiftKey(appId, JSON.toJSONString(giftKeyList)).successData();
        Map<String, CoinGiftProductVO> giftMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(coinGiftProductList)) {
            giftMap = coinGiftProductList.stream().collect(Collectors.toMap(CoinGiftProductVO::getGiftKey, vo -> vo, (k1, k2) -> k1));
        }
        for (ChristmasBellSceneDO sceneDO : sceneList) {
            BellSceneVO.GiftVO gift = new BellSceneVO.GiftVO();
            String giftKey = sceneDO.getGiftKey();
            // 礼物领取状态
            String userGiftReceiveStatus = "0";
            if (null != currentMagicCount && currentMagicCount >= sceneDO.getMagicCount()) {
                userGiftReceiveStatus = "1";
                if (getUserGiftReceiveStatus(userId, toUid, sceneDO.getId())) {
                    userGiftReceiveStatus = "2";
                }
            }
            // 头像框单独获取一下
            if (DressUpType.head_frame.getCode().equals(sceneDO.getGiftType())) {
                HeadFrameProductVO headFrameProductVO = feignProductService.getHfProductByKey(appId, giftKey).successData();
                if (null != headFrameProductVO) {
                    gift.setGiftId(sceneDO.getId());
                    gift.setGiftKey(giftKey);
                    gift.setGiftName(headFrameProductVO.getName());
                    gift.setGiftIcon(headFrameProductVO.getIcon());
                    gift.setGiftCoin(300L);
                    gift.setMagicCount(sceneDO.getMagicCount());
                    gift.setReceiveStatus(userGiftReceiveStatus);
                    result.add(gift);
                }
            } else {
                if (giftMap.containsKey(giftKey)) {
                    CoinGiftProductVO coinGiftProductVO = giftMap.get(giftKey);
                    if (null != coinGiftProductVO) {
                        gift.setGiftId(sceneDO.getId());
                        gift.setGiftKey(coinGiftProductVO.getGiftKey());
                        gift.setGiftName(coinGiftProductVO.getName());
                        gift.setGiftIcon(coinGiftProductVO.getIcon());
                        gift.setGiftCoin(sceneDO.getGiftCoin());
                        gift.setMagicCount(sceneDO.getMagicCount());
                        gift.setReceiveStatus(userGiftReceiveStatus);
                        result.add(gift);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取场景列表
     * @param userId 用户 id
     * @param currentMagicCount 当前用户魔法值
     * @return List
     */
    public List<BellSceneVO.SceneVO> getSceneList(Long userId, Long currentMagicCount) {
        List<BellSceneVO.SceneVO> result = new ArrayList<>();
        if (null == userId) {
            return result;
        }
        if (null == currentMagicCount) {
            currentMagicCount = 0L;
        }
        Long initialMagicCount = 0L;
        for (ChristmasBellSceneEnum scene : ChristmasBellSceneEnum.values()) {
            BellSceneVO.SceneVO sceneVO = new BellSceneVO.SceneVO();
            sceneVO.setSceneId(scene.getId());
            if (scene.getId().equals(ChristmasBellSceneEnum.scene1.getId())) {
                sceneVO.setLock(true);
            } else {
                sceneVO.setLock(currentMagicCount >= initialMagicCount);
            }
            initialMagicCount = scene.getMagicCount();
            result.add(sceneVO);
        }
        return result;
    }

    /**
     * 用户领取礼物
     * @param userId 用户 id
     * @param appId 应用 id
     * @param extDate 扩展数据
     * @return CommonResultVO
     */
    public CommonResultVO receiveGift(Long userId, Long appId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long giftId = null;
        Long toUid = null;
        if (StringUtils.isNotBlank(extDate)) {
            JSONObject jsonObject = JSON.parseObject(extDate);
            giftId = Convert.toLong(jsonObject.get("giftId"), null);
            toUid = Convert.toLong(jsonObject.get("toUid"), null);
        }
        if (null == giftId || null == toUid) {
            return CommonResultVO.fail("当前礼物不存在~");
        }
        // 获取礼物类型
        ChristmasBellSceneDO sceneDO = christmasBellSceneJpaDAO.findTopById(giftId);
        if (null == sceneDO || StringUtils.isBlank(sceneDO.getGiftKey())) {
            return CommonResultVO.fail("当前礼物不存在~");
        }
        // 判断是否领取
        Boolean userGiftReceiveStatus = getUserGiftReceiveStatus(userId, toUid, giftId);
        if (userGiftReceiveStatus) {
            return CommonResultVO.fail("该礼物已经领取了~");
        }
        String prizeType = PrizeTypeEnum.PRIZE_GIFT.getCode();
        if (DressUpType.head_frame.getCode().equals(sceneDO.getGiftType())) {
            prizeType = DressUpType.head_frame.getCode();
        }
        if (null == appId) {
            appId = 1L;
        }
        String giftKey = sceneDO.getGiftKey();
        BaseParam baseParam = BaseParam.builder()
                .uid(userId).appId(appId).unionId(null).build();
        if (PrizeTypeEnum.PRIZE_GIFT.getCode().equals(prizeType)) {
            // 礼物下发到背包
            SendPrizeDTO sendPrizeDTO = SendPrizeDTO.builder()
                    .appId(appId)
                    .prizeValue(giftKey)
                    .prizeType(prizeType)
                    .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                    .prizeEffectiveDay(15)
                    .prizeNum(1)
                    .toUid(userId)
                    .scene(ChristmasBellConstant.ACTIVITY_CODE)
                    .fee(false)
                    .build();
            sendPrizeManager.sendSceneGift(baseParam, sendPrizeDTO);
        } else {
            sendPrizeManager.sendDressUp(baseParam.getAppId(), baseParam.getUid(), DressUpType.getByCode(prizeType), giftKey, 15, 1);
        }
        // 更新用户领取状态
        updateUserGiftReceiveStatus(userId, toUid, sceneDO.getId());
        // 埋点
        allActivityReceiveAward(userId, "scene-collection", giftKey, sceneDO.getGiftCoin(), 1);
        return CommonResultVO.success();
    }

    /**
     * 送礼更新积分、魔法值
     */
    @ActivityCheck(activityCode = ChristmasBellConstant.ACTIVITY_CODE, isThrowException = false)
    public Boolean sendGiftHandler(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(coinGiftGivedModelList)) {
            log.warn("christmasBell sendGiftHandler 参数错误 param :{} coinGiftGivedModelList :{}",
                    JSONObject.toJSONString(param), JSONObject.toJSONString(coinGiftGivedModelList));
            return Boolean.FALSE;
        }
        try {
            Long userId = param.getUid();
            Long appId = param.getAppId();
            for (CoinGiftGivedModel giftGivedModel : coinGiftGivedModelList) {
                if (null == giftGivedModel) {
                    continue;
                }
                // 判断是否背包礼物
                if (GiftWay.PACKET.getCode().equals(giftGivedModel.getType())) {
                    continue;
                }
                // 是否活动礼物
                String giftKey = giftGivedModel.getGiftKey();
                if (!PANEL_GIFT_LIST.contains(giftKey)) {
                    continue;
                }
                Long coin = giftGivedModel.getCoin();
                if (null == coin) {
                    continue;
                }
                // 更新圣诞积分
                BigDecimal bd = new BigDecimal(coin);
                bd = bd.divide(BigDecimal.TEN, 1, RoundingMode.HALF_UP);
                double integral = bd.doubleValue();
                updateUserIntegral(userId, integral);
                // 更新魔法值
                Long toUid = giftGivedModel.getToUid();
                updateMagicCount(userId, toUid, appId, coin);
            }
        } catch (Exception e) {
            log.info("christmasBell sendGiftHandler error {}", JSON.toJSONString(e));
        }
        return Boolean.TRUE;
    }

    /**
     * 更新魔法值
     */
    public void updateMagicCount(Long fromUid, Long toUid, Long appId, Long coin) {
        if (null == fromUid || null == toUid || null == coin) {
            return;
        }
        // 判断两个人是否好友
        String relation = feignSnsService.getUserRelation(fromUid, toUid, appId).getData();
        if (!UserRelationType.friend.getCode().equals(relation)) {
            return;
        }
        // 更新魔法值
        Double magicCount = Convert.toDouble(coin, 0d);
        // 更新我的榜单
        String personalKey = String.format(ChristmasBellRedisEnum.bell_user_friend_magic_personal.getKey(), fromUid);
        redisManager.zIncrby(personalKey, toUid, magicCount, REDIS_KEY_EXPIRED_TIME);
        // 更新好友的榜单
        String friendKey = String.format(ChristmasBellRedisEnum.bell_user_friend_magic_personal.getKey(), toUid);
        redisManager.zIncrby(friendKey, fromUid, magicCount, REDIS_KEY_EXPIRED_TIME);
        String totalKey = ChristmasBellRedisEnum.bell_user_friend_magic_total.getKey();
        redisManager.zIncrby(totalKey, AppUtil.splicUserId(fromUid, toUid), magicCount, REDIS_KEY_EXPIRED_TIME);
    }


    /**
     * 获取活动榜单
     * @return RankVO
     */
    public RankVO getRank(Long currentUid) {
        RankVO result = new RankVO();
        String key =  ChristmasBellRedisEnum.bell_user_friend_magic_total.getKey();
        Set<ZSetOperations.TypedTuple<Object>> top10List = redisManager.reverseRangeWithScores(key, 0L, 9L);
        if (CollectionUtil.isEmpty(top10List)) {
            return result;
        }
        List<Long> idList = new ArrayList<>();
        for (ZSetOperations.TypedTuple<Object> typedTuple : top10List) {
            String uidStr = Convert.toStr(typedTuple.getValue(),"");
            idList.addAll(AppUtil.openSplicUserId(uidStr));
        }
        if (CollectionUtil.isEmpty(idList)) {
            return result;
        }
        // 获取用户信息
        List<UserVO> userVOList = feignUserService.acquireUsersBulkListPost(idList).successData();
        if (CollectionUtil.isEmpty(userVOList)) {
            return result;
        }
        List<CpRankItem> cpRankItemList = new ArrayList<>();
        Map<Long, UserVO> userMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, v -> v, (k1, k2) -> k1));
        long rank = 1;
        int giftIndex = 0;
        for (ZSetOperations.TypedTuple<Object> typedTuple : top10List) {
            String uidStr = Convert.toStr(typedTuple.getValue(),"");
            List<Long> uidList = AppUtil.openSplicUserId(uidStr);
            if (CollectionUtil.isEmpty(uidList)) {
                continue;
            }
            Long fromUid = uidList.get(0);
            Long toUid = uidList.get(1);
            // 是否隐藏头像
            boolean hide = true;
            if (uidList.contains(currentUid)) {
                // 榜单两个用户，我在第一个，好友在第二个
                fromUid = currentUid;
                toUid = uidList.stream()
                        .filter(uid -> !uid.equals(currentUid))
                        .findFirst()
                        .orElse(toUid);
                hide = false;
            }
            UserVO fromUser = userMap.getOrDefault(fromUid, new UserVO());
            UserVO toUser = userMap.getOrDefault(toUid, new UserVO());
            Long score = Convert.toLong(typedTuple.getScore(), 0L);
            List<RewardVO> rewardVOList = new ArrayList<>();
            if (giftIndex < RANK_GIFT_LIST.size()) {
                RewardVO rewardVO = RANK_GIFT_LIST.get(giftIndex++);
                rewardVOList.add(rewardVO);
            }
            CpRankItem rankItem = CpRankItem.builder()
                    .rank(rank)
                    .maleUid(fromUser.getId())
                    .maleUserName(hide ? HIDE_USER_NAME : fromUser.getName())
                    .maleAvatar(hide ? HIDE_USER_AVATAR : fromUser.getAvatar())
                    .femaleUid(toUser.getId())
                    .femaleUserName(hide ? HIDE_USER_NAME : toUser.getName())
                    .femaleAvatar(hide ? HIDE_USER_AVATAR : toUser.getAvatar())
                    .value(score)
                    .rewardVOList(rewardVOList)
                    .hide(hide).build();
            cpRankItemList.add(rankItem);
            rank++;
        }
        result.setCpRankItemList(cpRankItemList);
        // 获取我的排名
        result.setMyCpRankItem(getMyRank(currentUid));
        return result;
    }

    /**
     * 获取榜单中我的排名
     * @param currentUid 当前用户 id
     * @return CpRankItem
     */
    public CpRankItem getMyRank(Long currentUid) {
        CpRankItem result = new CpRankItem();
        Long friendUid = getUserFriendRelation(currentUid);
        Long magicCount = 0L;
        Long rank = -1L;
        if (null != friendUid) {
            // 获取魔法值
            String personalKey = String.format(ChristmasBellRedisEnum.bell_user_friend_magic_personal.getKey(), currentUid);
            magicCount = Convert.toLong(redisManager.score(personalKey, friendUid), 0L);
            UserVO friendUser = feignUserService.getBasic(friendUid, 1L).successData();
            if (null != friendUser) {
                result.setFemaleUid(friendUser.getId());
                result.setFemaleUserName(friendUser.getName());
                result.setFemaleAvatar(friendUser.getAvatar());
            }
            // 获取排名
            String totalKey = ChristmasBellRedisEnum.bell_user_friend_magic_total.getKey();
            rank = Convert.toLong(redisManager.reverseRank(totalKey, AppUtil.splicUserId(currentUid, friendUid)), -2L) + 1;
        }
        // 10 名以上显示未上榜
        if (rank > 10) {
            rank = -1L;
        }
        result.setRank(rank);
        result.setValue(magicCount);
        result.setHide(false);
        UserVO currentUser = feignUserService.getBasic(currentUid, 1L).successData();
        if (null != currentUser) {
            result.setMaleUid(currentUser.getId());
            result.setMaleUserName(currentUser.getName());
            result.setMaleAvatar(currentUser.getAvatar());
        }
        return result;
    }

    /**
     * 发送小助手消息
     */
    public void npcMessage(Long userId, String msg) {
        feignImService.npcTalk(SystemNPC.LANLING_LITTLE.getCode(), msg, ServicesNameEnum.lanling_services.getCode(), userId);
    }

    /**
     * 活动结束，下发奖励
     * @return Boolean
     */
    public Boolean christmasSendActivityTotalReward() {
        try {
            String key =  ChristmasBellRedisEnum.bell_user_friend_magic_total.getKey();
            Set<ZSetOperations.TypedTuple<Object>> top5List = redisManager.reverseRangeWithScores(key, 0L, 4L);
            if (CollectionUtil.isEmpty(top5List)) {
                log.info("christmasSendActivityTotalReward top5List is null");
            }
            Map<String, Integer> rewardGiftMap = new HashMap<>();
            rewardGiftMap.put("YHLAJ_GIFT", 5200);
            rewardGiftMap.put("AFEZY_GIFT", 3344);
            rewardGiftMap.put("AYS_GIFT", 1314);
            rewardGiftMap.put("AXSJP_GIFT", 888);
            rewardGiftMap.put("HLDG_GIFT", 888);

            List<String> giftList = new ArrayList<>();
            giftList.add("YHLAJ_GIFT");
            giftList.add("AFEZY_GIFT");
            giftList.add("AYS_GIFT");
            giftList.add("AXSJP_GIFT");
            giftList.add("HLDG_GIFT");

            int rank = 1;
            int giftIndex = 0;
            for (ZSetOperations.TypedTuple<Object> typedTuple : top5List) {
                String uidStr = Convert.toStr(typedTuple.getValue(),"");
                log.info("christmasSendActivityTotalReward userId {}", uidStr);
                List<Long> uidList = AppUtil.openSplicUserId(uidStr);
                if (CollectionUtil.isEmpty(uidList)) {
                    continue;
                }
                // 判断前五魔法值是否达到 520000
                Long score = Convert.toLong(typedTuple.getScore(), 0L);
                if (score < 520000) {
                    log.info("christmasSendActivityTotalReward score is less 520000 uid {} score {}", uidStr, score);
                    continue;
                }
                String giftKey = giftList.get(giftIndex++);
                Integer coin = rewardGiftMap.get(giftKey);
                // 下发奖励，两个人都需要
                Long userId = uidList.get(0);
                Long toUid = uidList.get(1);
                BaseParam baseParam1 = BaseParam.builder()
                        .uid(userId).appId(1L).unionId(null).build();
                SendPrizeDTO sendPrizeDto1 = SendPrizeDTO.builder()
                        .appId(1L)
                        .prizeValue(giftKey)
                        .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                        .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                        .prizeEffectiveDay(15)
                        .prizeNum(1)
                        .toUid(userId)
                        .scene(ChristmasBellConstant.ACTIVITY_CODE)
                        .fee(false)
                        .build();
                sendPrizeManager.sendSceneGift(baseParam1, sendPrizeDto1);
                // 下发第二个人
                BaseParam baseParam2 = BaseParam.builder()
                        .uid(toUid).appId(1L).unionId(null).build();
                SendPrizeDTO sendPrizeDto2 = SendPrizeDTO.builder()
                        .appId(1L)
                        .prizeValue(giftKey)
                        .prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode())
                        .expiredType(ExpiredTypeEnum.EFFECTIVE_DAYS.getCode())
                        .prizeEffectiveDay(15)
                        .prizeNum(1)
                        .toUid(userId)
                        .scene(ChristmasBellConstant.ACTIVITY_CODE)
                        .fee(false)
                        .build();
                sendPrizeManager.sendSceneGift(baseParam2, sendPrizeDto2);
                // 小助手消息
                npcMessage(userId, String.format(REWARD_MSG, rank));
                npcMessage(toUid, String.format(REWARD_MSG, rank));
                rank++;
                log.info("christmasSendActivityTotalReward userId {} toUid {} giftKey {}", userId, toUid, giftKey);
                // 埋点
                allActivityReceiveAward(userId, "double-list", giftKey, Convert.toLong(coin), 1);
            }
        } catch (Exception e) {
            log.info("swordsmanSendActivityTotalReward error {}", JSON.toJSONString(e));
        }
        return true;
    }


    /**
     * 通用埋点_活动任务完成
     *
     * @param uid      用户 id
     * @param taskType 任务类型
     * @param taskCost 任务消耗
     */
    public void allActivityTaskFinish(Long uid, String taskType, Long taskCost) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "jingle-bells");
        params.put("attribute_type", "platform_activity");
        params.put("task_type", Optional.ofNullable(taskType).orElse(""));
        params.put("task_cost", Optional.ofNullable(taskCost).orElse(0L));
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     *
     * @param uid         用户 id
     * @param awardType   奖品类型
     * @param awardKey    奖品 key
     * @param awardAmount 奖品对应的金币价值
     * @param awardCount  获得奖励的个数
     */
    public void allActivityReceiveAward(Long uid, String awardType, String awardKey, Long awardAmount, Integer awardCount) {
        Map<String, Object> params = new HashMap<>();
        params.put("activity_type", "jingle-bells");
        params.put("attribute_type", "platform_activity");
        params.put("award_type", Optional.ofNullable(awardType).orElse(""));
        params.put("award_key", Optional.ofNullable(awardKey).orElse(""));
        params.put("award_amount", Optional.ofNullable(awardAmount).orElse(0L));
        params.put("award_count", Optional.ofNullable(awardCount).orElse(0));
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 选择好友
     * @param userId 用户 id
     * @param extDate 扩展数据
     * @return CommonResultVO
     */
    public CommonResultVO chooseFriend(Long userId, String extDate) {
        if (null == userId || StringUtils.isBlank(extDate)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long toUid = 0L;
        if (StringUtils.isNotBlank(extDate)) {
            JSONObject jsonObject = JSON.parseObject(extDate);
            toUid = Convert.toLong(jsonObject.get("toUid"), 0L);
        }
        if (toUid == 0L) {
            return CommonResultVO.fail("请选择好友~");
        }
        updateUserFriendRelation(userId, toUid);
        return CommonResultVO.success();
    }
}