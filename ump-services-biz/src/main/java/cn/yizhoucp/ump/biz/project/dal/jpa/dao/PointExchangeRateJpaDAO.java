package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PointExchangeRateDO;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 积分汇率
 *
 * <AUTHOR>
 */
@Repository
public interface PointExchangeRateJpaDAO extends JpaSpecificationExecutor<PointExchangeRateDO>, CrudRepository<PointExchangeRateDO, Long> {

    /**
     * 根据 unionId 获取积分汇率列表
     *
     * @param unionId 应用唯一标识
     * @return List<PointExchangeRateDO>
     */
    List<PointExchangeRateDO> findByUnionIdOrderByUpdateTimeDesc(String unionId);

    /**
     * 根据 unionId、场景 获取积分汇率
     *
     * @param unionId 应用唯一标识
     * @param scene   场景
     * @param status  状态
     * @return PointExchangeRateDO
     */
    @Query(value = "select * from point_exchange_rate where union_id = ?1 and scene = ?2 and status != ?3", nativeQuery = true)
    PointExchangeRateDO findByUnionIdAndSceneAndStatusNot(String unionId, String scene, int status);

}
