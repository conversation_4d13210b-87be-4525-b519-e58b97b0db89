package cn.yizhoucp.ump.biz.framework.aop;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.biz.project.biz.manager.commonActivity.ActivityStatusManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * 监测活动是否开启切面
 * <AUTHOR>
 * @Date 2023/3/9 19:22
 * @Version 1.0
 */
@Slf4j
@Component
@Aspect
public class ActivityCheckAspect {

    @Resource
    private ActivityStatusManager activityStatusManager;

    @Pointcut("@annotation(cn.yizhoucp.ump.biz.framework.aop.ActivityCheck)")
    public void activityCheck() {
    }

    @Around("activityCheck()")
    public Object aroundHandler(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sign = (MethodSignature) joinPoint.getSignature();
        Method method = sign.getMethod();
        ActivityCheck annotation = method.getAnnotation(ActivityCheck.class);
        log.debug("check activityCode {}", annotation);
        if (activityStatusManager.activityIsEnable(BaseParam.ofMDC(), annotation.activityCode())) {
            return joinPoint.proceed();
        }
        if (annotation.isThrowException()) {
            throw new ServiceException(ErrorCode.ACTIVITY_NONE);
        }
        return null;
    }

}
