package cn.yizhoucp.ump.biz.project.biz.util;

import cn.yizhoucp.ms.core.base.util.MD5;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *  手机号工具类
 * Created by yang<PERSON> on 2018/2/5.
 */
public class MobileUtil {

    /**
     * 手机号码前缀
     * <p>
     * 移动号段：
     * 134 135 136 137 138 139 147 150 151 152 157 158 159 178 182 183 184 187 188 198
     * 联通号段：
     * 130 131 132 145 155 156 171 175 176 185 186 166
     * 电信号段：
     * 133 149 153 173 177 180 181 189 199
     * 虚拟运营商:
     * 170
     * <p>
     * 130-9
     * 145,7,9
     * 150-3,5-9
     * 170,1,3,5-8
     * 180-9
     */
    public static boolean isMobileNumber(String mobiles) {
        if (StringUtils.isBlank(mobiles) || StringUtils.length(mobiles) < 11) {
            return false;
        }
        Pattern p = Pattern.compile("^((13[0-9])|(14[5,7,9])|(15[^4,\\D])|(166)|(18[0-9])|(17[0,1,3,5,6,7,8])|(19[8-9]))\\d{8}$");
        Matcher m = p.matcher(mobiles);
        return m.matches();
    }

    /**
     * @param phone 手机号码
     * @return 加密之后的手机号码
     */
    public static String generatePhoneMD5(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        String md5Phone = MD5.getMD5(phone);// md5之后的手机号码
        char[] md5PhoneArray = md5Phone.toCharArray();
        StringBuffer md5PhoneResult = new StringBuffer(16);
        for (int i = 8; i < 24; i++) {
            md5PhoneResult.append(md5PhoneArray[i]);
        }
        return md5PhoneResult.toString();

    }

    /**
     * 屏蔽11位手机号的中间4位
     * @param mobile 手机号
     * @return 屏蔽后的手机号
     */
    public static String maskMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) return null;
        return StringUtils.replace(mobile, mobile.substring(3, mobile.length() - 4), "****", 1);
    }


}
