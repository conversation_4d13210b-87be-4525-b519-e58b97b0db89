package cn.yizhoucp.ump.biz.project.biz.constant.activity;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeSubTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/1 14:34
 * @Version 1.0
 */
public class TwinsConstant {

    public static final String ACTIVITY_CODE = "guard_constellation";

    /**
     * 当日礼盒送出限制（uid、礼盒key、当日日期）
     */
    public static final String BOX_LIMIT_TODAY = "ump:twins:sendLimit_%s_%s_%s";
    public static final String ASTROLOGICAL_VALUE = "astrological_value";

    @Getter
    @AllArgsConstructor
    public enum ConstellationPrizeLevelEnum {
        LEVEL_1(1, Lists.newArrayList(
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/test/mount/image/huiye.png").prizeKey("xingtuyaolan_mount").prizeName("30天星兔摇篮").effectiveDays(30).prizeNum(1).valueGold(3000).prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.MOUNT.getCode()).build(),
                PrizeItem.builder().prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-06/1688093951922408.png").prizeKey("JXXZ_GIFT").prizeName("巨蟹星座").prizeNum(1).valueGold(10000).prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).build())),
        LEVEL_2(3, Lists.newArrayList(
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/test/mount/image/huiye.png").prizeKey("xingtuyaolan_mount").prizeName("60天星兔摇篮").effectiveDays(60).prizeNum(1).valueGold(3000).prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.MOUNT.getCode()).build(),
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/branch_test/gift/image/64805a933414e7d5.png").prizeKey("XKZS_GIFT").prizeName("星空之誓").prizeNum(2).valueGold(10000).prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).build())),
        LEVEL_3(6, Lists.newArrayList(
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/test/mount/image/huiye.png").prizeKey("xingtuyaolan_mount").prizeName("90天星兔摇篮").effectiveDays(90).prizeNum(1).valueGold(3000).prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.MOUNT.getCode()).build(),
                PrizeItem.builder().prizeIcon("https://design-platform-cdn.yizhoucp.cn/gift-image/2023-06/1688107531485036.png").prizeKey("ZZXS_GIFT").prizeName("至尊星矢").prizeNum(1).valueGold(30000).prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).build())),
        LEVEL_4(9, Lists.newArrayList(
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/test/mount/image/huiye.png").prizeKey("xingtuyaolan_mount").prizeName("120天星兔摇篮").effectiveDays(120).prizeNum(1).valueGold(3000).prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.MOUNT.getCode()).build(),
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/branch_test/gift/image/64805a2a1b49d321.png").prizeKey("TSST_GIFT").prizeName("天使圣庭").prizeNum(2).valueGold(30000).prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).build())),
        LEVEL_5(12, Lists.newArrayList(
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/test/mount/image/huiye.png").prizeKey("xingtuyaolan_mount").prizeName("180天星兔摇篮").effectiveDays(180).prizeNum(1).valueGold(3000).prizeType(PrizeTypeEnum.PRIZE_DRESS.getCode()).prizeSubType(PrizeSubTypeEnum.MOUNT.getCode()).build(),
                PrizeItem.builder().prizeIcon("https://res-cdn.nuan.chat/res/branch_test/gift/image/64805a2a1b49d321.png").prizeKey("TSST_GIFT").prizeName("天使圣庭").prizeNum(3).valueGold(30000).prizeType(PrizeTypeEnum.PRIZE_GIFT.getCode()).build()));

        private Integer limit;
        private List<PrizeItem> prizeItemList;
    }

    @Getter
    @AllArgsConstructor
    public enum BoxPoolEnum {
        HHXYLH_GIFT("豪华星座礼盒"), GJXYLH_GIFT("高级星座礼盒"), CJXYLH_GIFT("初级星座礼盒"),
        HHXYLH_GIFT_H("豪华星座礼盒高爆奖池"), GJXYLH_GIFT_H("高级星座礼盒高爆奖池"), CJXYLH_GIFT_H("初级星座礼盒"),
        HHXYLH_GIFT_TIME("豪华星座礼盒触发星运时刻"), GJXYLH_GIFT_TIME("高级星座礼盒触发星运时刻"), CJXYLH_GIFT_TIME("初级星座礼盒触发星运时刻"),
        HHXYLH_GIFT_TRUE("豪华礼盒碎片奖池"), GJXYLH_GIFT_TRUE("高级礼盒碎片奖池"), CJXYLH_GIFT_TRUE("初级礼盒碎片奖池"),
        ;
        private String desc;
    }

    /**
     * 十二星座集合
     */
    @Getter
    @AllArgsConstructor
    public enum ConstellationEnum {
        Aquarius("水瓶座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Aquarius.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Aquarius.png"),
        Pisces("双鱼座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Pisces.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Pisces.png"),
        Aries("白羊座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Aries.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Aries.png"),
        Taurus("金牛座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Taurus.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Taurus.png"),
        Gemini("双子座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Gemini.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Gemini.png"),
        Cancer("巨蟹座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Cancer.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Cancer.png"),
        shouhushizizuo("狮子座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Leo.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Leo.png"),
        Virgo("处女座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Virgo.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Virgo.png"),
        Libra("天秤座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Libra.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Libra.png"),
        Scorpio("天蝎座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Scorpio.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Scorpio.png"),
        Sagittarius("射手座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Sagittarius.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Sagittarius.png"),
        Capricorn("摩羯座", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/open/open/Capricorn.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/activity/twins/close/close/Capricorn.png");

        private String desc;
        private String openIcon;
        private String closeIcon;

        public static ConstellationEnum getByName(String param) {
            for(ConstellationEnum item:ConstellationEnum.values()){
                if(item.name().equals(param)){
                    return item;
                }
            }
            return null;
        }

        public String getIcon(Integer status) {
            // 已点亮取 openIcon
            if (status >= 1) {
                return getOpenIcon();
            } else {
                return getCloseIcon();
            }
        }

        public static Set<ConstellationEnum> getByOrdinalList(Set<Integer> ordinals) {
            return Arrays.stream(values()).filter(item -> ordinals.contains(item.ordinal())).collect(Collectors.toSet());
        }


        public static ConstellationEnum getByMonthAndDay(int month, int day) {
            ConstellationEnum[] astro = {Capricorn, Aquarius, Pisces, Aries, Taurus, Gemini, Cancer, shouhushizizuo, Virgo, Libra, Scorpio, Sagittarius, Capricorn};
            int[] arr = {20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22}; // 两个星座分割日
            return day < arr[month - 1] ? astro[month - 1] : astro[month];
        }

    }


    // ======== lock =========
    /**
     * 榜单key 初始化
     */
    public static final String EXCHANGE_GIFT_LOCK = "ump:twins:exchangeGiftLock:%s";

    // ======== 缓存key  =========
    /**
     * 个人星运时刻（String - uid）
     */
    public static final String LUCKY_TIME_PERSON = "ump:twins:luckyTime_%s";
    /**
     * 全服星运时刻
     */
    public static final String LUCKY_TIME_GLOBAL = "ump:twins:luckyTimeGlobal";
    public static final String ENDORSEMENT_INFO_CACHE = "ump:twins:endorsementInfo:%s";
    /**
     * 星运值 %s -> activityCode  hash item (uid,value)
     */
    public static final String LUCK_VALUE_CACHE = "ump:twins:luckValue:%s";
    /**
     * 兑换记录列表 %s -> 日期  hash item (uid,value)
     */
    public static final String EXCHANGE_GIFT_RECORD = "ump:twins:exchangeGiftRecord:%s";

    // QYF_GIFT 礼物key
    public static final String QYK_GIFT = "QYK_GIFT";

    public static final String ASTROLOGICAL_VALUE_POOL = "ASTROLOGICAL_VALUE_POOL";

}
