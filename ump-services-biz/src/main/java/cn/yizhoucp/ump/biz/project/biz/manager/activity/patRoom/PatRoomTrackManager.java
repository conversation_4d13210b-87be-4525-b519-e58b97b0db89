package cn.yizhoucp.ump.biz.project.biz.manager.activity.patRoom;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class PatRoomTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(String awardKey, Long awardAmount, Integer awardCount, Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "pat_blind_box");
        params.put("attribute_type", "platform_activity");
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

}
