package cn.yizhoucp.ump.biz.project.dal.mybatis.service;

import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.LnRation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ln_ration(传说中的梦幻岛口粮表)】的数据库操作Service
* @createDate 2024-12-08 20:13:07
*/
public interface LnRationService extends IService<LnRation> {

    LnRation getByUniqueKey(String uniqueKey);

    /**
     * 获取所有食物，根据price 升序
     */
    List<LnRation> getAllForLimitAndPriceASC(Long limit);
}
