package cn.yizhoucp.ump.biz.project.biz.manager.activity.moon_screat.common;

import cn.hutool.core.collection.CollUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityRedisKeyGenerator;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 月夜下的秘密redis管理类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 14:42 2025/4/23
 */
@Slf4j
@Component
public class MoonScreatRedisManager {
    @Resource
    private RedisManager redisManger;
    @Resource
    private ActivityRedisKeyGenerator activityRedisKeyGenerator;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    public Long getUserTaskProgress(Long uid, String cardName) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        Object hget = redisManger.hget(key, cardName);
        if(hget == null){
            return 0L;
        }
        return Long.valueOf(hget.toString());
    }
    public Long getRoomTaskProgress(Long uid, String cardName) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        Object hget = redisManger.hget(key, cardName);
        if(hget == null){
            return 0L;
        }
        return Long.valueOf(hget.toString());
    }


    public Long getPkScore(String name) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.PK_SCORE_KEY, MoonScreatConstant.ACTIVITY_CODE, name, DateUtil.getNowYyyyMMdd());
        return Optional.ofNullable( redisManger.getLong(key)).orElse(0L);
    }

    public String getTimeInfo() {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.TIME_INFO_KEY, MoonScreatConstant.ACTIVITY_CODE,DateUtil.getNowYyyyMMdd());
        return redisManger.getString(key);
    }

    public Double incrementUserCardCount(Long uid, String cardType,Long num) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        return redisManger.hincr(key,cardType,num,DateUtil.ONE_MONTH_SECOND);
    }

    public Double incrementRoomCardCount(Long relation, String cardType, long num) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, relation.toString(),DateUtil.getNowYyyyMMdd());
        return redisManger.hincr(key,cardType,num,DateUtil.ONE_MONTH_SECOND);
    }



    public void setFaction(Long uid, String factionId) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_FACTION_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        redisManger.set(key,factionId,DateUtil.ONE_MONTH_SECOND);
    }

    public String getFaction(Long uid) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_FACTION_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        return redisManger.getString(key);
    }

    public void incrementPkScore(String factionId, long dropCount) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.PK_SCORE_KEY, MoonScreatConstant.ACTIVITY_CODE, factionId,DateUtil.getNowYyyyMMdd());
        redisManger.incrLong(key,dropCount,DateUtil.ONE_MONTH_SECOND);
    }

    public String getUserRankKey(String faction) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_RANK_KEY, MoonScreatConstant.ACTIVITY_CODE,faction)+":"+DateUtil.getNowYyyyMMdd();
    }

    /**
     * 获取前一天的用户排行榜键
     * @param faction 阵营
     * @return 排行榜键
     */
    public String getUserRankKeyForYesterday(String faction) {
        // 获取前一天的日期
        String yesterday = DateUtil.format(DateUtil.getBeforeDay(new Date()), DateUtil.YMD_WITHOUT_LINE);
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_RANK_KEY, MoonScreatConstant.ACTIVITY_CODE,faction)+":"+yesterday;
    }

    public String getUserRankKeyTemplate(String faction) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_RANK_KEY, MoonScreatConstant.ACTIVITY_CODE,faction);
    }


    public String getRoomRankKey(String faction) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_RANK_KEY, MoonScreatConstant.ACTIVITY_CODE, faction)+":"+DateUtil.getNowYyyyMMdd();
    }

    /**
     * 获取前一天的房间排行榜键
     * @param faction 阵营
     * @return 排行榜键
     */
    public String getRoomRankKeyForYesterday(String faction) {
        // 获取前一天的日期
        String yesterday = DateUtil.format(DateUtil.getBeforeDay(new Date()), DateUtil.YMD_WITHOUT_LINE);
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_RANK_KEY, MoonScreatConstant.ACTIVITY_CODE, faction)+":"+yesterday;
    }

    public String getRoomRankKeyTemplate(String faction) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_RANK_KEY, MoonScreatConstant.ACTIVITY_CODE, faction);
    }


    public List<ScenePrizeDO> getScenePrizeDOS(List<String> rewards) {
        if (rewards == null || rewards.isEmpty()) {
            return CollUtil.newArrayList();
        }
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.SCENE_PRIZE_MAP_KEY, MoonScreatConstant.ACTIVITY_CODE);
        String json = redisManger.getString(key);
        if (json == null) {
            List<ScenePrizeDO> userPrizeDOS= scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(),getActivityCode(),MoonScreatConstant.USER_TASK_GIFT_CODE);
            List<ScenePrizeDO> roomPrizeDOS= scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(),getActivityCode(),MoonScreatConstant.ROOM_TASK_GIFT_CODE);
            if(userPrizeDOS==null||roomPrizeDOS==null){
                return CollUtil.newArrayList();
            }
            Map<String, ScenePrizeDO> map = CollUtil.addAll(userPrizeDOS, roomPrizeDOS).stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, Function.identity(),(existing,replacement)->existing));
            json = JSON.toJSONString(map);
            redisManger.set(key,json,DateUtil.ONE_MONTH_SECOND);
            return rewards.stream().map(map::get).collect(Collectors.toList());
        }
        Map<String, ScenePrizeDO> map = JSON.parseObject(json, new com.alibaba.fastjson.TypeReference<Map<String, ScenePrizeDO>>() {
        });
        return rewards.stream().map(map::get).collect(Collectors.toList());
    }

    private String getActivityCode() {
        return MoonScreatConstant.ACTIVITY_CODE;
    }

    public Boolean getRankLock() {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.RANK_LOCK_KEY, MoonScreatConstant.ACTIVITY_CODE,DateUtil.getNowYyyyMMdd());
        return redisManger.setnx(key,System.currentTimeMillis(),DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 检查任务是否已完成
     * @param collectionType 收集类型（用户/房间）
     * @param id 用户ID或房间ID
     * @param levelId 等级ID
     * @param taskId 任务ID
     * @return 是否已完成
     */
    public boolean isTaskCompleted(String collectionType, Long id, Integer levelId, String taskId) {
        if (id == null || levelId == null || taskId == null) {
            return false;
        }
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                MoonScreatConstant.TASK_COMPLETED_KEY,
                MoonScreatConstant.ACTIVITY_CODE,
                collectionType,
                id.toString(),
                levelId.toString(),
                taskId,
                DateUtil.getNowYyyyMMdd()
                );
        String value = redisManger.getString(key);
        return MoonScreatConstant.TASK_COMPLETED_VALUE.equals(value);
    }

    /**
     * 标记任务已完成
     * @param collectionType 收集类型（用户/房间）
     * @param id 用户ID或房间ID
     * @param levelId 等级ID
     * @param taskId 任务ID
     */
    public void markTaskCompleted(String collectionType, Long id, Integer levelId, String taskId) {
        if (id == null || levelId == null || taskId == null) {
            return;
        }
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                MoonScreatConstant.TASK_COMPLETED_KEY,
                MoonScreatConstant.ACTIVITY_CODE,
                collectionType,
                id.toString(),
                levelId.toString(),
                taskId,
                DateUtil.getNowYyyyMMdd()
                );
        redisManger.set(key, MoonScreatConstant.TASK_COMPLETED_VALUE, DateUtil.ONE_MONTH_SECOND);
    }

    public Long getRankLength(String rankKey) {
        return Optional.ofNullable(redisManger.zSetGetSize(rankKey)).orElse(0L);
    }

    public Long getUserContribution(Long uid, String factionId) {
        if (uid == null || factionId == null) {
            return 0L;
        }
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_CONTRIBUTION_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(), factionId,DateUtil.getNowYyyyMMdd());
        return Optional.ofNullable(redisManger.getLong(key)).orElse(0L);
    }

    public void incrementUserContribution(Long uid, String factionId,Long num) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_CONTRIBUTION_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(), factionId,DateUtil.getNowYyyyMMdd());
        redisManger.incrLong(key,num,DateUtil.ONE_MONTH_SECOND);
    }

    /**
     * 获取场景奖励缓存
     * @param sceneCode 场景码
     * @return 场景奖励列表
     */
    public List<ScenePrizeDO> getScenePrizeCache(String sceneCode) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                MoonScreatConstant.SCENE_PRIZE_CACHE_KEY,
                MoonScreatConstant.ACTIVITY_CODE,
                sceneCode);
        String json = redisManger.getString(key);
        if (json == null) {
            return null;
        }
        return JSON.parseArray(json, ScenePrizeDO.class);
    }

    /**
     * 设置场景奖励缓存
     * @param sceneCode 场景码
     * @param scenePrizeDOS 场景奖励列表
     */
    public void setScenePrizeCache(String sceneCode, List<ScenePrizeDO> scenePrizeDOS) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                MoonScreatConstant.SCENE_PRIZE_CACHE_KEY,
                MoonScreatConstant.ACTIVITY_CODE,
                sceneCode);
        redisManger.set(key, JSON.toJSONString(scenePrizeDOS), MoonScreatConstant.CACHE_EXPIRE_SECONDS);
    }


    /**
     * 保存每日胜利阵营
     * @param date 日期，格式：yyyyMMdd
     * @param winner 胜利阵营
     */
    public void saveDailyWinner(String date, String winner) {
        if (date == null || winner == null) {
            return;
        }
        String key = String.format(MoonScreatConstant.DAILY_WINNER_KEY, MoonScreatConstant.ACTIVITY_CODE, date);
        redisManger.set(key, winner, MoonScreatConstant.WINNER_CACHE_EXPIRE_SECONDS);
    }

    /**
     * 获取每日胜利阵营
     * @param date 日期，格式：yyyyMMdd
     * @return 胜利阵营
     */
    public String getDailyWinner(String date) {
        if (date == null) {
            return null;
        }
        String key = String.format(MoonScreatConstant.DAILY_WINNER_KEY, MoonScreatConstant.ACTIVITY_CODE, date);
        return redisManger.getString(key);
    }

    public Long getTestTimestamp() {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.TEST_TIMESTAMP_KEY, MoonScreatConstant.ACTIVITY_CODE);
        return redisManger.getLong(key);
    }

    public Long getUserVillagerCount(Long uid) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        Map<Object, Object> hmget = redisManger.hmget(key);
        if(hmget==null){
            return 0L;
        }
        long count = 0L;
        try {
            for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
                String cardName = entry.getKey().toString();
                MoonScreatEnums.CardTypeEnum cardTypeEnum = MoonScreatEnums.CardTypeEnum.valueOf(cardName);
                if (MoonScreatEnums.FactionsEnum.VILLAGER_CAMP.name().equals(cardTypeEnum.getFaction().name())) {
                    count += Long.parseLong(entry.getValue().toString());
                }
            }
        } catch (Exception e) {
            log.warn("getUserVillagerCount error {}", e.getMessage());
        }
        return count;
    }

    public Long getUserWerewolfCount(Long uid) {
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.USER_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, uid.toString(),DateUtil.getNowYyyyMMdd());
        Map<Object, Object> hmget = redisManger.hmget(key);
        if(hmget==null){
            return 0L;
        }
        long count = 0L;
        try {
            for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
                String cardName = entry.getKey().toString();
                MoonScreatEnums.CardTypeEnum cardTypeEnum = MoonScreatEnums.CardTypeEnum.valueOf(cardName);
                if (MoonScreatEnums.FactionsEnum.WEREWOLF_CAMP.name().equals(cardTypeEnum.getFaction().name())) {
                    count += Long.parseLong(entry.getValue().toString());
                }
            }
        } catch (Exception e) {
            log.warn("getUserVillagerCount error {}", e.getMessage());
        }
        return count;
    }

    public Long getRoomVillagerCount(Long roomId) {
        if (roomId == null) {
            return 0L;
        }
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, roomId.toString(),DateUtil.getNowYyyyMMdd());
        Map<Object, Object> hmget = redisManger.hmget(key);
        if(hmget==null){
            return 0L;
        }
        long count = 0L;
        try {
            for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
                String cardName = entry.getKey().toString();
                MoonScreatEnums.CardTypeEnum cardTypeEnum = MoonScreatEnums.CardTypeEnum.valueOf(cardName);
                if (MoonScreatEnums.FactionsEnum.VILLAGER_CAMP.name().equals(cardTypeEnum.getFaction().name())) {
                    count += Long.parseLong(entry.getValue().toString());
                }
            }
        } catch (Exception e) {
            log.warn("getUserVillagerCount error {}", e.getMessage());
        }
        return count;
    }
    public Long getRoomWerewolCount(Long roomId) {
        if (roomId == null) {
            return 0L;
        }
        String key= activityRedisKeyGenerator.generateKeyWithActivityStartTime(MoonScreatConstant.ROOM_CARD_COUNT_KEY, MoonScreatConstant.ACTIVITY_CODE, roomId.toString(),DateUtil.getNowYyyyMMdd());
        Map<Object, Object> hmget = redisManger.hmget(key);
        if(hmget==null){
            return 0L;
        }
        long count = 0L;
        try {
            for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
                String cardName = entry.getKey().toString();
                MoonScreatEnums.CardTypeEnum cardTypeEnum = MoonScreatEnums.CardTypeEnum.valueOf(cardName);
                if (MoonScreatEnums.FactionsEnum.WEREWOLF_CAMP.name().equals(cardTypeEnum.getFaction().name())) {
                    count += Long.parseLong(entry.getValue().toString());
                }
            }
        } catch (Exception e) {
            log.warn("getUserVillagerCount error {}", e.getMessage());
        }
        return count;
    }

}
