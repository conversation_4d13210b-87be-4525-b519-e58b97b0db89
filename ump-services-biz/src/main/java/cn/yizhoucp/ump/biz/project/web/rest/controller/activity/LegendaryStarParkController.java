package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.AuthConstant;
import cn.yizhoucp.ms.core.base.auth.Authorize;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkHistoryCaptainVO;
import cn.yizhoucp.ump.api.vo.activity.legendaryStarPark.LegendaryStarParkRankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.legendary_star_park.LegendaryStarParkIndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class LegendaryStarParkController {

    @Resource
    private LegendaryStarParkIndexManager legendaryStarParkIndexManager;

    @GetMapping("/api/inner/activity/legendaryStarPark/get-rank")
    public Result<LegendaryStarParkRankVO> getRank(String activityCode, String poolCode) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                legendaryStarParkIndexManager.getRank(poolCode)
        );
    }

    @GetMapping("/api/inner/activity/legendaryStarPark/claimedReward")
    public Result<Boolean> claimedReward(String sceneCode,String taskId,String extData){
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                legendaryStarParkIndexManager.getClaimedReward(sceneCode,taskId,extData));
    }

    @GetMapping("/api/inner/activity/legendaryStarPark/captains/history")
    public Result<LegendaryStarParkHistoryCaptainVO> getCaptainsHistory(String poolCode, Long roomId) {
        return RestBusinessTemplate.executeWithoutTransaction(() ->
                legendaryStarParkIndexManager.getCaptainsHistory(poolCode, roomId));
    }

    /**
     * 下发舰长金币奖励
     */
    @Authorize(authRule = AuthConstant.AUTHORIZATION_USER_ANONYMOUS)
    @GetMapping("/api/inner/activity/legendaryStarPark/send-captain-reward")
    public Result<Boolean> sendCaptainReward() {
        return RestBusinessTemplate.executeWithoutTransaction(() -> legendaryStarParkIndexManager.sendCaptainReward());
    }
}
