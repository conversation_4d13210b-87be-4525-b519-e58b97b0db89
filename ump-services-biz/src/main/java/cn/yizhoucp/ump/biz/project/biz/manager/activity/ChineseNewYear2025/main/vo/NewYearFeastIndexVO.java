package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class NewYearFeastIndexVO {
    private List<Dishes> dishes;
    private CurrentDish currentDish;
    private List<Ingredients> ingredients;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class CurrentDish{
        private String dishKey;
        private Long countDown;
        private Integer status;
        private List<Ingredients> requiredIngredients;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Dishes {
        private String dishKey;
        private String dishName;
        private Integer cookingTime;
        private List<Ingredients> requiredIngredients;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Ingredients {
        private String ingredientKey;
        private Integer quantity;
        private String ingredientName;
    }
}
