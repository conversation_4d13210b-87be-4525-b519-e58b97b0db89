//package api.project.biz.manager.activity.implement.getMissionList;
//
//import api.project.biz.enums.activity.jimu.getMissionList.DrawTypeEnum;
//import api.project.biz.manager.activity.implement.getMissionList.context.DrawContext;
//import api.project.biz.manager.activity.implement.getMissionList.context.DrawParam;
//import api.project.biz.manager.activity.richMan.RichManager;
//import api.project.dal.jpa.dataobject.DrawPoolItemDO;
//import cn.yizhoucp.ms.core.base.ErrorCode;
//import cn.yizhoucp.ms.core.base.ServiceException;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.*;
//
///**
// * 520 活动抽奖
// *
// * @author: lianghu
// */
//@Slf4j
//@Component
//public class RichDrawManager extends AbstractDrawManager {
//
//    @Resource
//    private RichManager richManager;
//    @Resource
//    private ThreadPoolTaskExecutor reportHandleThreadPool;
//
//    @Override
//    protected void resourceCheck(DrawContext context) {
//        DrawParam param = context.getDrawParam();
//
//        Long fromUid = param.getUid();
//        Long toUid = Long.valueOf(param.getExtValue());
//        if (Objects.isNull(fromUid) || Objects.isNull(toUid)) {
//            throw new ServiceException(ErrorCode.INVALID_PARAM);
//        }
//
////        // 1. 活动开关验证
////        if (!richManager.activityIsEnable()) {
////            throw new ServiceException(ErrorCode.ACTIVITY_END_ERROR);
////        }
////        // 2. 幂等验证
////        if (richManager.hasDicePositionFinish(fromUid, toUid)) {
////            log.warn("richman 当前位置已完成 fromUid:{} toUid:{}", fromUid, toUid);
////            throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180031);
////        }
////        // 3. 业务验证
////        if (RICHMAN_DEEPRELATION_WITCH_CHECK.getCode().equals(param.getPoolCode()) && !richManager.hasCardiac(fromUid, toUid)) {
////            log.warn("richman 尚未达成心动关系 fromUid:{}, toUdi:{}", fromUid, toUid);
////            throw new ServiceException(ErrorCode.ACTIVITY_ERROR_180030);
////        }
//    }
//
//    @Override
//    protected void deductResource(DrawContext context) {
//
//    }
//
//    @Override
//    protected void callback(DrawContext context) {
//        // 记录本次抽奖已完成
//        DrawParam param = context.getDrawParam();
//        richManager.activeFinish(param.getUid(), Long.valueOf(param.getExtValue()));
//        // 小助手消息、数据埋点
//        Long uid = Optional.ofNullable(param.getUid()).orElse(-1L);
//        Long toUid = Long.valueOf(param.getExtValue());
//        reportHandleThreadPool.submit(() -> handle(uid, toUid, param.getType(), context.getPrizeDOList()));
//    }
//
//    private void handle(Long uid, Long toUid, String type, List<DrawPoolItemDO> prizeDOList) {
//        try {
//            log.info("richman 抽奖后置处理 uid:{}, toUid:{}, type:{}, prize:{}", uid, toUid, type, prizeDOList);
//            track(uid, type, prizeDOList);
//            notify(uid, toUid, type, prizeDOList);
//        } catch (Exception e) {
//            log.error("richman 抽奖后置处理异常", e);
//        }
//    }
//
//    private void notify(Long uid, Long toUid, String type, List<DrawPoolItemDO> prizeDOList) {
//        if (CollectionUtils.isEmpty(prizeDOList)) {
//            return;
//        }
//        if (Objects.isNull(toUid)) {
//            return;
//        }
//
//        String text = "";
//        if (DrawTypeEnum.RICHMAN_DEEPRELATION_WITCH_CHECK.getCode().equals(type)
//                || DrawTypeEnum.RICHMAN_DEEPRELATION.getCode().equals(type)) {
//            text = String.format("【双向奔赴】您已获得 %s，奔赴成功可返 60%% 的金币哦，点击 <a href=\"%s\">去看看</a>", prizeDOList.get(0).getItemName(), richManager.getPageRoute(toUid));
//        } else if (DrawTypeEnum.RICHMAN_COIN.getCode().equals(type)) {
//            text = String.format("【双向奔赴】您已获得 [金币]x%s，奔赴成功可返 60%% 的金币哦，点击 <a href=\"%s\">去看看</a>", prizeDOList.get(0).getItemNum(), richManager.getPageRoute(toUid));
//        }
//
//        log.info("richman text:{}", text);
//
//        if (StringUtils.isNotBlank(text)) {
//            notifyComponent.npcNotify(uid, text);
//        }
//    }
//
//    private void track(Long uid, String type, List<DrawPoolItemDO> prizeDOList) {
//        if (DrawTypeEnum.RICHMAN_DEEPRELATION_WITCH_CHECK.getCode().equals(type)
//                || DrawTypeEnum.RICHMAN_DEEPRELATION.getCode().equals(type)) {
//            giftTrack(uid, prizeDOList.get(0).getItemKey());
//        } else if (DrawTypeEnum.RICHMAN_COIN.getCode().equals(type)) {
//            if (!CollectionUtils.isEmpty(prizeDOList)) {
//                goldTrack(uid, Long.valueOf(prizeDOList.get(0).getItemKey()));
//            }
//        } else {
//            log.error("richman 抽奖流程异常");
//        }
//    }
//
//
//    private void goldTrack(Long uid, Long much) {
//        Map<String, Object> params = new HashMap();
//        params.put("uid", uid);
//        params.put("much", much);
//        producerManager.umpTrack(uid, "love_lottery_gold", params);
//    }
//
//    private void giftTrack(Long uid, String key) {
//        Map<String, Object> params = new HashMap();
//        params.put("uid", uid);
//        params.put("gift_key", StringUtils.isBlank(key) ? "" : key);
//        params.put("much", giftValueGoldByKey(key));
//        producerManager.umpTrack(uid, "love_lottery_gift", params);
//    }
//
//
//    private Long giftValueGoldByKey(String key) {
//        if ("LITTLE_FLOWER".equals(key)) {
//            return 1L;
//        } else if ("XDBD_GIFT".equals(key)) {
//            return 10L;
//        } else if ("YJQX_GIFT".equals(key)) {
//            return 25L;
//        } else if ("MMD_GIFT".equals(key)) {
//            return 52L;
//        } else if ("BBQS_GIFT".equals(key)) {
//            return 99L;
//        } else if ("100".equals(key)) {
//            return 100L;
//        } else if ("FTXZ_GIFT".equals(key)) {
//            return 188L;
//        } else if ("200".equals(key)) {
//            return 200L;
//        } else if ("QBT_GIFT".equals(key)) {
//            return 399L;
//        } else if ("400".equals(key)) {
//            return 400L;
//        } else {
//            return 1L;
//        }
//    }
//}
