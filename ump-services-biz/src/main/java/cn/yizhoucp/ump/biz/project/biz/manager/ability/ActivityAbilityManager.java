package cn.yizhoucp.ump.biz.project.biz.manager.ability;

import cn.yizhoucp.ump.biz.project.biz.enums.AdSpaceStatusEnum;
import cn.yizhoucp.ump.biz.project.biz.manager.appAdSpace.AppAdSpaceAdminManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.AppAdSpaceJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.AppAdSpaceDO;
import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageAttrsActivity;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.client.BuoyInfoVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.client.inner.BuoyInfoItem;
import cn.yizhoucp.ump.biz.project.biz.manager.clientPush.ServerPushManager;
import cn.yizhoucp.ump.biz.project.biz.util.LanlingActivityUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 活动通用能力
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class ActivityAbilityManager {

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private ServerPushManager serverPushManager;
    @Resource
    private List<BuoyNumHandle> buoyNumHandleList;
    @Resource
    private AppAdSpaceJpaDAO appAdSpaceJpaDao;
    @Resource
    private AppAdSpaceAdminManager appAdSpaceAdminManager;

    /**
     * 主动更新浮标角标数字
     *
     * @param toUid
     * @param list
     * @return boolean
     */
    public boolean updateBuoyInfo(Long toUid, List<BuoyInfoItem> list) {
        SystemMessageAttrsActivity content = new SystemMessageAttrsActivity();
        content.setBuoyInfoItemList(list);
        serverPushManager.sendBuoyNumServerPush(SystemNPC.LANLING_LITTLE, toUid, content);
        return true;
    }

    /**
     * 获取浮标角标数字
     *
     * @param appId
     * @return cn.yizhoucp.ms.core.vo.umpServices.activity.common.client.BuoyInfoVO
     */
    public BuoyInfoVO getBuoyInfo(Long appId) {

        // 获取活动浮标数字信息
        List<BuoyInfoItem> buoyInfoItemList = new ArrayList<>();
        for (BuoyNumHandle handle : buoyNumHandleList) {
            buoyInfoItemList.addAll(handle.getBuoyInfo());
        }

        return BuoyInfoVO.builder()
                .buoyInfoItemList(buoyInfoItemList).build();
    }

    /**
     * 获取页面跳转链接
     *
     * @param jumpCode     drawPost-奖池公示页面
     * @param activityCode
     * @param type
     * @return java.lang.String
     */
    public JSONObject getJumpUrl(String jumpCode, String activityCode, String type) {
        // todo: 场景扩展时再重构
        if ("drawPost".equals(jumpCode)) {
            JSONObject result = new JSONObject();
            result.put("url", String.format(LanlingActivityUtil.getH5BaseUrl(env) + "lottery-probability?activityCode=%s&type=%s", activityCode, type));
            return result;
        }
        return null;
    }

    @Deprecated
    public boolean updateRemoteConfigBuoyByGlobal(List<AppAdSpaceDO> appAdSpaceList) {
        if (CollectionUtils.isEmpty(appAdSpaceList)) {
            return Boolean.FALSE;
        }
        Long appId = MDCUtil.getCurAppIdByMdc();
        if (Objects.isNull(appId)) {
            return Boolean.FALSE;
        }
        // 修改上架中状态、落库
        for (AppAdSpaceDO a : appAdSpaceList) {
            a.setStatus(AdSpaceStatusEnum.put_on_ing);
        }
        log.info("debug-修改广告位落库 appAdSpaceList:{}", JSON.toJSONString(appAdSpaceList));
        appAdSpaceJpaDao.saveAll(appAdSpaceList);

        // 手动执行定时任务
        appAdSpaceAdminManager.putOnOrOffAdSpace(appId);
        return Boolean.TRUE;
    }
}
