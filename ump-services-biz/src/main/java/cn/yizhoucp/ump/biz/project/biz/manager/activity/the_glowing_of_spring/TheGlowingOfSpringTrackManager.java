package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring;

import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.starter.cassandra.base.kafka.YzKafkaProducerManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 春日灼灼埋点类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 16:40 2025/3/7
 */
@Component
public class TheGlowingOfSpringTrackManager {

    @Resource
    private YzKafkaProducerManager yzKafkaProducerManager;

    /**
     * 通用埋点_活动任务完成
     *
     * @param uid
     * @param taskType
     */
    public void allActivityTaskFinish(Long uid, String taskType) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "the_glowing_of_spring");
        params.put("attribute_type", "platform_activity");
        params.put("task_type", taskType);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_task_finish", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_ 活动领取奖励
     */
    public void allActivityReceiveAward(String awardType, String awardKey, Long awardAmount, Integer awardCount,Long uid) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type", "the_glowing_of_spring");
        params.put("attribute_type", "platform_activity");
        params.put("award_type", awardType);
        params.put("award_key", awardKey);
        params.put("award_amount", awardAmount);
        params.put("award_count", awardCount);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_receive_award", params, ServicesNameEnum.ump_services.getCode());
    }

    /**
     * 通用埋点_活动抽奖
     * @param uid
     * @param poolCode
     * @param awardKey
     * @param awardAmount
     */
    public void allActivityLottery(Long uid,String poolCode,String awardKey,Long awardAmount,Long account){
        Map<String,Object> params= Maps.newHashMapWithExpectedSize(6);
        params.put("activity_type","the_glowing_of_spring");
        params.put("attribute_type", "platform_activity");
        params.put("pool_code",poolCode);
        params.put("award_key",awardKey);
        params.put("award_amount",awardAmount);
        params.put("award_count",account);
        yzKafkaProducerManager.dataRangerTrack(ServicesAppIdEnum.lanling.getAppId(), uid, "all_activity_lottery", params, ServicesNameEnum.ump_services.getCode());
    }

}
