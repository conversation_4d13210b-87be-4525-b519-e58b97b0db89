package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.dressup.DressUpType;
import cn.yizhoucp.ms.core.base.env.DynamicEnvManager;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.EmotionalBondsRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.EmotionalBondsTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common.EmotionalBondsRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityUrlUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondRelationshipDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.domain.EmotionalBondSummaryDO;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondRelationshipService;
import cn.yizhoucp.ump.biz.project.dal.mybatis.service.EmotionalBondSummaryService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.ArrayListMultimap;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmotionalBondsAgreed implements ExecutableStrategy {
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private EmotionalBondRelationshipService emotionalBondRelationshipService;
    @Resource
    private EmotionalBondSummaryService emotionalBondSummaryService;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private EmotionalBondsRedisManager emotionalBondsRedisManager;
    @Resource
    private EmotionalBondsRankManager emotionalBondsRankManager;
    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private SendPrizeManager sendPrizeManager;

    @Resource
    private EmotionalBondsTrackManager emotionalBondsTrackManager;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private Environment environment;



    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        String bizKey = buttonEventParam.getBizKey();
        JSONObject jsonObject = JSONObject.parseObject(bizKey);
        Boolean isAgree = jsonObject.getBoolean("isAgreed");
        String invitationId = jsonObject.getString("invitationId");
        Long currentUserId = buttonEventParam.getBaseParam().getUid();
        UserVO currentUserVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), currentUserId);

        // 使用分布式锁来控制并发访问
        String lockKey = "emotional_bonds_agreed_lock:" + invitationId;
        boolean lockAcquired = false;
        try {
            lockAcquired = emotionalBondsRedisManager.tryLock(lockKey, 10, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("无法获取分布式锁，邀请ID: {}", invitationId);
                throw new ServiceException(ErrorCode.SYSTEM_ERROR, "系统繁忙，请稍后再试");
            }

            // 从Redis获取邀请信息
            Map<Object, Object> invitationData = emotionalBondsRedisManager.getInvitation(invitationId);
            if (invitationData == null || invitationData.isEmpty()) {
                log.warn("邀请记录不存在或已过期 invitationId: {}", invitationId);
                return false;
            }

            // 解析邀请数据
            Long fromUid = Long.valueOf(invitationData.get("fromUid").toString());
            Long toUid = Long.valueOf(invitationData.get("toUid").toString());
            String role = invitationData.get("role").toString();
            String otherRole = invitationData.get("otherRole").toString();
            String effectKey = invitationData.get("effectKey").toString();
            Long originalRelationshipId = Long.valueOf(invitationData.get("originalRelationshipId").toString());

            // 获取角色对信息
            EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effectKey);
            if (rolePairEnum == null) {
                log.warn("角色对信息不存在 effectKey: {}", effectKey);
                return false;
            }
            // 验证当前用户是否是被邀请方
            if (!toUid.equals(currentUserId)) {
                log.warn("当前用户不是被邀请方 currentUserId: {}, toUid: {}", currentUserId, toUid);
                return false;
            }
            //校验角色是否是特效key的角色
            if (!rolePairEnum.getRoleA().equals(role) && !rolePairEnum.getRoleB().equals(role)) {
                log.warn("角色不是特效key的角色 role: {}, effectKey: {}", role, effectKey);
                // 无论同意还是拒绝，都删除Redis中的邀请记录
                emotionalBondsRedisManager.deleteInvitation(invitationId);
                throw new ServiceException(ErrorCode.INVALID_PARAM, "邀请已过期");
            }


            // 获取发起方用户信息
            UserVO fromUserVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), fromUid);
            if (fromUserVO == null) {
                log.warn("发起方用户信息不存在 fromUid: {}", fromUid);
                return false;
            }

            if (isAgree) {
                // 查找是否存在相同effectKey且未结成关系的原始表记录
                EmotionalBondRelationshipDO existingRelationship = emotionalBondRelationshipService.getById(originalRelationshipId);
                // 如果已结成的关系则抛出异常邀请已过期
                if (existingRelationship != null && existingRelationship.getStatus() == 1) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "邀请已过期");
                }

                EmotionalBondRelationshipDO relationshipDO;
                if (existingRelationship != null) {
                    // 更新已存在的记录
                    log.info("更新已存在的关系记录 id: {}, fromUid: {}, toUid: {}", existingRelationship.getId(), fromUid, toUid);
                    relationshipDO = existingRelationship;
                    relationshipDO.setOppositeId(toUid);
                    relationshipDO.setUserRole(role);
                    relationshipDO.setOppositeRole(otherRole);
                    relationshipDO.setIsActive(1); // 设置为激活状态
                    relationshipDO.setStatus(1); // 设置为已缔结状态

                    // 设置过期时间（当前时间 + 有效期天数）
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, rolePairEnum.getExpireTime().intValue());
                    relationshipDO.setExpireTime(calendar.getTime());

                    // 更新关系记录
                    boolean updateResult = emotionalBondRelationshipService.updateById(relationshipDO);
                    if (!updateResult) {
                        log.error("更新关系记录失败 id: {}, fromUid: {}, toUid: {}", existingRelationship.getId(), fromUid, toUid);
                        return false;
                    }
                } else {
                    // 创建新的关系记录
                    log.info("创建新的关系记录 fromUid: {}, toUid: {}", fromUid, toUid);
                    relationshipDO = new EmotionalBondRelationshipDO();
                    relationshipDO.setUserId(fromUid);
                    relationshipDO.setOppositeId(toUid);
                    relationshipDO.setUserRole(role);
                    relationshipDO.setOppositeRole(otherRole);
                    relationshipDO.setEffectKey(effectKey);
                    relationshipDO.setIsActive(1); // 设置为激活状态
                    relationshipDO.setStatus(1); // 设置为已缔结状态

                    // 设置过期时间（当前时间 + 有效期天数）
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, rolePairEnum.getExpireTime().intValue());
                    relationshipDO.setExpireTime(calendar.getTime());

                    // 保存关系记录
                    boolean saveResult = emotionalBondRelationshipService.save(relationshipDO);
                    if (!saveResult) {
                        log.error("保存关系记录失败 fromUid: {}, toUid: {}", fromUid, toUid);
                        return false;
                    }
                }

                // 创建或更新合并表记录
                updateOrCreateSummaryRecord(relationshipDO);
                // 下发装扮
                sendDressUp(fromUid, toUid, rolePairEnum.name());

                // 根据礼包奖励的排行榜分数统计到排行榜中
                updateRankScore(fromUid, toUid, rolePairEnum.getRankValue());
                String url = ActivityUrlUtil.getH5BaseUrl(buttonEventParam.getBaseParam().getUnionId(), env, Boolean.TRUE, environment.getProperty(DynamicEnvManager.GLOBAL_ENV_NAME)) + "fetters";
                String msg = "恭喜你发起的「%s」羁绊关系，成功和「%s」缔结羁绊，装扮特效已经成功下发，请及时查收，<a href=\"%s\">点击查看</a>";
                // 发送通知给发起方
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        fromUid,
                        String.format(msg, rolePairEnum.getEffectName(), currentUserVO.getName(), url)
                );
                // 埋点
                emotionalBondsTrackManager.allActivityTaskFinish(fromUid, "suceessfully_finish");
                emotionalBondsTrackManager.allActivityTaskFinish(toUid, "suceessfully_finish");
            } else {
                // 拒绝邀请，发送通知给发起方
                notifyComponent.npcNotify(
                        ServicesAppIdEnum.lanling.getUnionId(),
                        fromUid,
                        String.format("你发起和「%s」的「%s」关系对方已经拒绝哦～，快去通知ta吧～", currentUserVO.getName(), rolePairEnum.getEffectName())
                );
            }

            // 删除Redis中的邀请记录
            emotionalBondsRedisManager.deleteInvitation(invitationId);

            return true;
        } finally {
            if (lockAcquired) {
                emotionalBondsRedisManager.releaseLock(lockKey);
            }
        }
    }

    private void sendDressUp(Long fromUid, Long toUid, String poolCodes) {
        //获取奖励
        ArrayListMultimap<String, DrawPoolItemDO> byPoolCodes = drawPoolItemService.getByPoolCodes(Collections.singletonList(poolCodes));
        if (ObjectUtil.isEmpty(byPoolCodes)) {
            log.error("目标奖池不存在 packageCode:{}", poolCodes);
            throw new ServiceException(ErrorCode.MISS_PARAM, "奖池列表为空");
        }
        List<DrawPoolItemDO> drawPoolItemDOList = new ArrayList<>(byPoolCodes.values());
        drawPoolItemDOList = drawPoolItemDOList.stream().filter(item -> DressUpType.entry_special_effect.getCode().equals(item.getItemSubType())).collect(Collectors.toList());
        //下发奖励
        sendPrizeManager.sendPrize(BaseParam.builder()
                        .uid(fromUid)
                        .unionId(ServicesAppIdEnum.lanling.getUnionId())
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                .build(), drawPoolItemDOList.stream().map(p -> SendPrizeDTO.of(p, null)).collect(Collectors.toList()));
        sendPrizeManager.sendPrize(BaseParam.builder()
                .uid(toUid)
                .unionId(ServicesAppIdEnum.lanling.getUnionId())
                .appId(ServicesAppIdEnum.lanling.getAppId())
                .build(), drawPoolItemDOList.stream().map(p -> SendPrizeDTO.of(p, null)).collect(Collectors.toList()));



    }


    /**
     * 更新或创建合并表中的记录
     * 根据mergedUserPair查找所有关系，匹配角色，当角色相同时在已有关系上更新过期时间
     * 过期时间为原时间叠加新增加的时间，如果没有则新建
     */
    private void updateOrCreateSummaryRecord(EmotionalBondRelationshipDO relationshipDO) {
        Long userId = relationshipDO.getUserId();
        Long oppositeId = relationshipDO.getOppositeId();
        String effectKey = relationshipDO.getEffectKey();
        String userRole = relationshipDO.getUserRole();
        String oppositeRole = relationshipDO.getOppositeRole();

        // 创建合并用户对标识（小ID_大ID）
        String mergedUserPair = AppUtil.splicUserId(userId, oppositeId);
        EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effectKey);

        // 查询该对用户的所有关系记录
        LambdaQueryWrapper<EmotionalBondSummaryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmotionalBondSummaryDO::getMergedUserPair, mergedUserPair);
        queryWrapper.gt(EmotionalBondSummaryDO::getExpireTime, new Date());

        List<EmotionalBondSummaryDO> existingSummaries = emotionalBondSummaryService.list(queryWrapper);

        log.info("查询到用户对 {} 的关系记录数量: {}", mergedUserPair, existingSummaries.size());

        // 查找角色匹配的记录
        EmotionalBondSummaryDO matchingSummary = existingSummaries.stream()
                .filter(summary -> summary.getEffectKey().equals(effectKey))
                .filter(summary -> {
                    if (summary.getUserId().equals(userId)) {
                        return summary.getUserSelectedRole().equals(userRole);
                    } else {
                        return summary.getOppositeSelectedRole().equals(userRole);
                    }
                })
                .findFirst()
                .orElse(null);

        if (matchingSummary != null) {
            // 找到匹配的记录，更新过期时间（原过期时间 + 新增时间）
            log.info("找到匹配的关系记录 id: {}, mergedUserPair: {}, effectKey: {}",
                    matchingSummary.getId(), mergedUserPair, effectKey);

            // 计算新的过期时间（原过期时间 + 新增时间的差值）
            Date originalExpireTime = matchingSummary.getExpireTime();
            Instant newExpireTime = Instant.ofEpochMilli(originalExpireTime.getTime())
                    .plus(rolePairEnum.getExpireTime(), ChronoUnit.DAYS);

            // 设置新的过期时间（原过期时间 + 新增时间）

            log.info("更新过期时间，原过期时间: {}, 新过期时间: {}", originalExpireTime, newExpireTime);

            // 更新记录
            matchingSummary.setStatus(1); // 设置为已激活状态
            matchingSummary.setBondCount(matchingSummary.getBondCount() + 1);
            matchingSummary.setLastActiveTime(new Date());
            matchingSummary.setExpireTime(Date.from(newExpireTime)); // 更新叠加后的过期时间
            emotionalBondSummaryService.updateById(matchingSummary);

            // 同时更新原始关系记录的过期时间
            relationshipDO.setExpireTime(Date.from(newExpireTime));
            emotionalBondRelationshipService.updateById(relationshipDO);
        } else {
            // 没有找到匹配的记录，创建新记录
            log.info("未找到匹配的关系记录，创建新记录 mergedUserPair: {}, effectKey: {}", mergedUserPair, effectKey);
            createNewSummaryRecord(relationshipDO, mergedUserPair);
        }
    }

    /**
     * 创建新的合并记录
     */
    private void createNewSummaryRecord(EmotionalBondRelationshipDO relationshipDO, String mergedUserPair) {
        Long userId = relationshipDO.getUserId();
        Long oppositeId = relationshipDO.getOppositeId();
        String userRole = relationshipDO.getUserRole();
        String oppositeRole = relationshipDO.getOppositeRole();
        String effectKey = relationshipDO.getEffectKey();
        EmotionalBondsEnums.RolePairEnum rolePairEnum = EmotionalBondsEnums.RolePairEnum.getRolePairEnumByName(effectKey);

        EmotionalBondSummaryDO newSummary = new EmotionalBondSummaryDO();
        // 设置过期时间
        Instant expireTime = Instant.now().plus(rolePairEnum.getExpireTime(), ChronoUnit.DAYS);

        // 确保用户ID顺序与mergedUserPair一致
        newSummary.setUserId(userId);
        newSummary.setUserSelectedRole(userRole);
        newSummary.setOppositeId(oppositeId);
        newSummary.setOppositeSelectedRole(oppositeRole);

        newSummary.setMergedUserPair(mergedUserPair);
        newSummary.setEffectKey(effectKey);
        newSummary.setBondCount(1);
        newSummary.setLastActiveTime(new Date());
        newSummary.setStatus(1); // 已激活
        newSummary.setExpireTime(Date.from(expireTime));

        emotionalBondSummaryService.save(newSummary);
    }

    /**
     * 根据礼包奖励的排行榜分数统计到排行榜中
     *
     * @param fromUid 发起方用户ID
     * @param toUid   接收方用户ID
     */
    private void updateRankScore(Long fromUid, Long toUid, Long rankValue) {
        String mergedUserPair = AppUtil.splicUserId(fromUid, toUid);
        emotionalBondsRankManager.incrRankValue(mergedUserPair, rankValue, emotionalBondsRedisManager.getRankKey());
        emotionalBondsRedisManager.incrementBindRank(fromUid,toUid,rankValue);
    }
}
