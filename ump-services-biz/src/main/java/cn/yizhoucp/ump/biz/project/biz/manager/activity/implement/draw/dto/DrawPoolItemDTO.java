package cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto;

import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeSubTypeEnum;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.jimu.draw.inner.PrizeTypeEnum;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 抽奖礼物项
 *
 * @author: lianghu
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrawPoolItemDTO {

    /** 抽中次数 */
    private Integer targetTimes;

    /** 抽中礼品 */
    private DrawPoolItemDO drawPoolItemDO;



    public PrizeItem convert2PrizeItem() {
        if (Objects.isNull(drawPoolItemDO)) {
            return PrizeItem.builder().build();
        }
        // 补充奖品类型文案
        String prizeTypeDesc = "";
        if (PrizeTypeEnum.PRIZE_DRESS.getCode().equals(drawPoolItemDO.getItemType())) {
            prizeTypeDesc = PrizeSubTypeEnum.getInstance(drawPoolItemDO.getItemSubType()).getDesc();
        } else if (StringUtils.isNotBlank(drawPoolItemDO.getItemType())) {
            prizeTypeDesc = PrizeTypeEnum.getInstance(drawPoolItemDO.getItemType()).getDesc();
        }
        return PrizeItem.builder()
                .targetTimes(targetTimes)
                .prizeKey(drawPoolItemDO.getItemKey())
                .prizeIcon(drawPoolItemDO.getItemIcon())
                .effectiveDays(drawPoolItemDO.getItemEffectiveDay())
                .prizeName(drawPoolItemDO.getItemName())
                .prizeType(drawPoolItemDO.getItemType())
                .prizeTypeDesc(prizeTypeDesc)
                .prizeSubType(drawPoolItemDO.getItemSubType())
                .valueGold(drawPoolItemDO.getItemValueGold().intValue())
                .prizeNum(drawPoolItemDO.getItemNum()).build();
    }

}
