package cn.yizhoucp.ump.biz.project.dal.jpa.dao;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.GoddessTrainRecordDO;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/29 19:45
 * @Version 1.0
 */
@Repository
public interface GoddessTrainRecodeDAO extends CrudRepository<GoddessTrainRecordDO, Long> {

    List<GoddessTrainRecordDO> findAllByAppIdAndUidAndIsGraduation(Long appId, Long uid, String isGraduation);

}
