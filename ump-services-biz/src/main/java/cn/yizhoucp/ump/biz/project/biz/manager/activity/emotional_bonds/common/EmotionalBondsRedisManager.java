package cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.common;

import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.emotional_bonds.vo.CoupleEffectVO;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityRedisKeyGenerator;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 天生羁绊redis管理类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 18:18 2025/4/22
 */
@Component
@Slf4j
public class EmotionalBondsRedisManager {
    @Resource
    private RedisManager redisManger;
    @Resource
    private ActivityRedisKeyGenerator activityRedisKeyGenerator;
    @Resource
    private UserFeignManager userFeignManager;

    /**
     * 获取Redis管理器
     * @return Redis管理器
     */
    public RedisManager getRedisManger() {
        return redisManger;
    }

    public List<CoupleEffectVO> getCoupleEffectsCache(){
        String cacheKey = activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.COUPLE_EFFECTS_CACHE_KEY,EmotionalBondsConstant.ACTIVITY_CODE);
        String cacheData = redisManger.getString(cacheKey);
        if (CharSequenceUtil.isBlank(cacheData)) {
            return null;
        }
        try {
            // 从缓存中解析数据
            log.debug("从缓存中获取情侣特效列表");
            return JSON.parseArray(cacheData, CoupleEffectVO.class);
        } catch (Exception e) {
            log.error("解析情侣特效列表缓存数据失败", e);
            // 解析失败，删除缓存
            redisManger.delete(cacheKey);
        }

        return null;
    }

    // 邀请记录的Redis键前缀
    private static final String INVITATION_KEY_PREFIX = "ump:activity:%s:invitation_record:%s";
    // Redis记录过期时间（秒）
    private static final int INVITATION_EXPIRE_SECONDS = 7 * 24 * 60 * 60; // 7天

    /**
     * 获取排行榜key，每周更新
     * @return 当前周的排行榜key
     */
    public String getRankKey() {
        return getWeeklyRankKey(0); // 当前周，偏移量为0
    }

    /**
     * 获取上一周的排行榜key
     * @return 上一周的排行榜key
     */
    public String getLastWeekRankKey() {
        return getWeeklyRankKey(-1); // 上一周，偏移量为-1
    }

    /**
     * 获取指定周偏移量的排行榜key
     * @param weekOffset 周偏移量，0表示当前周，-1表示上一周，1表示下一周
     * @return 指定周的排行榜key
     */
    private String getWeeklyRankKey(int weekOffset) {
        // 获取当前日期所在的周数
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为周一
        calendar.setMinimalDaysInFirstWeek(4); // 设置最小天数，确保第一周正确计算

        // 如果有偏移量，调整日期
        if (weekOffset != 0) {
            calendar.add(Calendar.WEEK_OF_YEAR, weekOffset);
        }

        int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR); // 获取指定周是一年中的第几周


        // 添加周标记
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.RANK_KEY,
                EmotionalBondsConstant.ACTIVITY_CODE,String.valueOf(weekOfYear));
    }

    /**
     * 生成邀请记录ID
     * @return 唯一的邀请记录ID
     */
    public String generateInvitationId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取邀请记录的Redis键
     * @param invitationId 邀请记录ID
     * @return Redis键
     */
    private String getInvitationKey(String invitationId) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                INVITATION_KEY_PREFIX,
                EmotionalBondsConstant.ACTIVITY_CODE,
                invitationId);
    }

    /**
     * 保存邀请记录到Redis
     * @param invitationId 邀请记录ID
     * @param fromUid 发起方用户ID
     * @param toUid 接收方用户ID
     * @param role 发起方选择的角色
     * @param otherRole 接收方对应的角色
     * @param effectKey 关系特效标识
     * @param originalRelationshipId 原始关系表ID，可为null
     * @return 是否保存成功
     */
    public boolean saveInvitation(String invitationId, Long fromUid, Long toUid, String role, String otherRole, String effectKey, Long originalRelationshipId) {
        String key = getInvitationKey(invitationId);
        Map<Object, Object> invitationData = new HashMap<>();
        invitationData.put("fromUid", fromUid.toString());
        invitationData.put("toUid", toUid.toString());
        invitationData.put("role", role);
        invitationData.put("otherRole", otherRole);
        invitationData.put("effectKey", effectKey);
        invitationData.put("createTime", String.valueOf(System.currentTimeMillis()));
        if (originalRelationshipId != null) {
            invitationData.put("originalRelationshipId", originalRelationshipId.toString());
        }

        return redisManger.hmset(key, invitationData, INVITATION_EXPIRE_SECONDS);
    }

    /**
     * 保存邀请记录到Redis (兼容旧版本)
     * @param invitationId 邀请记录ID
     * @param fromUid 发起方用户ID
     * @param toUid 接收方用户ID
     * @param role 发起方选择的角色
     * @param otherRole 接收方对应的角色
     * @param effectKey 关系特效标识
     * @return 是否保存成功
     */
    public boolean saveInvitation(String invitationId, Long fromUid, Long toUid, String role, String otherRole, String effectKey) {
        return saveInvitation(invitationId, fromUid, toUid, role, otherRole, effectKey, null);
    }

    /**
     * 获取邀请记录
     * @param invitationId 邀请记录ID
     * @return 邀请记录数据
     */
    public Map<Object, Object> getInvitation(String invitationId) {
        String key = getInvitationKey(invitationId);
        return redisManger.hmget(key);
    }

    /**
     * 删除邀请记录
     * @param invitationId 邀请记录ID
     * @return 是否删除成功
     */
    public void deleteInvitation(String invitationId) {
        String key = getInvitationKey(invitationId);
         redisManger.delete(key);
    }

    /**
     * 获取装备关系的Redis键
     * @param uid 用户ID
     * @return Redis键
     */
    private String getEquippedRelationshipKey(Long uid) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.EQUIPPED_RELATIONSHIP_KEY, EmotionalBondsConstant.ACTIVITY_CODE,uid.toString());
    }

    /**
     * 设置用户当前装备的关系 ID
     * @param uid 用户ID
     * @param relationshipId 关系 ID
     * @return 是否设置成功
     */
    public boolean setEquippedRelationship(Long uid, Long relationshipId) {
        String key = getEquippedRelationshipKey(uid);
        return redisManger.set(key, relationshipId.toString(), DateUtil.ONE_MONTH_SECOND); // 30天过期
    }

    /**
     * 获取用户当前装备的关系 ID
     * @param uid 用户ID
     * @return 关系 ID，如果没有装备则返回 null
     */
    public Long getEquippedRelationship(Long uid) {
        String key = getEquippedRelationshipKey(uid);
        String value = redisManger.getString(key);
        return value != null ? Long.valueOf(value) : null;
    }

    /**
     * 清除用户当前装备的关系
     * @param uid 用户ID
     */
    public void clearEquippedRelationship(Long uid) {
        String key = getEquippedRelationshipKey(uid);
        redisManger.delete(key);
    }

    /**
     * 获取关系过期提醒的Redis键
     * @param relationshipId 关系ID
     * @return Redis键
     */
    private String getExpirationReminderKey(Long relationshipId) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.EXPIRATION_REMINDER_KEY, getActivityCode(),relationshipId.toString());
    }

    /**
     * 标记用户已收到关系过期提醒
     * @param relationshipId 关系ID
     * @param uid 用户ID
     * @return 是否设置成功
     */
    public void markUserReminded(Long relationshipId, Long uid) {
        String key = getExpirationReminderKey(relationshipId);
        redisManger.sSetExpire(key, DateUtil.ONE_WEEK_SECONDS,uid.toString()); // 7天过期
    }

    /**
     * 检查用户是否已收到关系过期提醒
     * @param relationshipId 关系ID
     * @param uid 用户ID
     * @return 是否已提醒
     */
    public boolean isUserReminded(Long relationshipId, Long uid) {
        String key = getExpirationReminderKey(relationshipId);
        return redisManger.setIsMember(key, uid.toString());
    }

    public void setCoupleEffectsCache(List<CoupleEffectVO> result) {
        String cacheKey = activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.COUPLE_EFFECTS_CACHE_KEY,EmotionalBondsConstant.ACTIVITY_CODE);
        redisManger.set(cacheKey,JSON.toJSONString(result),DateUtil.ONE_DAY_SECOND);
    }

    /**
     * 获取单个情侣特效缓存的Redis键
     * @param uid 用户ID
     * @param effectCode 特效代码
     * @return Redis键
     */
    private String getCoupleEffectCacheKey(Long uid, String effectCode) {
        return activityRedisKeyGenerator.generateKeyWithActivityStartTime(
                EmotionalBondsConstant.COUPLE_EFFECT_CACHE_KEY,
                EmotionalBondsConstant.ACTIVITY_CODE,
                uid.toString(),
                effectCode);
    }

    /**
     * 缓存单个用户的情侣特效
     * @param uid 用户ID
     * @param effectCode 特效代码
     * @param effect 情侣特效VO对象
     * @param expireTime 过期时间（可选）
     * @return 是否缓存成功
     */
    public boolean setCoupleEffectCache(Long uid, String effectCode, CoupleEffectVO effect, Date expireTime) {
        String cacheKey = getCoupleEffectCacheKey(uid, effectCode);

        // 计算缓存过期时间
        long expireSeconds;
        if (expireTime != null) {
            // 如果有关系过期时间，则缓存到关系过期时间或最多1小时
            long expireMillis = expireTime.getTime() - System.currentTimeMillis();
            // 如果关系已过期或即将过期，不缓存
            if (expireMillis <= 0) {
                return false;
            }
            // 取关系过期时间和1小时中的较小值
            expireSeconds = Math.min(TimeUnit.MILLISECONDS.toSeconds(expireMillis), EmotionalBondsConstant.CACHE_EXPIRE_SECONDS);
        } else {
            // 默认缓存1小时
            expireSeconds = EmotionalBondsConstant.CACHE_EXPIRE_SECONDS;
        }

        try {
            redisManger.set(cacheKey, JSON.toJSONString(effect), (int) expireSeconds);
            log.debug("已缓存用户{}的情侣特效{}，过期时间：{}秒", uid, effectCode, expireSeconds);
            return true;
        } catch (Exception e) {
            log.error("缓存用户{}的情侣特效{}失败", uid, effectCode, e);
            return false;
        }
    }

    /**
     * 获取用户的情侣特效缓存
     * @param uid 用户ID
     * @param effectCode 特效代码
     * @return 情侣特效VO对象，如果缓存不存在或已过期则返回null
     */
    public CoupleEffectVO getCoupleEffectCache(Long uid, String effectCode) {
        String cacheKey = getCoupleEffectCacheKey(uid, effectCode);
        String cacheData = redisManger.getString(cacheKey);

        if (CharSequenceUtil.isBlank(cacheData)) {
            return null;
        }

        try {
            log.debug("从缓存中获取用户{}的情侣特效{}", uid, effectCode);
            return JSON.parseObject(cacheData, CoupleEffectVO.class);
        } catch (Exception e) {
            log.error("解析用户{}的情侣特效{}缓存数据失败", uid, effectCode, e);
            // 解析失败，删除缓存
            redisManger.delete(cacheKey);
            return null;
        }
    }

    /**
     * 清除用户的情侣特效缓存
     * @param uid 用户ID
     * @param effectCode 特效代码
     */
    public void clearCoupleEffectCache(Long uid, String effectCode) {
        String cacheKey = getCoupleEffectCacheKey(uid, effectCode);
        redisManger.delete(cacheKey);
        log.debug("已清除用户{}的情侣特效{}缓存", uid, effectCode);
    }

    public Long getTopPairedFriend(Long uid) {
        String key = activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.BIND_FRIEND_RANK_KEY, getActivityCode(), uid.toString());
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManger.reverseRangeByScoreWithScores(key, 0, Double.MAX_VALUE, 0, 10);
        if (typedTuples == null || typedTuples.isEmpty()) {
            return null;
        }

        for (ZSetOperations.TypedTuple<Object> tuple : typedTuples) {
            Long friendId = Long.valueOf(Objects.requireNonNull(tuple.getValue()).toString());
            if (!isUserBanned(friendId)) {
                return friendId;
            }
        }
        return null;
    }

    public void incrementBindRank(Long uid, Long toUid, Long num) {
        String userKey = activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.BIND_FRIEND_RANK_KEY, getActivityCode(), uid.toString());
        redisManger.zIncrby(userKey, toUid.toString(), num.doubleValue(), DateUtil.ONE_MONTH_SECOND);
        String friendKey = activityRedisKeyGenerator.generateKeyWithActivityStartTime(EmotionalBondsConstant.BIND_FRIEND_RANK_KEY, getActivityCode(), toUid.toString());
        redisManger.zIncrby(friendKey, uid.toString(), num.doubleValue(), DateUtil.ONE_MONTH_SECOND);
    }

    private boolean isUserBanned(Long userId) {
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), userId);
        return userVO == null;
    }

    private String getActivityCode() {
        return EmotionalBondsConstant.ACTIVITY_CODE;
    }

    public Long getRankLength() {
        try {
            return Optional.ofNullable(redisManger.zSetGetSize(getRankKey())).orElse(0L);
        } catch (Exception e) {
            log.error("获取排行榜长度失败", e);
        }
        return 0L;
    }
}
