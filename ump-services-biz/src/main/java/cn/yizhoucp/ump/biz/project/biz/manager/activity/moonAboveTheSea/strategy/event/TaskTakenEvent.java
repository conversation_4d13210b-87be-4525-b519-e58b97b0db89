package cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.strategy.event;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.MoonAboveTheSeaTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.common.MoonAboveTheSeaRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.moonAboveTheSea.strategy.MoonAboveTheSeaEventStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TaskTakenEvent implements MoonAboveTheSeaEventStrategy {

    @Resource
    private MoonAboveTheSeaRedisManager moonAboveTheSeaRedisManager;
    @Resource
    private MoonAboveTheSeaTrackManager moonAboveTheSeaTrackManager;

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        // 任务领取事件
        BaseParam baseParam = buttonEventParam.getBaseParam();
        Long uid = baseParam.getUid();
        String taskCode = buttonEventParam.getBizKey();
        MoonAboveTheSeaEnums.TaskEnum taskEnum = MoonAboveTheSeaEnums.TaskEnum.getByTaskCode(taskCode);
        if (taskEnum == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "任务不存在");
        }
        // 任务领取逻辑
        moonAboveTheSeaRedisManager.setTaskClaimed(uid, taskCode);
        //领取任务埋点
        if (moonAboveTheSeaRedisManager.clickTaskBtnLock(uid)) {
            moonAboveTheSeaTrackManager.allActivityBtnClick(uid);
        }
        return Boolean.TRUE;
    }
}
