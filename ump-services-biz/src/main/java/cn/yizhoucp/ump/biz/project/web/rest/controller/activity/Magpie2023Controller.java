package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.magpie2023.IndexVO;
import cn.yizhoucp.ump.api.vo.activity.magpie2023.LoveDrawPoolVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawLogVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.magpie2023.Magpie2023PageManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class Magpie2023Controller {

    @Resource
    private Magpie2023PageManager magpie2023PageManager;

    @RequestMapping("/api/inner/activity/magpie2023/get-index")
    public Result<IndexVO> getIndexInfo(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.getIndexInfo(param, toUid));
    }

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/exchange-draw-time")
    public Result<Long> exchangeDrawTime(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.exchangeDrawTime(param, toUid));
    }

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/draw-log")
    public Result<DrawLogVO> lovePoolDrawLog(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.lovePoolDrawLog(param));
    }

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/refresh")
    public Result<LoveDrawPoolVO> lovePoolRefresh(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.lovePoolRefresh(param, toUid));
    }

    @RequestMapping("/api/inner/activity/magpie2023/love-pool/draw")
    public Result<DrawReturn> lovePoolDraw(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.lovePoolDraw(param, toUid));
    }

    @RequestMapping("/api/inner/activity/magpie2023/xsms-pool/draw")
    public Result<DrawReturn> xsmsPoolDraw(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.xsmsPoolDraw(param));
    }

    @RequestMapping("/api/inner/activity/magpie2023/couple-board/hide")
    public Result<Boolean> coupleBoardHide(BaseParam param, Long toUid, Boolean hide) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> magpie2023PageManager.coupleBoardHide(param, toUid, hide));
    }

}
