package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starWishCardCollectionGame;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawEvent;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarWishCardCollectionGameConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarWishCardCollectionGameConstant.ASTROLOGY_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarWishCardCollectionGameConstant.REPLACE_GIFT;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.StarWishCardCollectionGameConstant.STAR_WISH_BOARD;

@Service
public class StarWishCardCollectionGameBizManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private StarWishCardCollectionGameTrackManager starWishCardCollectionGameTrackManager;
    @Resource
    private StarWishCardCollectionGameRankManager starWishCardCollectionGameRankManager;

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
//    @EventListener
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void onDraw(AstrologyDrawEvent event) {
        AstrologyDrawMessage drawMessage = event.getSource();

        Long astrologyTimes = redisManager.incrLong(
                String.format(ASTROLOGY_TIMES, drawMessage.getUid(), DateUtil.getNowYyyyMMdd()),
                drawMessage.getConsumeTimes(),
                DateUtil.ONE_MONTH_SECOND);

        starWishCardCollectionGameRankManager.incrRankValue(drawMessage.getUid(), Long.valueOf(drawMessage.getConsumeTimes()), String.format(STAR_WISH_BOARD, DateUtil.getNowYyyyMMdd()));

        if (astrologyTimes >= 50 && astrologyTimes - drawMessage.getConsumeTimes() < 50) {
            starWishCardCollectionGameTrackManager.allActivityTaskFinish("star_wish_value_50", drawMessage.getUid());
        } else if (astrologyTimes >= 299 && astrologyTimes - drawMessage.getConsumeTimes() < 299) {
            starWishCardCollectionGameTrackManager.allActivityTaskFinish("star_wish_value_299", drawMessage.getUid());
        } else if (astrologyTimes >= 1314 && astrologyTimes - drawMessage.getConsumeTimes() < 1314) {
            starWishCardCollectionGameTrackManager.allActivityTaskFinish("star_wish_value_1314", drawMessage.getUid());
        } else if (astrologyTimes >= 2588 && astrologyTimes - drawMessage.getConsumeTimes() < 2588) {
            starWishCardCollectionGameTrackManager.allActivityTaskFinish("star_wish_value_2588", drawMessage.getUid());
        } else if (astrologyTimes >= 5999 && astrologyTimes - drawMessage.getConsumeTimes() < 5999) {
            starWishCardCollectionGameTrackManager.allActivityTaskFinish("star_wish_value_5999", drawMessage.getUid());
        } else if (astrologyTimes >= 9999 && astrologyTimes - drawMessage.getConsumeTimes() < 9999) {
            starWishCardCollectionGameTrackManager.allActivityTaskFinish("astrology_9999", drawMessage.getUid());
        }
    }

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    public Map<Long, PrizeItem> getReplaceGiftMap(Long uid) {
        Map<Object, Object> hmget = redisManager.hmget(String.format(REPLACE_GIFT, uid));
        if (MapUtils.isEmpty(hmget)) {
            return Collections.emptyMap();
        }

        Map<Long, PrizeItem> replaceGiftMap = Maps.newHashMapWithExpectedSize(hmget.size());
        for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
            replaceGiftMap.put(Long.parseLong(String.valueOf(entry.getKey())), JSON.parseObject(String.valueOf(entry.getValue()), PrizeItem.class));
        }

        return replaceGiftMap;
    }

}
