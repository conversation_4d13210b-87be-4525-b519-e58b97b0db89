package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.hutool.core.text.CharSequenceUtil;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025Enums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.common.MainChineseNewYear2025RedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo.LuckToMyHomeVO;
import cn.yizhoucp.user.manager.UserFeignManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class LuckToMyHomeStrategy implements MainChineseNewYear2025Strategy {

    @Resource
    private MainChineseNewYear2025RedisManager mainChineseNewYear2025RedisManager;
    @Resource
    private UserFeignManager userFeignManager;

    @Override
    public LuckToMyHomeVO execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        Long otherId = buttonEventParam.getOtherId();
        if (otherId == null) {
            otherId = getPairedFriends(uid);
        } else {
            Long topPairedFriend = mainChineseNewYear2025RedisManager.getTopPairedFriend(uid);
            if (topPairedFriend == null) {
                mainChineseNewYear2025RedisManager.initBindPairFriends(uid, otherId);
                mainChineseNewYear2025RedisManager.getTopPairedFriend(uid);
            }
        }
        String bindId = AppUtil.splicUserId(uid, otherId);
        String currentLevel = mainChineseNewYear2025RedisManager.getCurrentLevel(bindId);
        MainChineseNewYear2025Enums.NodeEnum currentNode = MainChineseNewYear2025Enums.NodeEnum.getByNodeCode(currentLevel);
        if (currentNode == null) {
            currentNode = MainChineseNewYear2025Enums.NodeEnum.NODE_0;
        }
        return LuckToMyHomeVO.builder()
                .pairedFriends(buildPairedFriends(uid, otherId))
                .rewardNodes(buildRewardNodes(bindId, uid))
                .wateringStatus(buildWateringStatus(bindId, uid, currentNode))
                .isUpgradeAnimationShown(getIsUpgradeAnimationShown(bindId, uid, currentNode))
                .build();
    }

    private Boolean getIsUpgradeAnimationShown(String bindId, Long uid, MainChineseNewYear2025Enums.NodeEnum currentNode) {
        return mainChineseNewYear2025RedisManager.getIsUpgradeAnimationShown(bindId, uid, currentNode.getNodeCode());
    }

    public Long getPairedFriends(Long uid) {
        return mainChineseNewYear2025RedisManager.getTopPairedFriend(uid);
    }

    private LuckToMyHomeVO.WateringStatus buildWateringStatus(String bindId, Long uid, MainChineseNewYear2025Enums.NodeEnum currentNode) {
        if (currentNode == null) {
            return LuckToMyHomeVO.WateringStatus.builder().build();
        }
        Long wateringProgress = mainChineseNewYear2025RedisManager.getWateringNumByUid(bindId, uid, currentNode.getNodeCode());
        return LuckToMyHomeVO.WateringStatus.builder()
                .currentWealth(mainChineseNewYear2025RedisManager.getCurrentWealth(bindId))
                .maxWatering(currentNode.getWateringRequired())
                .level(currentNode.getLevel())
                .wateringProgress(wateringProgress.intValue())
                .wateringCost(currentNode.getWateringCost())
                .build();
    }

    private List<LuckToMyHomeVO.RewardNodeVO> buildRewardNodes(String bindId, Long uid) {
        return Arrays.stream(MainChineseNewYear2025Enums.NodeEnum.values())
                .filter(MainChineseNewYear2025Enums.NodeEnum::getIsRewardNode)
                .map(enumItem -> createRewardNode(enumItem, bindId, uid))
                .collect(Collectors.toList());
    }

    private LuckToMyHomeVO.RewardNodeVO createRewardNode(MainChineseNewYear2025Enums.NodeEnum enumItem, String bindId, Long uid) {
        return LuckToMyHomeVO.RewardNodeVO.builder()
                .wateringRequired(enumItem.getWateringRequired())
                .rewardCardKey(enumItem.getNodeCode())
                .status(getRewardNodeStatus(enumItem, bindId))
                .build();
    }

    private Integer getRewardNodeStatus(MainChineseNewYear2025Enums.NodeEnum enumItem, String bindId) {
        if (CharSequenceUtil.isBlank(bindId)) {
            return MainChineseNewYear2025Enums.RewardNodeStatusEnum.NOT_START.getStatus();
        }
        String[] ids = bindId.split("_");
        if (ids.length != 2 || ids[0].isEmpty() || ids[1].isEmpty()) {
            return MainChineseNewYear2025Enums.RewardNodeStatusEnum.NOT_START.getStatus();
        }
        Long userWateringNum = mainChineseNewYear2025RedisManager.getWateringNumByUid(bindId, Long.valueOf(ids[0]), enumItem.getNodeCode());
        Long otherUserWateringNum = mainChineseNewYear2025RedisManager.getWateringNumByUid(bindId, Long.valueOf(ids[1]), enumItem.getNodeCode());
        if (userWateringNum < enumItem.getWateringRequired() || otherUserWateringNum < enumItem.getWateringRequired()) {
            return MainChineseNewYear2025Enums.RewardNodeStatusEnum.NOT_START.getStatus();
        }
        Boolean isReceived = mainChineseNewYear2025RedisManager.getRewardNodeStatus(bindId, enumItem.getNodeCode());
        if (Boolean.FALSE.equals(isReceived)) {
            return MainChineseNewYear2025Enums.RewardNodeStatusEnum.CAN_GET.getStatus();
        }
        return MainChineseNewYear2025Enums.RewardNodeStatusEnum.ALREADY_GET.getStatus();
    }

    private LuckToMyHomeVO.PairedFriendsVO buildPairedFriends(Long uid, Long otherId) {
        final Long appId = ServicesAppIdEnum.lanling.getAppId();

        LuckToMyHomeVO.PairedUserVO currentUser = Optional.ofNullable(uid)
                .map(id -> createPairedUser(appId, id))
                .orElse(null);

        LuckToMyHomeVO.PairedUserVO friend = Optional.ofNullable(otherId)
                .map(id -> createPairedUser(appId, id))
                .orElse(null);

        return LuckToMyHomeVO.PairedFriendsVO.builder()
                .currentUser(currentUser)
                .friend(friend)
                .build();
    }

    private LuckToMyHomeVO.PairedUserVO createPairedUser(Long appId, Long userId) {
        UserVO user = userFeignManager.getBasicWithCache(appId, userId);
        return LuckToMyHomeVO.PairedUserVO.builder()
                .userId(user.getId())
                .userName(user.getName())
                .sex(user.getSex().getCode())
                .avatar(user.getAvatar())
                .build();
    }
}
