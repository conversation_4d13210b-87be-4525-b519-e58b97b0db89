package cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.CommonUserVO;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.ChineseNewYear2025.main.vo.LuckToMyHomeVO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.user.manager.UserFeignManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FriendListStrategy implements MainChineseNewYear2025Strategy {

    @Resource
    private FeignLanlingService feignLanlingService;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private DepthFeignService depthFeignService;


    private Object getFriendList() {
        SecurityUser user = SecurityUtils.getCurrentUser();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId())) {
            log.warn("SecurityUtils getCurrentUser is null");
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }
        Long uid = user.getUserId();
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        if (Objects.isNull(userVO) || Objects.isNull(userVO.getSex())) {
            return null;
        }

        Long start = 0L;
        Integer num = 100;
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
        PageVO<WrapUserVO> subFriendList = feignLanlingService.getSubscribeList(start, num).getData();
        PageVO<WrapUserVO> fansList = feignLanlingService.getFansList(start, num).getData();
        List<WrapUserVO> closeFriends = getCloseFriends(uid);

        if (Objects.isNull(friendList) && Objects.isNull(subFriendList) && Objects.isNull(fansList)) {
            return Collections.emptyList();
        }

        List<WrapUserVO> list = new ArrayList<>();
        if (friendList != null) {
            list.addAll(friendList.getList());
        }
        if (subFriendList != null) {
            list.addAll(subFriendList.getList());
        }
        if (fansList != null) {
            list.addAll(fansList.getList());
        }
        if (closeFriends != null) {
            list.addAll(closeFriends);
        }
        Set<Long> userIds = new HashSet<>();
        log.info("获取好友列表：{}", list);
        return list.stream()
                .filter(wrapUserVO -> {
                    CommonUserVO temp = wrapUserVO.getUser();
                    if (temp == null || ObjectUtil.equals(userVO.getSex().getCode(), temp.getSex())) {
                        return false;
                    }
                    return userIds.add(temp.getId());
                })
                .limit(50)
                .map(wrapUserVO -> {
                    CommonUserVO item = wrapUserVO.getUser();
                    return LuckToMyHomeVO.PairedUserVO.builder()
                            .userId(item.getId())
                            .userName(item.getName())
                            .avatar(item.getAvatar())
                            .build();
                })
                .collect(Collectors.toList());

    }

    public List<WrapUserVO> getCloseFriends(Long uid) {
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(MDCUtil.getCurUserIdByMdc()).successData();
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return null;
        }
        List<WrapUserVO> list = new ArrayList<>();
        allUserDepth.forEach((k, v) -> {
            UserVO userVO = userFeignManager.getBasicWithCache(MDCUtil.getCurAppIdByMdc(), k);
            if (userVO != null && userVO.getSex() != null) {
                CommonUserVO commonUserVO = new CommonUserVO();
                commonUserVO.setId(userVO.getId());
                commonUserVO.setName(userVO.getName());
                commonUserVO.setAvatar(userVO.getAvatar());
                commonUserVO.setSex(userVO.getSex().getCode());
                WrapUserVO wrapUserVO = new WrapUserVO();
                wrapUserVO.setUser(commonUserVO);
                list.add(wrapUserVO);
            }
        });

        return list;
    }

    @Override
    public Object execute(ButtonEventParam buttonEventParam) {
        return getFriendList();
    }
}
