package cn.yizhoucp.ump.biz.common.security.token;

import cn.yizhoucp.ump.biz.common.base.config.JHipsterProperties;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.UserAuthentication;
import com.alibaba.fastjson.JSON;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

@Component
public class TokenProvider {

    private final Logger        log             = LoggerFactory.getLogger(TokenProvider.class);

    private static final String AUTHORITIES_KEY = "auth";

    private static final String LOGIN_TYPE_KEY  = "login_type";

    private String              secretKey;

    private long                tokenValidityInSeconds;

    private long                tokenValidityInSecondsForRememberMe;

    @Inject
    private JHipsterProperties jHipsterProperties;

    @PostConstruct
    public void init() {
        this.secretKey = jHipsterProperties.getSecurity().getAuthentication().getJwt().getSecret();

        this.tokenValidityInSeconds = 1000 * jHipsterProperties.getSecurity().getAuthentication()
            .getJwt().getTokenValidityInSeconds();
        this.tokenValidityInSecondsForRememberMe = 1000 * jHipsterProperties.getSecurity()
            .getAuthentication().getJwt().getTokenValidityInSecondsForRememberMe();
    }



    public Authentication getAuthentication(String token) {

        Claims claims = Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody();


        SecurityUser user = JSON.parseObject(claims.get(Claims.SUBJECT).toString(),
            SecurityUser.class);



        UserAuthentication authentication = new UserAuthentication(user);

        return authentication;
    }

    public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(secretKey).parseClaimsJws(authToken);
            return true;
        } catch (Exception e) {
            log.info("Invalid JWT signature: " + e.getMessage());
            return false;
        }
    }
}
