package cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.strategy;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.param.jimu.ButtonEventParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.vo.LoveInBloomFestivalIndexVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInBloomFestival.vo.LoveInBloomFestivalRewardVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.TheGlowingOfSpringTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.the_glowing_of_spring.common.TheGlowingOfSpringRedisManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.ExecutableStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class TheGlowingOfSpringRedeem implements ExecutableStrategy {
    @Resource
    private TheGlowingOfSpringRedisManager theGlowingOfSpringRedisManager;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private TheGlowingOfSpringTrackManager theGlowingOfSpringTrackManager;

    @Override
    @NoRepeatSubmit(time = 3)
    public Object execute(ButtonEventParam buttonEventParam) {
        Long uid = buttonEventParam.getBaseParam().getUid();
        String bizKey = buttonEventParam.getBizKey();
        ScenePrizeDO scenePrizeDO = theGlowingOfSpringRedisManager.getScenePrize(bizKey);
        if (scenePrizeDO == null) {
            return Boolean.FALSE;
        }
        Long requireCount = getRequireCount(scenePrizeDO.getExtData());
        Long currentCount = theGlowingOfSpringRedisManager.getItemCount(uid);
        if (currentCount < requireCount) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "桃花仙不足哦");
        }
        theGlowingOfSpringRedisManager.decrementItemCount(uid, requireCount);
        //下发奖品
        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(ServicesAppIdEnum.lanling.getAppId()).unionId(ServicesAppIdEnum.lanling.getUnionId()).uid(uid).build(),
                Stream.of(scenePrizeDO).map(scenePrize -> SendPrizeDTO.of(scenePrize, uid)).collect(Collectors.toList())
        );
        //记录日志
        recordLog(scenePrizeDO);
        //下发消息
        String msg = "恭喜在“春日灼灼”活动中，成功使用%s桃花仙兑换%s礼物一个，礼物已经下发至背包，快去查看吧～";
        notifyComponent.npcNotify(
                ServicesAppIdEnum.lanling.getUnionId(),
                uid,
                String.format(msg, requireCount, scenePrizeDO.getPrizeDesc())
        );
        //埋点
        theGlowingOfSpringTrackManager.allActivityReceiveAward("spring_store", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), 1, uid);
        return LoveInBloomFestivalRewardVO.builder()
                .rewardList(Stream.of(scenePrizeDO).map(item -> LoveInBloomFestivalIndexVO.GiftVO.builder()
                        .giftName(item.getPrizeDesc())
                        .giftValue(item.getPrizeValueGold())
                        .giftIcon(item.getPrizeIcon())
                        .build()).collect(Collectors.toList()))
                .build();

    }

    private void recordLog(ScenePrizeDO scenePrizeDO) {
        DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                .itemName(String.format("兑换%s*1", scenePrizeDO.getPrizeDesc()))
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .itemKey(scenePrizeDO.getPrizeValue())
                .itemNum(1)
                .itemValueGold(scenePrizeDO.getPrizeValueGold())
                .status(1)
                .poolCode(TheGlowingOfSpringConstant.REDEEM_LOG)
                .build();
        DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                .targetTimes(1)
                .drawPoolItemDO(drawPoolItemDO).build();
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        prizeItemList.add(drawPoolItemDTO);
        logComponent.putDrawLog(BaseParam.ofMDC(), TheGlowingOfSpringConstant.ACTIVITY_CODE, prizeItemList);
    }

    private Long getRequireCount(String extData) {
        JSONObject jsonObject = JSON.parseObject(extData);
        if (jsonObject.containsKey("required")) {
            return jsonObject.getLong("required");
        }
        return 0L;
    }
}
