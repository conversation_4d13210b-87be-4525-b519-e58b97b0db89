package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RestBusinessTemplate;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.IncomeCallWarnVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.RomanticTourVO;
import cn.yizhoucp.ump.api.vo.activity.labourDay2024.UrgentLoveVO;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.labourDay2024.LabourDay2024DrawManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.labourDay2024.LabourDay2024IndexManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.LabourDay2024Constant.ACTIVITY_CODE;

@RestController
public class LabourDay2024Controller {

    @Resource
    private LabourDay2024DrawManager labourDay2024DrawManager;
    @Resource
    private LabourDay2024IndexManager labourDay2024IndexManager;

    @GetMapping("/api/inner/activity/labour_day_2024/draw")
    public Result<DrawReturn> draw(String type, String poolCode, Integer times, String extValue) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024DrawManager.draw(DrawParam.builder()
                .unionId(MDCUtil.getCurUnionIdByMdc())
                .appId(MDCUtil.getCurAppIdByMdc())
                .uid(MDCUtil.getCurUserIdByMdc())
                .activityCode(ACTIVITY_CODE)
                .type(type)
                .poolCode(poolCode)
                .times(times)
                .extValue(extValue).build()));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/love_confess/send")
    public Result<Boolean> send(BaseParam param, Long toUid, String text) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.send(param, toUid, text));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/romantic_tour/choose_close_friend")
    public Result<RomanticTourVO> romanticTourChooseCloseFriend(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.romanticTourChooseCloseFriend(param, toUid, null));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/romantic_tour/receive")
    public Result<RomanticTourVO> receive(BaseParam param, Long toUid, Integer puzzleIndex, Integer ticketIndex, Boolean headFrame) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.receive(param, toUid, puzzleIndex, ticketIndex, headFrame));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/urgent_love/give-gift")
    public Result<Long> giveGift(BaseParam param, Long toUid, Integer productCount) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.giveGift(param, toUid, productCount));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/urgent_love/choose_close_friend")
    public Result<IncomeCallWarnVO> urgentLoveChooseCloseFriend(BaseParam param, Long toUid) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.urgentLoveChooseCloseFriend(param, toUid));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/urgent_love/flip")
    public Result<UrgentLoveVO> flip(BaseParam param) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.flip(param));
    }

    @GetMapping("/api/inner/activity/labour_day_2024/income_call_warn/great_move")
    public Result<IncomeCallWarnVO> greatMove(BaseParam param, Long toUid, Integer index) {
        return RestBusinessTemplate.executeWithoutTransaction(() -> labourDay2024IndexManager.greatMove(param, toUid, index));
    }

}
