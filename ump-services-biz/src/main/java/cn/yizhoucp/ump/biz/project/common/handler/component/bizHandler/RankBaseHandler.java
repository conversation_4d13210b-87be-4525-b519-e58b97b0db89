package cn.yizhoucp.ump.biz.project.common.handler.component.bizHandler;

import cn.yizhoucp.ump.api.vo.jimu.rank.CpRankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.common.handler.BaseHandler;
import cn.yizhoucp.ump.biz.project.common.handler.HandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

import static cn.yizhoucp.ump.biz.project.biz.util.NumberUtil.convertToStr;

/**
 * <AUTHOR>
 * @Date 2023/5/10 17:33
 * @Version 1.0
 */
@Slf4j
@Component("rankBaseHandler")
public class RankBaseHandler extends BaseHandler {
    @Override
    protected void doHandle(HandlerContext context) {
        dealWithDecimalRankVal(context);
    }

    private void dealWithDecimalRankVal(HandlerContext context) {
        if (Boolean.TRUE.equals(context.getRankIsSupportDecimal())) {
            RankVO rankVO = context.getRankVO();
            // 1、处理个人数据
            if (Objects.nonNull(rankVO.getMyselfRank())) {
                rankVO.getMyselfRank().setDecimalVal(Objects.isNull(rankVO.getMyselfRank().getValue()) ? "0" : convertToStr(rankVO.getMyselfRank().getValue().intValue()));
                rankVO.getMyselfRank().setDecimalDiffVal(Objects.isNull(rankVO.getMyselfRank().getDiffVal()) ? null : convertToStr(rankVO.getMyselfRank().getDiffVal().intValue()));
            }
            if (Objects.nonNull(rankVO.getMyCpRankItem())) {
                rankVO.getMyCpRankItem().setDecimalVal(Objects.isNull(rankVO.getMyCpRankItem().getValue()) ? "0" : convertToStr(rankVO.getMyCpRankItem().getValue().intValue()));
                rankVO.getMyCpRankItem().setDecimalDiffVal(Objects.isNull(rankVO.getMyCpRankItem().getDiffVal()) ? null : convertToStr(rankVO.getMyCpRankItem().getDiffVal().intValue()));
            }
            // 2、处理榜单列表
            if (!CollectionUtils.isEmpty(rankVO.getRankList())) {
                for (RankItem rankItem : rankVO.getRankList()) {
                    rankItem.setDecimalVal(Objects.isNull(rankItem.getValue()) ? "0" : convertToStr(rankItem.getValue().intValue()));
                }
            }
            if (!CollectionUtils.isEmpty(rankVO.getCpRankItemList())) {
                for (CpRankItem cpRankItem : rankVO.getCpRankItemList()) {
                    cpRankItem.setDecimalVal(Objects.isNull(cpRankItem.getValue()) ? "0" : convertToStr(cpRankItem.getValue().intValue()));
                }
            }
        }
    }

}
