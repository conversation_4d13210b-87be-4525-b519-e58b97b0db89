package cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.springStarCeremony;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.DateUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.umpServices.activity.common.jimu.inner.PrizeItem;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeParam;
import cn.yizhoucp.ump.api.vo.jimu.task.TakePrizeReturn;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.SpringStarCeremonyConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.astrology.starSpring.StarSpringTrackManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.AbstractMissionTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.MissionContext;
import cn.yizhoucp.ump.biz.project.biz.manager.luckyBag.AstrologyValueManager;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.ScenePrizeService;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.SpringStarCeremonyConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.SpringStarCeremonyConstant.TASK_CUR_FINISH_TIMES;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.astrology.SpringStarCeremonyConstant.TASK_TAKE_PRIZE;

@Service
@Slf4j
public class SpringStarCeremonyMissionManager extends AbstractMissionTemplate {

    @Resource
    private ScenePrizeService scenePrizeService;
    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private RedisManager redisManager;
    @Resource
    private SpringStarCeremonyTrackManager springStarCeremonyTrackManager;

    @Override
    @ActivityCheck(activityCode = ACTIVITY_CODE)
    public TakePrizeReturn takePrize(TakePrizeParam param) {
        if (StringUtils.isBlank(param.getTaskCode())) {
            throw new ServiceException(ErrorCode.INVALID_PARAM);
        }

        if ("TASK_6".equals(param.getTaskCode())) {
            Integer taskCurFinishTimes = Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), param.getTaskCode()))).orElse(0);
            if (8 > taskCurFinishTimes) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "您尚未完成该任务，继续完成吧～");
            }

            if (!Boolean.TRUE.equals(redisManager.setnx(String.format(TASK_TAKE_PRIZE, param.getUid(), param.getTaskCode()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "您已完成该任务，看看其他任务吧～");
            }

            List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, param.getTaskCode());
            sendPrizeManager.sendPrize(
                    BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                    scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
            );

            springStarCeremonyTrackManager.allActivityReceiveAward("task", param.getTaskCode(), scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), param.getUid());

            return TakePrizeReturn.builder().prizeItemList(this.scenePrizeDO2PrizeItem(scenePrizeDOList)).build();
        }

        for (SpringStarCeremonyConstant.Task task : SpringStarCeremonyConstant.Task.values()) {
            if (task.name().equals(param.getTaskCode())) {
                Integer taskCurFinishTimes = Optional.ofNullable(redisManager.getInteger(String.format(TASK_CUR_FINISH_TIMES, param.getUid(), task.name()))).orElse(0);
                if (10 > taskCurFinishTimes) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您尚未完成该任务，继续完成吧～");
                }

                if (!Boolean.TRUE.equals(redisManager.setnx(String.format(TASK_TAKE_PRIZE, param.getUid(), task.name()), System.currentTimeMillis(), DateUtil.ONE_MONTH_SECOND))) {
                    throw new ServiceException(ErrorCode.INVALID_PARAM, "您已完成该任务，看看其他任务吧～");
                }

                List<ScenePrizeDO> scenePrizeDOList = scenePrizeService.getListBySceneCode(ACTIVITY_CODE, param.getTaskCode());
                sendPrizeManager.sendPrize(
                        BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                        scenePrizeDOList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, param.getUid())).collect(Collectors.toList())
                );

                springStarCeremonyTrackManager.allActivityReceiveAward("task", param.getTaskCode(), scenePrizeDOList.get(0).getPrizeValue(), scenePrizeDOList.get(0).getPrizeValueGold(), scenePrizeDOList.get(0).getPrizeNum(), param.getUid());

                return TakePrizeReturn.builder().prizeItemList(this.scenePrizeDO2PrizeItem(scenePrizeDOList)).build();
            }
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    protected Boolean doCheck(MissionContext context) {
        return null;
    }

    @Override
    protected Boolean init(MissionContext context) {
        return null;
    }

    @Override
    protected Boolean completedProcess(MissionContext context) {
        return null;
    }

    private List<PrizeItem> scenePrizeDO2PrizeItem(List<ScenePrizeDO> params) {
        List<PrizeItem> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(params)) {
            for (ScenePrizeDO item : params) {
                list.add(PrizeItem.builder()
                        .valueGold(Math.toIntExact(item.getPrizeValueGold()))
                        .prizeIcon(item.getPrizeIcon())
                        .prizeName(item.getPrizeDesc()).build());
            }
        }
        return list;
    }

}
