package cn.yizhoucp.ump.biz.project.dal.jpa.dataobject;

import cn.yizhoucp.ms.core.base.enums.CommonStatus;
import cn.yizhoucp.ms.core.base.enums.UserPackageBizType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * 奖池内容
 *
 * @author: lianghu
 */
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "lucky_bag_content")
public class PrizePoolContentDO {

    @Id
    @GenericGenerator(name = "manualStrategy", strategy = "cn.yizhoucp.ump.biz.framework.config.jpa.ManualInsertGenerator")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "manualStrategy")
    private Long id;
    /** 应用ID */
    private Long appId;
    /** 活动ID */
    private Long activityId;
    /** 奖池ID */
    private Long prizePoolId;
    /** 奖品总价值 */
    private Long totalCoin;
    /** 奖品总数量 */
    private Long totalNum;
    /** 可用数量 */
    private Long availableNum;
    /** 奖池内容ID */
    private String bizId;
    /** 奖池内容类型 */
    @Enumerated(EnumType.STRING)
    private UserPackageBizType bizType;
    /** 可用状态 */
    @Enumerated(EnumType.STRING)
    private CommonStatus status;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;

}
