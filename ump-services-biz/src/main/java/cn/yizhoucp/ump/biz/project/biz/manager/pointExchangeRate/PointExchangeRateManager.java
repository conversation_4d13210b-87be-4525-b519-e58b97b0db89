package cn.yizhoucp.ump.biz.project.biz.manager.pointExchangeRate;

import cn.yizhoucp.ump.biz.project.biz.constant.RedisConstant;
import cn.yizhoucp.ump.biz.project.biz.enums.PerExamineHandleType;
import cn.yizhoucp.ump.biz.project.biz.enums.PerExamineStatus;
import cn.yizhoucp.ump.biz.project.biz.enums.PerStatus;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PointExchangeRateDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.PointExchangeRateExamineDO;
import cn.yizhoucp.ump.biz.project.dal.service.PointExchangeRateExamineService;
import cn.yizhoucp.ump.biz.project.dal.service.PointExchangeRateService;
import cn.yizhoucp.ms.core.base.enums.ump.PointExchangeRateScene;
import cn.yizhoucp.ms.core.vo.CommonResultVO;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.redPacket.PointExchangeRateExamineVO;
import cn.yizhoucp.ump.api.vo.redPacket.PointExchangeRateVO;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 积分汇率管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PointExchangeRateManager {

    @Resource
    RedisManager redisManager;

    @Resource
    PointExchangeRateService pointExchangeRateService;

    @Resource
    PointExchangeRateExamineService examineService;

    /** 默认 积分汇率 */
    private static final Double DEFAULT_POINT_RATE = 0.22d;

    /**
     * 根据场景获取积分汇率（最多支持 3 位小数）
     *
     * @param unionId 应用唯一标识
     * @param scene   场景
     * @return Double
     */
    public Double getPointExchangeRateByScene(String unionId, String scene) {
        if (StringUtils.isBlank(unionId)) {
            return DEFAULT_POINT_RATE;
        }
        // 优先从缓存中拉
        String key = String.format(RedisConstant.POINT_EXCHANGE_RATE_CACHE, unionId);
        Object rateObj = redisManager.hget(key, scene);
        if (!Objects.isNull(rateObj)) {
            double result = Double.parseDouble(String.valueOf(rateObj));
            log.debug("redis result {}", result);
            return result;
        }
        // 从数据库中查对应汇率
        List<PointExchangeRateDO> rateList = pointExchangeRateService.getListByUnionId(unionId);
        if (CollectionUtils.isEmpty(rateList)) {
            log.debug("default rate 0.22");
            redisManager.hset(key, scene, DEFAULT_POINT_RATE, DateUtil.ONE_MONTH_SECOND);
            return DEFAULT_POINT_RATE;
        }
        Map<Object, Object> rateMap = rateList.stream().collect(Collectors.toMap(PointExchangeRateDO::getScene, rate -> BigDecimal.valueOf(rate.getExchangeRate()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP).doubleValue(), (v1, v2) -> v1));
        redisManager.hmset(key, rateMap, DateUtil.ONE_MONTH_SECOND);
        Object rate = Objects.isNull(rateMap.get(scene)) ? rateMap.get(PointExchangeRateScene.default_rate.getCode()) : rateMap.get(scene);
        double result = (double) rate;
        log.debug("db result {}", result);
        return result;
    }

    /**
     * 获取所有积分汇率场景
     *
     * @return Map<String, String>
     */
    public List<JSONObject> getPointExchangeRateScene() {
        return PointExchangeRateScene.getList();
    }

    /**
     * 根据 unionId 分页获取积分汇率
     *
     * @param page    当前页码
     * @param size    每页条数
     * @param unionId 应用唯一标识
     * @param scene   场景
     * @param status  涨停
     * @return AdminPageVO<PointExchangeRateVO>
     */
    public AdminPageVO<PointExchangeRateVO> getPointExchangeRateList(Integer page, Integer size, String unionId, String scene, String status) {
        page = Objects.isNull(page) || page < 1 ? 1 : page;
        size = Objects.isNull(size) ? 20 : size;
        AdminPageVO<PointExchangeRateVO> result = new AdminPageVO<>();
        result.setPageIndex(page);
        result.setPageSize(size);
        result.setHasNext(false);
        Page<PointExchangeRateDO> ratePage = pointExchangeRateService.pageGetList(page, size, unionId, scene, status);
        if (Objects.isNull(ratePage) || CollectionUtils.isEmpty(ratePage.getContent())) {
            return result;
        }
        List<PointExchangeRateVO> rateList = ratePage.getContent().stream()
                .filter(rate -> !rate.getStatus().equals(PerStatus.delete.getStatus()))
                .map(rateDo -> {
                    PointExchangeRateVO rateVo = new PointExchangeRateVO();
                    BeanUtils.copyProperties(rateDo, rateVo);
                    rateVo.setExchangeRate(BigDecimal.valueOf(rateDo.getExchangeRate()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP).doubleValue());
                    return rateVo;
                }).collect(Collectors.toList());
        result.setItems(rateList);
        result.setHasNext(ratePage.hasNext());
        result.setTotalCount(ratePage.getTotalElements());
        result.setTotalPagesCount(ratePage.getTotalPages());
        return result;
    }

    /**
     * 新增或更新积分汇率
     *
     * @param perVo 积分汇率
     * @return CommonResultVO
     */
    public CommonResultVO saveOrUpdate(PointExchangeRateVO perVo) {
        if (Objects.isNull(perVo) || Objects.isNull(perVo.getUnionId()) || Objects.isNull(perVo.getScene())) {
            return CommonResultVO.fail("必要参数不能为空");
        }
        PerExamineHandleType handleType = PerExamineHandleType.update;
        PointExchangeRateDO perDo = pointExchangeRateService.getByUnionIdAndScene(perVo.getUnionId(), perVo.getScene());
        JSONObject valueBefore = new JSONObject();
        JSONObject valueAfter = new JSONObject();
        if (Objects.isNull(perVo.getId())) {
            if (!Objects.isNull(perDo)) {
                // 同一应用不可新增同一场景汇率
                return CommonResultVO.fail("当前应用已经配置了该场景汇率，请勿重复配置");
            }
            handleType = PerExamineHandleType.add;
        } else {
            // 不可将汇率更新为同一应用，同一场景
            if (!Objects.isNull(perDo) && !perVo.getId().equals(perDo.getId())) {
                return CommonResultVO.fail("当前应用已经配置了该场景汇率，请勿重复配置");
            }
            // 有审核中的记录，不可再变更
            List<PointExchangeRateExamineDO> examineList = examineService.getExamineByPerIdAndStatus(perVo.getId(), PerExamineStatus.examining.getStatus());
            if (!CollectionUtils.isEmpty(examineList)) {
                return CommonResultVO.fail("当前积分汇率有审核中记录，请先联系审核人员完成审核");
            }
            valueBefore.put("scene", perDo.getScene());
            valueBefore.put("rate", BigDecimal.valueOf(perDo.getExchangeRate()).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP).doubleValue());
            valueBefore.put("status", PerStatus.getInstance(perDo.getStatus()).getDesc());
            perDo.setExamine(true);
            // 更新汇率
            perDo = pointExchangeRateService.saveOrUpdate(perDo);
        }
        valueAfter.put("scene", perVo.getScene());
        valueAfter.put("rate", perVo.getExchangeRate());
        valueAfter.put("status", PerStatus.getInstance(perVo.getStatus()).getDesc());
        // 保存审核信息
        Boolean result = examineService.saveExamine(Objects.isNull(perDo) ? null : perDo.getId(), perVo.getUnionId(), handleType.getStatus(), valueBefore.toJSONString(),
                valueAfter.toJSONString(), perVo.getReason(), perVo.getUpdateBy());
        return result ? CommonResultVO.success() : CommonResultVO.fail("保存审核信息失败");
    }

    /**
     * 删除积分汇率记录
     *
     * @param perId    积分汇率id
     * @param reason   理由
     * @param operator 操作者
     * @return CommonResultVO
     */
    public CommonResultVO deletePerInfo(Long perId, String reason, String operator) {
        if (Objects.isNull(perId) || StringUtils.isBlank(reason)) {
            return CommonResultVO.fail("必要参数不能为空");
        }
        PointExchangeRateDO perDo = pointExchangeRateService.getById(perId);
        if (Objects.isNull(perDo)) {
            return CommonResultVO.fail("当前 id 对应记录不存在");
        }
        // 有审核中的记录，不可再变更
        List<PointExchangeRateExamineDO> examineList = examineService.getExamineByPerIdAndStatus(perId, PerExamineStatus.examining.getStatus());
        if (!CollectionUtils.isEmpty(examineList)) {
            return CommonResultVO.fail("当前记录正在审核中，请联系审核人员完成审核");
        }
        perDo.setExamine(true);
        perDo = pointExchangeRateService.saveOrUpdate(perDo);
        // 保存审核记录
        Boolean result = examineService.saveExamine(perId, perDo.getUnionId(), PerExamineHandleType.delete.getStatus(), null,
                null, reason, operator);
        return result ? CommonResultVO.success() : CommonResultVO.fail("保存审核信息失败");
    }

    /**
     * 根据 unionId 分页获取积分汇率审核列表
     *
     * @param page    当前页码
     * @param size    每页条数
     * @param unionId 应用唯一标识
     * @param status  状态
     * @return AdminPageVO<PointExchangeRateExamineVO>
     */
    public AdminPageVO<PointExchangeRateExamineVO> getPerExamineList(Integer page, Integer size, String unionId, String status) {
        page = Objects.isNull(page) || page < 1 ? 1 : page;
        size = Objects.isNull(size) ? 20 : size;
        AdminPageVO<PointExchangeRateExamineVO> result = new AdminPageVO<>();
        result.setPageIndex(page);
        result.setPageSize(size);
        result.setHasNext(false);
        Page<PointExchangeRateExamineDO> ratePage = examineService.pageGetList(page, size, unionId, status);
        if (Objects.isNull(ratePage) || CollectionUtils.isEmpty(ratePage.getContent())) {
            return result;
        }
        List<PointExchangeRateExamineVO> rateList = ratePage.getContent().stream().map(examineDo -> {
            PointExchangeRateExamineVO examineVo = new PointExchangeRateExamineVO();
            BeanUtils.copyProperties(examineDo, examineVo);
            examineVo.setHandleType(PerExamineHandleType.getInstance(examineDo.getHandleType()).getDesc());
            return examineVo;
        }).collect(Collectors.toList());
        result.setItems(rateList);
        result.setHasNext(ratePage.hasNext());
        result.setTotalCount(ratePage.getTotalElements());
        result.setTotalPagesCount(ratePage.getTotalPages());
        return result;
    }

    /**
     * 审核
     *
     * @param id       审核记录id
     * @param type     操作类型 0-通过；1-拒绝
     * @param operator 审核人
     * @return CommonResultVO
     */
    public CommonResultVO examineOperate(Long id, int type, String operator) {
        PointExchangeRateExamineDO examine = examineService.getById(id);
        if (Objects.isNull(examine) || !examine.getStatus().equals(PerExamineStatus.examining.getStatus())) {
            return CommonResultVO.fail("当前状态下不可操作");
        }
        if (Objects.isNull(examine.getPerId()) && PerExamineHandleType.add.getStatus() != examine.getHandleType()) {
            return CommonResultVO.fail("积分汇率申请记录异常，请联系后台管理员");
        }
        PointExchangeRateDO rate = null;
        if (!Objects.isNull(examine.getPerId())) {
            rate = pointExchangeRateService.getById(examine.getPerId());
        }
        if (0 == type) {
            JSONObject jsonObject = JSONObject.parseObject(examine.getValueAfter());
            // 审核通过
            if (PerExamineHandleType.update.getStatus() == examine.getHandleType()) {
                rate.setScene(jsonObject.getString("scene"));
                rate.setExchangeRate(BigDecimal.valueOf(jsonObject.getDouble("rate")).multiply(BigDecimal.valueOf(1000)).intValue());
                rate.setStatus(Objects.requireNonNull(PerStatus.getInstanceByDesc(jsonObject.getString("status"))).getStatus());
            } else if (PerExamineHandleType.delete.getStatus() == examine.getHandleType()) {
                rate.setStatus(PerStatus.delete.getStatus());
            } else if (PerExamineHandleType.add.getStatus() == examine.getHandleType()) {
                rate = new PointExchangeRateDO();
                rate.setUnionId(examine.getUnionId());
                rate.setScene(jsonObject.getString("scene"));
                rate.setExchangeRate(BigDecimal.valueOf(jsonObject.getDouble("rate")).multiply(BigDecimal.valueOf(1000)).intValue());
                rate.setStatus(Objects.requireNonNull(PerStatus.getInstanceByDesc(jsonObject.getString("status"))).getStatus());
                rate.setCreateBy(examine.getCreateBy());
                rate.setCreateTime(examine.getCreateTime());
            }
        }
        if (0 == type || PerExamineHandleType.add.getStatus() != examine.getHandleType()) {
            rate.setUpdateBy(examine.getCreateBy());
            rate.setUpdateTime(examine.getCreateTime());
            rate.setExamine(false);
            // 更新积分汇率记录
            pointExchangeRateService.saveOrUpdate(rate);
        }
        // 更新审核状态
        examineService.examineOperator(examine, 0 == type ? PerExamineStatus.pass.getStatus() : PerExamineStatus.refuse.getStatus(), operator);
        // 清除积分汇率缓存
        redisManager.delete(String.format(RedisConstant.POINT_EXCHANGE_RATE_CACHE, rate.getUnionId()));
        return CommonResultVO.success();
    }

}
