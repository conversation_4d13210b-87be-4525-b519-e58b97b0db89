package cn.yizhoucp.ump.biz.project.dal.mp.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("room_coin_box")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoomCoinBoxDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long triggerUid;

    private Long roomId;

    private String boxType;

    private String boxStatus;

    private Integer canOpenUserNum;

    private Integer coin;

    /** 宝箱开始生效时间 */
    private Long startTimestamp;

    /** 宝箱可打开时间 */
    private Long canOpenTimestamp;

    /** 宝箱过期时间 */
    private Long expireTimestamp;

    /** 宝箱结束时间 */
    private Long closeTimestamp;

    public enum BoxStatusType {
        INIT,
        OPEN,
        CLOSE;

    }
}
