package cn.yizhoucp.ump.biz.project.biz.manager.activity.familyKey;

import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.product.dto.*;
import cn.yizhoucp.product.enums.PackageUseScene;
import cn.yizhoucp.ump.biz.project.biz.enums.activity.familyKey.*;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AbstractManager;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.inner.WindowParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.DoDrawWithPoolStrategy;
import cn.yizhoucp.ump.biz.project.biz.util.ActivityTimeUtil;
import cn.yizhoucp.ump.biz.project.biz.util.LanlingActivityUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.SystemNPC;
import cn.yizhoucp.ms.core.base.UserPackageScene;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.base.enums.*;
import cn.yizhoucp.ms.core.base.util.Base64Util;
import cn.yizhoucp.ms.core.vo.imservices.SystemMessageModel;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.landingservices.FamilyInfoVO;
import cn.yizhoucp.ms.core.vo.landingservices.family.FamilyVO;
import cn.yizhoucp.ms.core.vo.roomservices.RoomVO;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyKey.*;
import cn.yizhoucp.ms.core.vo.umpServices.activity.familyKey.inner.*;
import cn.yizhoucp.ms.core.vo.userservices.UserBaseVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import static cn.yizhoucp.ms.core.base.ErrorCode.ACTIVITY_ERROR_180018;
import static cn.yizhoucp.ms.core.base.ErrorCode.ACTIVITY_ERROR_180019;

/**
 * 家族钥匙 PK 大赛
 *
 * @author: lianghu
 */
@Slf4j
@Service
public class FamilyKeyManager extends AbstractManager {

    @Value("${spring.profiles.active}")
    private String env;

    /** 个人任务完成次数 hash */
    private static final String PERSONAL_TASK_COMPLETE_TIMES = "family_key_personal_task_complete_times_%s";
    /** 家族金钥匙排名 zset */
    private static final String FAMILY_GOLD_RANK = "family_key_family_gold_rank_%s";
    /** 家族-个人金钥匙排名 zset */
    private static final String FAMILY_MEMBER_GOLD_RANK = "family_key_family_member_gold_rank_%s_%s";
    /** 家族金钥匙榜展示人数 */
    private static final Integer FAMILY_GOLD_RANK_SHOW_NUM = 50;
    /** 家族信息缓存 string */
    private static final String FAMILY_INFO_CACHE = "family_key_family_info_cache_%s";
    /** 家族-个人关系缓存 hash */
    private static final String FAMILY_MEMBER_INFO_CACHE = "family_key_member_info_cache_%s";
    /** 家族成就等级 hash */
    private static final String FAMILY_ACHIEVEMENT_LEVEL = "family_key_family_achievement_level_%s";
    /** 家族成就等级奖励下发锁 */
    private static final String FAMILY_ACHIEVEMENT_SEND_LOCK = "family_key_family_achievement_send_lock_%s_%s";
    /** 家族成就等级奖励发放记录 hash */
    private static final String FAMILY_ACHIEVEMENT_LEVEL_SEND_PRIZE_LOG = "family_key_family_achievement_level_send_prize_log_%s";
    /** 抽奖奖池 set */
    private static final String DRAW_PRIZE_POOL = "family_key_draw_prize_pool";
    /** 抽奖消耗金钥匙数/次 */
    private static final Integer DRAW_COST_GOLD_NUM = 10;
    /** 个人幸运值 hash */
    private static final String PERSONAL_LUCKY = "family_key_personal_lucky";
    /** 个人幸运值阈值 */
    private static final String PERSONAL_LUCKY_LIMIT = "family_key_personal_lucky_limit";
    /** 活动弹幕 */
    private static final String BARRAGE_LIST = "family_key_barrage_list";
    /** 抽奖弹幕文案 */
    private static final String BARRAGE_ITEM = "恭喜 %s 在强化炉获得 %s";
    /** 默认弹幕文案 */
    private static final String DEFAULT_BARRAGE_ITEM = "做任务，得钥匙，交换金钥匙开宝箱！领绝版豪礼！";
    /** 今日领取倒计时奖励记录 hash */
    private static final String TAKE_COUNT_DOWN_PRIZE = "family_key_take_count_down_prize_%s";
    /** 周日通知时间区间 start */
    private static final LocalTime NOTIFY_TIME_RANGE_START = LocalTime.parse("20:00:00");
    /** 周日通知时间区间 end */
    private static final LocalTime NOTIFY_TIME_RANGE_ENT = LocalTime.parse("21:00:00");
    /** 周日通知去重 hash */
    private static final String NOTIFY_NO_REPEAT = "family_key_notify_no_repeat_%s";
    /** 获得灰钥匙次序 hash */
    private static final String GET_HYS_GIFT_NUM = "family_key_get_hys_gift_pair";
    /** 灰钥匙赠送场景 */
    private static final Set GIVE_HYS_GIFT_FROM = Sets.newHashSet(GiftFrom.room.getScene(), GiftFrom.family_gift.getScene(), GiftFrom.chatroom.getScene());
    /** 关爱新人任务收礼方每日次数限制 hash */
    private static final String GAXR_RECEIVE_PERSON_LIMIT = "family_key_gaxr_receive_person_limit_%s";
    /** 关爱新人任务送礼方每日次数限制 hash */
    private static final String GAXR_GIVE_PERSON_LIMIT = "family_key_gaxr_give_person_limit_%s";
    /** 关爱新人任务收礼方限制次数 */
    private static final Integer GAXR_RECEIVE_PERSON_LIMIT_NUM = 2;
    /** 关爱新人任务送礼方限制次数 */
    private static final Integer GAXR_GIVE_PERSON_LIMIT_NUM = 1;
    /** 话题担当任务发言时段 hash */
    private static final String HTDD_FAMILY_SPEAK_RANGE_TIMES = "family_key_htdd_family_speak_range_times_%s";
    /** 话题担当任务发言时段限制发言次数 hash */
    private static final Integer HTDD_FAMILY_SPEAK_RANGE_TIMES_LIMIT = 2;
    /** 最美的祝福任务累计祝福值 hash */
    private static final String ZMDZF_TOTAL_COIN = "family_key_zmdzf_total_coin_%s_%s";
    /** 礼轻义重任务送物去重 */
    private static final String LQYZ_GIVE_GIFT_SET = "family_key_lqyz_give_gift_set_%s_%s";
    /** 礼轻义重任务收礼去重 */
    private static final String LQYZ_RECEIVE_GIFT_SET = "family_key_lqyz_receive_gift_set_%s_%s";
    /** 活跃担当任务开关 */
    private static final String HYDD_SWITCH = "family_key_hydd_switch";

    /**
     * 获取活动弹幕
     *
     * @return
     */
    public List<String> getBarrageList() {
        List<String> barrageList = barrageComponent.getBarrageList(BARRAGE_LIST, 20L);
        if (Objects.isNull(barrageList) || barrageList.size() < 10) {
            return Lists.newArrayList(DEFAULT_BARRAGE_ITEM);
        }
        return barrageList;
    }

    /**
     * 个人任务页
     *
     * @return
     */
    public PersonalTaskVO getPersonalTaskPageInfo() {
        Long uid = SecurityUtils.getCurrentUserIdLong();

        // 获取个人任务完成次数
        List<Integer> personalTaskCompleteTimes = getPersonalTaskCompleteTimes(uid);

        // 关爱新人
        Map<String, Object> gaxrTask = new HashMap<>(8);
        Integer times0 = personalTaskCompleteTimes.get(PersonalTaskEnum.GAXR.getIndex());
        gaxrTask.put("taskCompleteTimes", times0);
        gaxrTask.put("taskLimitTimes", PersonalTaskEnum.GAXR.getLimitTimes());
        gaxrTask.put("taskStatus", times0 >= PersonalTaskEnum.GAXR.getLimitTimes() ? 1 : 0);

        // 活跃担当
        Map<String, Object> hyddTask = new HashMap<>(8);
        Integer times1 = personalTaskCompleteTimes.get(PersonalTaskEnum.HYDD.getIndex());
        hyddTask.put("taskCompleteTimes", times1);
        hyddTask.put("taskLimitTimes", PersonalTaskEnum.HYDD.getLimitTimes());
        hyddTask.put("taskStatus", times1 >= PersonalTaskEnum.HYDD.getLimitTimes() ? 1 : 0);

        // 话题担当
        Map<String, Object> htddTask = new HashMap<>(8);
        Integer times2 = personalTaskCompleteTimes.get(PersonalTaskEnum.HTDD.getIndex());
        htddTask.put("taskCompleteTimes", times2);
        htddTask.put("taskLimitTimes", PersonalTaskEnum.HTDD.getLimitTimes());
        htddTask.put("taskStatus", times2 >= PersonalTaskEnum.HTDD.getLimitTimes() ? 1 : 0);
        htddTask.put("timeStatusList", getFamilySpeakTimeRange(uid));

        // 礼轻义重
        Map<String, Object> lqyzTask = new HashMap<>(8);
        Integer times3 = personalTaskCompleteTimes.get(PersonalTaskEnum.LQYZ_1.getIndex());
        lqyzTask.put("taskCompleteTimes2", times3);
        lqyzTask.put("taskLimitTimes2", PersonalTaskEnum.LQYZ_1.getLimitTimes());
        lqyzTask.put("taskStatus2", times3 >= PersonalTaskEnum.LQYZ_1.getLimitTimes() ? 1 : 0);
        Integer times4 = personalTaskCompleteTimes.get(PersonalTaskEnum.LQYZ_2.getIndex());
        lqyzTask.put("taskCompleteTimes1", times4);
        lqyzTask.put("taskLimitTimes1", PersonalTaskEnum.LQYZ_2.getLimitTimes());
        lqyzTask.put("taskStatus1", times4 >= PersonalTaskEnum.LQYZ_2.getLimitTimes() ? 1 : 0);

        // 谁是运气王
        Map<String, Object> ssyqwTask = new HashMap<>(8);
        Integer times5 = personalTaskCompleteTimes.get(PersonalTaskEnum.SSYQW_1.getIndex());
        ssyqwTask.put("taskCompleteTimes2", times5);
        ssyqwTask.put("taskLimitTimes2", PersonalTaskEnum.SSYQW_1.getLimitTimes());
        ssyqwTask.put("taskStatus2", times5 >= PersonalTaskEnum.SSYQW_1.getLimitTimes() ? 1 : 0);
        Integer times6 = personalTaskCompleteTimes.get(PersonalTaskEnum.SSYQW_2.getIndex());
        ssyqwTask.put("taskCompleteTimes1", times6);
        ssyqwTask.put("taskLimitTimes1", PersonalTaskEnum.SSYQW_2.getLimitTimes());
        ssyqwTask.put("taskStatus1", times6 >= PersonalTaskEnum.SSYQW_2.getLimitTimes() ? 1 : 0);

        // 空投炸弹
        Map<String, Object> ktzdTask = new HashMap<>(8);
        Integer times7 = personalTaskCompleteTimes.get(PersonalTaskEnum.KTZD_1.getIndex());
        Integer times8 = personalTaskCompleteTimes.get(PersonalTaskEnum.KTZD_2.getIndex());
        ktzdTask.put("taskCompleteTimes2", times7);
        ktzdTask.put("taskLimitTimes2", PersonalTaskEnum.KTZD_1.getLimitTimes());
        ktzdTask.put("taskStatus2", times7 >= PersonalTaskEnum.KTZD_1.getLimitTimes() ? 1 : 0);
        ktzdTask.put("taskCompleteTimes1", times8);
        ktzdTask.put("taskLimitTimes1", PersonalTaskEnum.KTZD_2.getLimitTimes());
        ktzdTask.put("taskStatus1", times8 >= PersonalTaskEnum.KTZD_2.getLimitTimes() ? 1 : 0);

        // 最美的祝福
        Map<String, Object> zmdzfTask = new HashMap<>(8);
        Integer times9 = personalTaskCompleteTimes.get(PersonalTaskEnum.ZMDZF.getIndex());
        zmdzfTask.put("taskCompleteTimes", times9);
        zmdzfTask.put("taskLimitTimes", PersonalTaskEnum.ZMDZF.getLimitTimes());
        zmdzfTask.put("taskStatus", times9 >= PersonalTaskEnum.ZMDZF.getLimitTimes() ? 1 : 0);

        // 一锤定亲
        Map<String, Object> ycdqTask = new HashMap<>(8);
        Integer times10 = personalTaskCompleteTimes.get(PersonalTaskEnum.YCDQ.getIndex());
        ycdqTask.put("taskCompleteTimes", times10);
        ycdqTask.put("taskLimitTimes", PersonalTaskEnum.YCDQ.getLimitTimes());
        ycdqTask.put("taskStatus", times10 >= PersonalTaskEnum.YCDQ.getLimitTimes() ? 1 : 0);

        // 获取家族信息
        Long familyId = null;
        Long chatId = null;
        FamilyVO familyInfoVO = getFamilyByUid(uid);
        if (Objects.isNull(familyInfoVO) || Objects.isNull(familyInfoVO.getId())) {
            familyInfoVO = getFamilyInfo(getDefaultFamilyId());
        }
        if (Objects.nonNull(familyInfoVO) && Objects.nonNull(familyInfoVO.getId())) {
            familyId = familyInfoVO.getId();
            chatId = familyInfoVO.getChatId();
        }

        // 获取钥匙信息
        Map<KeyEnum, Integer> keyNum = getKeyNum(uid);
        Integer goldKeyNum = keyNum.get(KeyEnum.JYS);
        Integer grayKeyNum = keyNum.get(KeyEnum.HYS);

        // 获取家族角色
        String role = FamilyRole.guest.getCode();

        return PersonalTaskVO.builder()
                .gaxrTask(gaxrTask)
                .hyddTask(hyddTask)
                .htddTask(htddTask)
                .lqyzTask(lqyzTask)
                .ssyqwTask(ssyqwTask)
                .ktzdTask(ktzdTask)
                .zmdzfTask(zmdzfTask)
                .ycdqTask(ycdqTask)
                .familyId(familyId)
                .chatId(chatId)
                .role(role)
                .goldKeyNum(goldKeyNum)
                .grayKeyNum(grayKeyNum)
                .build();
    }

    /**
     * 家族闯关页
     *
     * @return
     */
    public FamilyBreakThroughVO getFamilyBreakThroughPageInfo() {
        Long uid = SecurityUtils.getCurrentUserIdLong();

        // 获取当前家族信息
        Boolean hasFamily = Boolean.FALSE;
        Long familyId = null;
        String familyAvatar = null;
        String familyName = null;
        Integer familyGoldKeyNum = 0;
        FamilyVO familyInfoVO = getFamilyByUid(uid);
        if (Objects.nonNull(familyInfoVO) && Objects.nonNull(familyInfoVO.getId())) {
            hasFamily = Boolean.TRUE;
            familyId = familyInfoVO.getId();
            familyAvatar = familyInfoVO.getIcon();
            familyName = familyInfoVO.getName();
            familyGoldKeyNum = getFamilyGoldNum(familyInfoVO.getId());
        }

        // 获取家族隐藏任务
        List<FamilyTaskItem> familyTaskItemList = getFamilyTaskItems();

        // 获取家族成就列表
        List<AchievementItem> achievementItemList = getAchievementItems(familyId);

        // 自动补发分支
//        List<FamilyAchievementEnum> achievementList = FamilyAchievementEnum.getAchievementListByCurrent(familyGoldKeyNum);
//        if (!CollectionUtils.isEmpty(achievementList) && activityIsEnable()) {
//            for (FamilyAchievementEnum item : achievementList) {
//                if (completeFamilyAchievement(familyInfoVO, item)) {
//                    log.info("familyKey 自动补发分支 familyId:{}, itemId:{}", familyInfoVO.getId(), item.getAchievementLevel());
//                }
//            }
//        }

        // 获取家族当前发言人数
        Integer currentPersonNum = getFamilyPersonNum(familyId, getToday());

        return FamilyBreakThroughVO.builder()
                .familyTaskList(familyTaskItemList)
                .familyAchievementList(achievementItemList)
                .hasFamily(hasFamily)
                .familyAvatar(familyAvatar)
                .familyName(familyName)
                .familyGoldKeyNum(familyGoldKeyNum)
                .currentPersonNum(currentPersonNum).build();
    }

    /**
     * 抽奖页
     *
     * @return
     */
    public FamilyKeyDrawVO getDrawPageInfo() {

        Long uid = SecurityUtils.getCurrentUserIdLong();

        // 获取奖品列表
        List<PrizeItem> prizeItemList = getDrawPrizeList();

        // 获取个人金钥匙数量
        Map<KeyEnum, Integer> keyNum = getKeyNum(uid);

        // 获取个人幸运值
        Integer lucky = getPersonalLucky(uid);

        return FamilyKeyDrawVO.builder()
                .prizeList(prizeItemList)
                .keyNum(keyNum.get(KeyEnum.JYS))
                .lucky(lucky)
                .luckyLimit(getLuckyLimit())
                .build();
    }

    /**
     * 抽奖
     *
     * @return
     */
    public DrawReturnVO draw(Integer times) {
        if (!activityIsEnable()) {
            return null;
        }
        UserVO user = feignUserService.getBasic(SecurityUtils.getCurrentUserIdLong(), ServicesAppIdEnum.lanling.getAppId()).successData();

        // 验证金钥匙
        checkKeyNum(user.getId(), times * DRAW_COST_GOLD_NUM);

        log.info("familyKey 抽奖 uid:{}, times:{}", user.getId(), times);

        // 抽奖
//        List<ScenePrizeDO> prizeItemList = drawComponent.getMissionList(getActivityCode(), user.getId(), times);
        List<ScenePrizeDO> prizeItemList = doDraw(user.getId(), times);

        // 扣除金钥匙
        deductKeyNum(user.getId(), KeyEnum.JYS, times * DRAW_COST_GOLD_NUM);

        // 下发奖励
        sendPrizeComponent.sendPrize(user.getAppId(), getActivityCode(), "family_key_draw", user.getId(), prizeItemList);

        // 添加弹幕
        barrageComponent.putBarrage(BARRAGE_LIST, String.format(BARRAGE_ITEM, user.getName(), getPrizeDescListStr(prizeItemList)));

        return DrawReturnVO.builder()
                .prizeItemList(convert2PrizeItem(prizeItemList)).build();
    }

    /**
     * 荣耀榜页
     *
     * @return
     */
    public FamilyKeyRankVO getRankPageInfo() {

        // 获取家族排名
        List<FamilyItem> familyItemList = getFamilyItems();

        // 获取奖励列表
        List<PrizeItem> prizeItemList = getPrizeItems();

        // 获取家族信息
        Boolean hasFamily = Boolean.FALSE;
        Long familyId = null;
        String familyName = null;
        String familyAvatar = null;
        Integer familyGoldKeyNum = null;
        Integer familyRank = null;
        FamilyInfoVO familyVO = feignLanlingService.getFamilyInfoByUid(SecurityUtils.getCurrentUserIdLong()).successData();
        if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
            hasFamily = Boolean.TRUE;
            familyId = familyVO.getId();
            familyName = familyVO.getName();
            familyAvatar = familyVO.getAvatar();
            familyGoldKeyNum = getFamilyGoldNum(familyVO.getId());
            familyRank = getFamilyRank(familyVO.getId());
        }

        return FamilyKeyRankVO.builder()
                .familyList(familyItemList)
                .prizeList(prizeItemList)
                .hasFamily(hasFamily)
                .familyId(familyId)
                .familyName(familyName)
                .familyAvatar(familyAvatar)
                .familyGoldKeyNum(familyGoldKeyNum)
                .familyRank(familyRank).build();
    }

    /**
     * 获取是否展示倒计时
     *
     * @return
     */
    public Boolean showCountDown() {
        if (!activityIsEnable()) {
            return false;
        }

        if (!hyddTaskIsEnable()) {
            return false;
        }

        Long uid = SecurityUtils.getCurrentUserIdLong();

        // 获取是否展示
        return !hasSendCountDownPrize(uid);
    }

    protected boolean hyddTaskIsEnable() {
        String status = (String) redisManager.get(HYDD_SWITCH);
        if ("on".equals(status)) {
            return true;
        }
        return false;
    }

    /**
     * 下发倒计时奖励
     *
     * @return
     */
    public Boolean sendCountDownPrize() {
        if (!activityIsEnable()) {
            return false;
        }

        if (!hyddTaskIsEnable()) {
            return false;
        }

        Long uid = SecurityUtils.getCurrentUserIdLong();

        // 验证当日未下发
        if (hasSendCountDownPrize(uid)) {
            log.error("当日已领取倒计时奖励 uid:{}", uid);
            return false;
        }

        // 完成任务
        completePersonalTask(uid, PersonalTaskEnum.HYDD);

        // 更新当日记录
        redisManager.hset(String.format(TAKE_COUNT_DOWN_PRIZE, getToday()), uid.toString(), System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND * 3);
        return true;
    }

    public boolean roomAuctionSuccessHandle(Long uid, String type) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey拍卖成功 uid:{}", uid);
        Map<String, Object> params = new HashMap<>();
        FamilyVO familyVO = getFamilyByUid(uid);
        if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
            params.put("familyId", familyVO.getId());
            params.put("type", type);
        }
        return completePersonalTask(uid, PersonalTaskEnum.YCDQ, params);
    }

    public boolean weddingRoomCoinUpdateHandle(Long roomId, Long uid, Long totalCoin) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey祝福值更新 roomId:{}, uid:{}, totalCoin:{} ", roomId, uid, totalCoin);
        Double after;
        if (redisManager.hasKey(String.format(ZMDZF_TOTAL_COIN, getToday(), roomId))) {
            after = redisManager.hincr(String.format(ZMDZF_TOTAL_COIN, getToday(), roomId), uid.toString(), totalCoin,DateUtil.ONE_MONTH_SECOND);
        } else {
            redisManager.hset(String.format(ZMDZF_TOTAL_COIN, getToday(), roomId), uid.toString(), totalCoin);
            after = totalCoin.doubleValue();
        }
        if (after.intValue() >= 20) {
            return completePersonalTask(uid, PersonalTaskEnum.ZMDZF);
        }
        return false;
    }

    public boolean sendRedPacketHandle(Long uid, Integer coin) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey发红包 uid:{}", uid);
        Map<String, Object> params = new HashMap<>();
        FamilyVO familyVO = getFamilyByUid(uid);
        if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
            params.put("familyId", familyVO.getId());
            params.put("coin", coin);
        }
        return completePersonalTask(uid, PersonalTaskEnum.SSYQW_1, params);
    }

    public boolean openRedPacketHandle(Long uid) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey抢红包 uid:{}", uid);
        Map<String, Object> params = new HashMap<>();
        FamilyVO familyVO = getFamilyByUid(uid);
        if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
            params.put("familyId", familyVO.getId());
        }
        return completePersonalTask(uid, PersonalTaskEnum.SSYQW_2, params);
    }

    public boolean buyAirdropHandle(Long uid, Integer count, String airdropName) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey买空投 uid:{}", uid);
        Map<String, Object> params = new HashMap<>();
        FamilyVO familyVO = getFamilyByUid(uid);
        if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
            params.put("familyId", familyVO.getId());
            params.put("type", "user");
            params.put("count", count);
            params.put("airdropName", airdropName);
        }

        return completePersonalTask(uid, PersonalTaskEnum.KTZD_1, params);
    }

    public boolean openAirdropHandle(Long uid, Long airdropId) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey抢空投 uid:{}", uid);

        FamilyAchievementEnum item = FamilyAchievementEnum.getById(airdropId);
        Map<String, Object> params = new HashMap<>();
        FamilyVO familyVO = getFamilyByUid(uid);
        if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
            params.put("familyId", familyVO.getId());
            params.put("airdropName", Objects.nonNull(item) ? item.getAirdropName() : "");
        }
        return completePersonalTask(uid, PersonalTaskEnum.KTZD_2, params);
    }

    public boolean familySpeakHandle(Long uid, Long familyId) {
        if (!activityIsEnable()) {
            return false;
        }
        log.info("familyKey家族发言 uid:{}, familyId:{}", uid, familyId);
        Map<String, Object> param = new HashMap();
        param.put("familyId", familyId);
        return completePersonalTask(uid, PersonalTaskEnum.HTDD, param);
    }

    private List<Integer> getFamilySpeakTimeRange(Long uid) {
        List<Integer> list = (List<Integer>) redisManager.hget(String.format(HTDD_FAMILY_SPEAK_RANGE_TIMES, getToday()), uid.toString());
        if (Objects.isNull(list)) {
            list = Lists.newArrayList(0, 0, 0, 0, 0, 0, 0);
            redisManager.hset(String.format(HTDD_FAMILY_SPEAK_RANGE_TIMES, getToday()), uid.toString(), list, DateUtil.ONE_DAY_SECOND * 24);
        }
        return list;
    }

    private boolean getHysTrack(Long uid, FamilyVO family, Integer num) {
        // 埋点
        Map<String, Object> params = new HashMap<>();
        params.put("type", "灰钥匙");
        params.put("family_id", -1L);
        if (Objects.nonNull(family) && Objects.nonNull(family.getId())) {
            params.put("family_id", family.getId());
        }
        for (int i = 0; i < num; i++) {
            yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "get_key2.0_activity", params, ServicesNameEnum.ump_services.getCode());
        }
        return true;
    }

    private boolean setFamilySpeakTimeRange(Long uid) {
        List<Integer> list = getFamilySpeakTimeRange(uid);
        Integer index = TimeRangeEnum.getIndex(LocalTime.now());
        log.info("更新发言区间 uid:{}, list:{}, index:{}", uid, JSONObject.toJSONString(uid), index);
        Integer targetTimes = list.get(index) + 1;
        if (targetTimes > HTDD_FAMILY_SPEAK_RANGE_TIMES_LIMIT) {
            log.info("更新发言区间失败 uid:{}, targetTimes:{}", uid, targetTimes);
            return false;
        }
        log.info("更新发言区间成功 uid:{}, index:{}, targetTimes:{}", uid, index, targetTimes);
        list.set(index, targetTimes);
        redisManager.hset(String.format(HTDD_FAMILY_SPEAK_RANGE_TIMES, getToday()), uid.toString(), list);
        return true;
    }

    /**
     * 登录处理
     *
     * @return boolean
     */
    public boolean loginHandle() {
        if (!activityIsEnable()) {
            return false;
        }
        Long uid = SecurityUtils.getCurrentUserIdLong();

        log.info("预通知 uid:{}, ts:{}", uid, getNowTimestamp());

        // 过滤时间区间
        if (!needNotify()) {
            return false;
        }

        // 验证未发送
        Long ts = (Long) redisManager.hget(String.format(NOTIFY_NO_REPEAT, getWeekFirstDay()), uid.toString());
        if (Objects.nonNull(ts)) {
            return false;
        }

        // 发送消息
        Map<KeyEnum, Integer> keyNumMap = getKeyNum(uid);
        Integer greyKeyNum = Optional.ofNullable(keyNumMap.get(KeyEnum.HYS)).orElse(0);
        Integer goldKeyNum = Optional.ofNullable(keyNumMap.get(KeyEnum.JYS)).orElse(0);
        String msg = "今天是周日！您还有 %s 把灰钥匙， %s 把金钥匙未兑换使用 \n 今日 24:00 灰/金钥匙数量将晴空哦～请记得进入藏宝阁使用哦";
        npcNotify(uid, String.format(msg, greyKeyNum, goldKeyNum));

        log.info("familyKey 发送登录通知 uid:{}, msg:{}", uid, msg);

        // no-repeat
        redisManager.hset(String.format(NOTIFY_NO_REPEAT, getWeekFirstDay()), uid.toString(), System.currentTimeMillis(), DateUtil.ONE_DAY_SECOND * 24);

        return true;
    }

    /**
     * 送礼处理
     *
     * @return
     */
    public boolean giveGiftHandle(List<CoinGiftGivedModel> modelList) {
        if (!activityIsEnable()) {
            return false;
        }
        for (CoinGiftGivedModel item : modelList) {

            // 空投礼物不计
            if (GiftFrom.airdrop_family.getCode().equals(item.getFrom())) {
                continue;
            }
            log.info("familyKey活动送礼 fromUid:{}, toUid:{}, key:{}, item:{}", item.getFromUid(), item.getToUid(), item.getGiftKey(), JSONObject.toJSONString(item));
            UserVO fromUser = feignUserService.getBasic(item.getFromUid(), ServicesAppIdEnum.lanling.getAppId()).successData();
            UserVO toUser = feignUserService.getBasic(item.getToUid(), ServicesAppIdEnum.lanling.getAppId()).successData();
            if (Objects.isNull(toUser)) {
                log.error("familyKey 收礼用户不存在或已被封禁");
                continue;
            }

            FamilyVO fromFamily = getFamilyByUid(fromUser.getId());
            FamilyVO toFamily = getFamilyByUid(toUser.getId());

            // 灰钥匙
            if (KeyEnum.HYS.getCode().equals(item.getGiftKey())) {
                log.info("familyKey送灰钥匙 fromUid:{}, toUid:{}", item.getFromUid(), item.getToUid());
                // 收礼方下发金钥匙
                sendPrizeComponent.sendGift(null, KeyEnum.JYS.getCode(), item.getToUid(), item.getProductCount(), String.format("%s_activity", getActivityCode()), ActivityTimeUtil.getLastTimeOfWeek(getActivityCode()));
                // 埋点
                Map<String, Object> params = new HashMap<>();
                params.put("type", "金钥匙");
                params.put("family_id", Optional.ofNullable(toFamily).map(obj -> obj.getId()).orElse(-1L));
                for (int i = 0; i < item.getProductCount(); i++) {
                    yzKafkaProducerManager.dataRangerTrack(toUser.getAppId(), toUser.getId(), "get_key2.0_activity", params, ServicesNameEnum.ump_services.getCode());
                }
                // 累计家族金钥匙榜、家族-个人金钥匙榜
                if (Objects.nonNull(toFamily) && Objects.nonNull(toFamily.getId())) {
                    incrFamilyGoldNum(toFamily, toUser.getId(), item.getProductCount().intValue());
                }
                // 系统通知
                GiftFrom giftFrom = GiftFrom.findByCode(item.getFrom());
                if (Objects.isNull(giftFrom)) {
                    continue;
                }
                String chatMsg = String.format("我送你 %s 把灰钥匙，凑够 10 把钥匙就能免费开宝箱啦", item.getProductCount());
                String otherMsg = String.format("%s 送 %s %s 把灰钥匙，凑够 10 把钥匙就能免费开宝箱啦！%s", fromUser.getName(), toUser.getName(), item.getProductCount(), getJumpText2());
                SystemMessageModel smm = new SystemMessageModel();
                smm.setType(ImMessageType.system_msg.getCode());
                smm.setText(otherMsg);
                switch (giftFrom) {
                    case chat:
                        chatNotify(fromUser.getId(), toUser.getId(), chatMsg);
                        break;
                    case family_gift:
                        FamilyVO familyInfo = feignLanlingService.familyInfo(item.getRelationId()).successData();
                        Long familyChatId = feignImService.getTeamChatByImTid(familyInfo.getImTid()).successData();
                        feignImService.sendTeamCustomSystemMsgBase64(familyChatId, familyInfo.getOwner().getId(), ServicesAppIdEnum.lanling.getAppId(), Base64Util.encodeToString(JSONObject.toJSONString(smm)));
                        break;
                    case chatroom:
                        feignImService.sendChatroomMessageByChatroomIdBase64(item.getRelationId(), ServicesAppIdEnum.lanling.getAppId(), SystemNPC.LANLING_LITTLE.getUserId(), null, Base64Util.encodeToString(JSONObject.toJSONString(smm)));
                        break;
                    case room:
                        RoomVO roomVO = feignRoomService.getRoomInfoByRoomId(item.getRelationId(), ServicesAppIdEnum.lanling.getAppId()).successData();
                        avNotify(roomVO.getChannelId(), smm.getText());
                        break;
                }
            }

            // 指定场景任务
            if (fromContain(item.getFrom())) {
                // 关爱新人任务
                if (Objects.equals("LITTLE_FLOWER", item.getGiftKey())) {
                    Integer giveNum = Optional.ofNullable((Integer) redisManager.hget(String.format(GAXR_GIVE_PERSON_LIMIT, getToday()), fromUser.getId() + "_" + toUser.getId())).orElse(0);
                    Integer receiveNum = Optional.ofNullable((Integer) redisManager.hget(String.format(GAXR_RECEIVE_PERSON_LIMIT, getToday()), toUser.getId().toString())).orElse(0);
                    Map<String, Object> params = new HashMap();
                    if (Objects.nonNull(toFamily) && Objects.nonNull(toFamily.getId())) {
                        params.put("familyId", toFamily.getId());
                    }
                    if (giveNum + 1 <= GAXR_GIVE_PERSON_LIMIT_NUM && receiveNum + 1 <= GAXR_RECEIVE_PERSON_LIMIT_NUM && completePersonalTask(fromUser.getId(), PersonalTaskEnum.GAXR, params)) {
                        redisManager.hincr(String.format(GAXR_RECEIVE_PERSON_LIMIT, getToday()), toUser.getId().toString(), 1,DateUtil.ONE_MONTH_SECOND);
                        redisManager.hincr(String.format(GAXR_GIVE_PERSON_LIMIT, getToday()), fromUser.getId() + "_" + toUser.getId(), 1,DateUtil.ONE_MONTH_SECOND);
                    }
                }

                // 礼轻义重任务
                if (item.getCoin() > 1) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("giftName", item.getName());
                    param.put("coin", item.getCoin());
                    param.put("family_id", -1L);
                    if (!giveGiftContain(fromUser.getId(), item.getGiftKey())) {
                        if (Objects.nonNull(fromFamily) && Objects.nonNull(fromFamily.getId())) {
                            param.put("family_id", fromFamily.getId());
                        }
                        completePersonalTask(fromUser.getId(), PersonalTaskEnum.LQYZ_1, param);
                        addGiveGiftSet(fromUser.getId(), item.getGiftKey());
                    }
                    if (!receiveGiftContain(toUser.getId(), item.getGiftKey())) {
                        if (Objects.nonNull(toFamily) && Objects.nonNull(toFamily.getId())) {
                            param.put("family_id", toFamily.getId());
                        }
                        completePersonalTask(toUser.getId(), PersonalTaskEnum.LQYZ_2, param);
                        addReceiveGiftSet(toUser.getId(), item.getGiftKey());
                    }
                }
            }
        }
        return true;
    }

    private boolean fromContain(String from) {
        GiftFrom giftFrom = GiftFrom.findByCode(from);
        if (Objects.isNull(giftFrom) || !GIVE_HYS_GIFT_FROM.contains(giftFrom.getScene())) {
            return false;
        }
        return true;
    }

    private boolean addGiveGiftSet(Long uid, String key) {
        redisManager.sSetExpire(String.format(LQYZ_GIVE_GIFT_SET, getToday(), uid), DateUtil.ONE_DAY_SECOND * 3, key);
        return true;
    }

    private boolean addReceiveGiftSet(Long uid, String key) {
        redisManager.sSetExpire(String.format(LQYZ_RECEIVE_GIFT_SET, getToday(), uid), DateUtil.ONE_DAY_SECOND * 3, key);
        return true;
    }

    private boolean giveGiftContain(Long uid, String key) {
        return redisManager.setIsMember(String.format(LQYZ_GIVE_GIFT_SET, getToday(), uid), key);
    }

    private boolean receiveGiftContain(Long uid, String key) {
        return redisManager.setIsMember(String.format(LQYZ_RECEIVE_GIFT_SET, getToday(), uid), key);
    }

    public boolean dailySettlementHandle() {
        if (!activityIsEnable()) {
            return false;
        }
        // 遍历整个榜单计算隐藏任务
        String rankDate;
        if (isMonday()) {
            rankDate = getLastWeekFirstDay();
        } else {
            rankDate = getWeekFirstDay();
        }
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(String.format(FAMILY_GOLD_RANK, rankDate), 0, Double.MAX_VALUE);
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                continue;
            }
            Long familyId = Long.valueOf(typedTuple.getValue().toString());
            FamilyVO familyInfo = getFamilyInfo(familyId);

            // 获取昨日发言人数
            Integer personNum = getFamilyPersonNum(familyId, getYesterday());
            log.info("familyKey 日结算 familyId:{}, personNum:{}", familyId, personNum);
            FamilyTaskEnum belongInstance = FamilyTaskEnum.getBelongInstance(personNum);
            if (Objects.nonNull(belongInstance)) {
                log.info("familyKey 完成家族隐藏任务 familyId:{}, personNum:{}, task:{}", familyId, personNum, JSONObject.toJSONString(belongInstance));
                incrFamilyGoldNum(familyInfo, null, belongInstance.getPrizeNum());
            }
        }
        return true;
    }

    public boolean weeklySettlementHandle() {
        if (!activityIsEnable()) {
            return false;
        }
        String rankDate;
        if (isMonday()) {
            rankDate = getLastWeekFirstDay();
        } else {
            rankDate = getWeekFirstDay();
        }
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(String.format(FAMILY_GOLD_RANK, rankDate), 0, Double.MAX_VALUE, 0, 3);
        int i = 0;
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                continue;
            }
            ++i;
            Long familyId = Long.parseLong(typedTuple.getValue().toString());
            FamilyVO familyInfo = getFamilyInfo(familyId);
            log.info("familyKey 周结算 familyId:{}, userId:{}, rank:{}", familyInfo, familyInfo.getOwner().getId(), i);
            // 埋点
            Map<String, Object> params = new HashMap<>();
            params.put("uid", familyInfo.getOwner().getId());
            params.put("family_id", familyId);
            params.put("result", i);
            yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), familyInfo.getOwner().getId(), "send_familypk_honour", params, ServicesNameEnum.ump_services.getCode());
            switch (i) {
                case 1:
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.RANK_1.getSceneCode(), familyInfo.getOwner().getId());
                    npcNotify(familyInfo.getOwner().getId(), "恭喜您的家族获得本周钥匙PK赛冠军！奖励3个为你摘星发放至您的背包（您可自由选择活跃用户赠送），继续保持噢！");
                    break;
                case 2:
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.RANK_2.getSceneCode(), familyInfo.getOwner().getId());
                    npcNotify(familyInfo.getOwner().getId(), "恭喜您的家族获得本周钥匙PK赛亚军！奖励3个陪你看雪发放至您的背包（您可自由选择活跃用户赠送），继续加油吧！");
                    break;
                case 3:
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.RANK_3.getSceneCode(), familyInfo.getOwner().getId());
                    npcNotify(familyInfo.getOwner().getId(), "恭喜您的家族获得本周钥匙PK赛季军！奖励3个粉红皇冠发放至您的背包（您可自由选择活跃用户赠送），继续冲冲冲！");
                    break;
            }
        }
        return true;
    }


    private boolean incrFamilyGoldNum(FamilyVO familyInfoVO, Long uid, Integer num) {
        Double after = redisManager.zIncrby(String.format(FAMILY_GOLD_RANK, getWeekFirstDay()), familyInfoVO.getId(), num.doubleValue(), DateUtil.ONE_DAY_SECOND * 30);
        Double before = after - num;
        log.info("familyKey 榜单更新 familyId:{}, uid:{}, num:{}, after:{}", familyInfoVO.getId(), uid, num, after);
        if (Objects.nonNull(uid)) {
            redisManager.zIncrby(String.format(FAMILY_MEMBER_GOLD_RANK, getWeekFirstDay(), familyInfoVO.getId()), uid, num.doubleValue(), DateUtil.ONE_DAY_SECOND * 30);
        }
        // 达成成就判定
        List<FamilyAchievementEnum> achievementList = FamilyAchievementEnum.getAchievementList(before.intValue(), after.intValue());
        if (!CollectionUtils.isEmpty(achievementList)) {
            for (FamilyAchievementEnum item : achievementList) {
                log.info("familyKey 达成家族成就 familyId:{}, level:{}", familyInfoVO.getId(), item.getAchievementLevel());
                completeFamilyAchievement(familyInfoVO, item);
            }
        }
        return true;
    }

    private List<Boolean> getFamilyAchievementSendLog(Long familyId) {
        List<Boolean> logs = (List<Boolean>) redisManager.hget(String.format(FAMILY_ACHIEVEMENT_LEVEL_SEND_PRIZE_LOG, getWeekFirstDay()), familyId.toString());
        if (Objects.isNull(logs)) {
            logs = Lists.newArrayList(false, false, false, false, false);
            redisManager.hset(String.format(FAMILY_ACHIEVEMENT_LEVEL_SEND_PRIZE_LOG, getWeekFirstDay()), familyId.toString(), logs, DateUtil.ONE_DAY_SECOND * 24);
        }
        return logs;
    }

    private boolean completeValid(Long familyId, FamilyAchievementEnum item) {
        List<Boolean> sendLogs = getFamilyAchievementSendLog(familyId);
        if (Boolean.FALSE.equals(sendLogs.get(item.getLogIndex()))) {
            return true;
        }
        return false;
    }

    private boolean completeFamilyAchievement(FamilyVO familyInfoVO, FamilyAchievementEnum item) {
        // 验证是否可下发
        if (!completeValid(familyInfoVO.getId(), item)) {
            return false;
        }
        boolean result = false;
        Lock lock = redissonClient.getLock(String.format(FAMILY_ACHIEVEMENT_SEND_LOCK, familyInfoVO.getId(), item.getAchievementLevel()));
        try {
            lock.lock();
            // 二次验证
            if (!completeValid(familyInfoVO.getId(), item)) {
                return false;
            }
            // 下发空投
            lanlingRemoteService.callAirdrop(familyInfoVO.getId(), item.getAirdropId());
            // 埋点
            Map<String, Object> ktzd1Params = new HashMap<>();
            ktzd1Params.put("from", getActivityCode());
            ktzd1Params.put("type", "system");
            ktzd1Params.put("family_id", familyInfoVO.getId());
            ktzd1Params.put("airdrop_name", item.getAirdropName());
            ktzd1Params.put("count", -1);
            ktzd1Params.put("result", -1);
            yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), familyInfoVO.getId(), "call_airdrop_success_activity", ktzd1Params, ServicesNameEnum.ump_services.getCode());
            // 家族宝箱
//            feignLanlingService.updateFamilyBoxTodayOriginalCoin(familyInfoVO.getId(), item.getCoin().longValue());
            // 飘屏通知
            String msg = String.format("\uD83D\uDD11恭喜 %s 家族成功进入 %s 级镇妖塔，获得 %s 级宝物奖励\uD83D\uDC4F\uD83D\uDC4F", familyInfoVO.getName(), item.getAchievementLevel(), item.getAchievementLevel());
            sendGiftBarrage(999L, msg);
            // 家族聊天页系统通知
            String msg2 = String.format("恭喜家族闯关成功，成功进入 %s 级镇妖塔，获得妖物进贡的 %s 金币，已计入家族宝箱，记得签到领取哦！", item.getAchievementLevel(), item.getCoin());
            SystemMessageModel smm = new SystemMessageModel();
            smm.setType(ImMessageType.system_msg.getCode());
            smm.setText(msg2);
            Long familyChatId = feignImService.getTeamChatByImTid(familyInfoVO.getImTid()).successData();
            feignImService.sendTeamCustomSystemMsg(familyChatId, familyInfoVO.getOwner().getId(), ServicesAppIdEnum.lanling.getAppId(), JSONObject.toJSONString(smm));
            // 更新下发记录
            List<Boolean> logs = getFamilyAchievementSendLog(familyInfoVO.getId());
            logs.set(item.getLogIndex(), true);
            redisManager.hset(String.format(FAMILY_ACHIEVEMENT_LEVEL_SEND_PRIZE_LOG, getWeekFirstDay()), familyInfoVO.getId().toString(), logs, DateUtil.ONE_DAY_SECOND * 24);
            // 更新家族成就等级
            setFamilyAchievementLevel(familyInfoVO.getId(), item.getAchievementLevel());
            result = true;
            log.info("familyKey 下发家族成就奖励成功 familyId:{}, level:{}", familyInfoVO.getId(), item.getAchievementLevel());
        } catch (Exception e) {
            log.error("familyKey 下发家族成就奖励异常 familyId:{}, level:{}", familyInfoVO.getId(), item.getAchievementLevel(), e);
        } finally {
            lock.unlock();
        }
        return result;
    }

    private boolean completePersonalTask(Long uid, PersonalTaskEnum item) {
        return completePersonalTask(uid, item, null);
    }

    private String getJumpText() {
        return String.format("<a href=\"%s\">\uD83D\uDC49 点击查看已获得钥匙数\uD83D\uDC48</a>", LanlingActivityUtil.getH5BaseUrl(env) + getActivityUrl(1));
    }

    private String getJumpText2() {
        return String.format("<a href=\"%s\">\uD83D\uDC49 查看详情\uD83D\uDC48</a>", LanlingActivityUtil.getH5BaseUrl(env) + getActivityUrl(1));
    }

    private boolean pairValid() {
        Long num = redisManager.incrLong(GET_HYS_GIFT_NUM, 1, RedisManager.ONE_DAY_SECONDS * 15);
        Long current = num % 10;
        if (Objects.equals(0L, current) || Objects.equals(4L, current)) {
            return false;
        }
        return true;
    }

    private boolean updateTaskCompleteTimes(Long uid, PersonalTaskEnum item) {
        return updateTaskCompleteTimes(uid, item, null);
    }

    /**
     * 时间赶写的垃圾，请勿借鉴
     * <p>
     * 返回 true 将导致调用者进入完成任务处理流程，返回 false 则会中断，任何修改请对以下 case 进行验证
     * <p>
     * case1: 当前钥匙 = 上限 => 返回 false（任务已达上限，不再继续流转）
     * case2: 当前钥匙 + 获得钥匙 > 上限 => 返回 true，keyNum 为 targetTimes - times
     * case3: 话题担当任务更新失败 => 返回 false
     *
     * @param uid
     * @param item
     * @param bizParams
     * @return boolean
     */
    private boolean updateTaskCompleteTimes(Long uid, PersonalTaskEnum item, Map<String, Object> bizParams) {
        List<Integer> personalTaskCompleteTimes = getPersonalTaskCompleteTimes(uid);
        Integer times = personalTaskCompleteTimes.get(item.getIndex());
        // 已完成直接返回
        if (Objects.equals(times, item.getLimitTimes())) {
            return false;
        }
        // 获取目标完成次数
        Integer targetTimes = times + (PersonalTaskEnum.LQYZ_1.equals(item) ? getLqyz1NumPerComplete(uid, ((Long) bizParams.get("coin")).intValue()) : item.getNumPerComplete());
        log.info("debug1 - targetTimes:{}", targetTimes);
        // 防止完成次数溢出
        if (targetTimes > item.getLimitTimes()) {
            targetTimes = item.getLimitTimes();
        }
        log.info("debug2 - targetTimes:{}", targetTimes);
        // 活跃担当任务特殊处理
        if (PersonalTaskEnum.HTDD.equals(item) && !setFamilySpeakTimeRange(uid)) {
            return false;
        }
        // 计算防溢出处理后的下发钥匙数量
        log.info("debug3 - targetTimes:{}, times:{}", targetTimes, times);
        if (Objects.nonNull(bizParams)) {
            bizParams.put("keyNum", targetTimes - times);
        }
        personalTaskCompleteTimes.set(item.getIndex(), targetTimes);
        setPersonalTaskCompleteTimes(uid, personalTaskCompleteTimes);
        return true;
    }

    private boolean completePersonalTask(Long uid, PersonalTaskEnum item, Map<String, Object> bizParams) {
        log.info("完成个人任务 uid:{}, item:{}, bizParams:{}", uid, item, bizParams);
        switch (item) {
            case GAXR:
                boolean gaxrFlag = pairValid();
                if (gaxrFlag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("❤️  感谢你照顾家族新人，家族送你 1 把灰钥匙哦~\n集齐十把金钥匙，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_GAXR.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 1);
                }
                // 埋点
                Map<String, Object> params = new HashMap<>();
                params.put("gift_key", "LITTLE_FLOWER");
                params.put("result", gaxrFlag ? 1 : 0);
                params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "give_newbe_GIFT", params, ServicesNameEnum.ump_services.getCode());
                break;
            case HTDD:
                boolean htddFlag = pairValid();
                if (htddFlag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("嘟嘟，感谢你为家族带来活跃，家族送你 1 把灰钥匙。\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_HTDD.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 1);
                }
                // 埋点
                Map<String, Object> htddParams = new HashMap<>();
                htddParams.put("uid", uid);
                htddParams.put("from", getActivityCode());
                htddParams.put("result", htddFlag ? 1 : 0);
                htddParams.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "family_send_messag_activity", htddParams, ServicesNameEnum.ump_services.getCode());
                break;
            case HYDD:
                if (updateTaskCompleteTimes(uid, item)) {
                    // 下发奖励
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_HYDD.getSceneCode(), uid);
                    // 弹窗通知
                    notifyComponent.serverPush(WindowParam.getInstance(uid, "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/res/activity/familyKey/family_key_window_bg.png", "https://nuanchat.oss-cn-hangzhou.aliyuncs.com/res/activity/familyKey/family_key_window_button.png"));
                    getHysTrack(uid, getFamilyByUid(uid), 5);
                }
                // 埋点
                Map<String, Object> hyddParams = new HashMap<>();
                hyddParams.put("uid", uid);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "leave_family_chat_3min_screen", hyddParams, ServicesNameEnum.ump_services.getCode());
                break;
            case YCDQ:
                if (updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("恭喜拍卖成功，感谢你为家族建立更多友好关系，家族送你 10 把灰钥匙哦~\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_YCDQ.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 10);
                }
                // 埋点
                Map<String, Object> ycdqParams = new HashMap<>();
                ycdqParams.put("type", Optional.ofNullable(bizParams.get("type")).orElse(""));
                ycdqParams.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                ycdqParams.put("result", 10);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "relationship_witness_KEY2.0_activity", ycdqParams, ServicesNameEnum.ump_services.getCode());
                break;
            case ZMDZF:
                if (updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("你的祝福新人都收到啦！感谢你的祝福，家族送你 10 把灰钥匙哦~\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_ZMDZF.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 10);
                }
                // 埋点
                Map<String, Object> zmdzfParams = new HashMap<>();
                zmdzfParams.put("result", 10);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "marry_room_spend_KEY2.0_activity", zmdzfParams, ServicesNameEnum.ump_services.getCode());
                break;
            case KTZD_1:
                boolean ktzd1Flag = pairValid();
                if (ktzd1Flag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("谢谢壕主的礼物，感谢为家族带来活跃，家族送你 5 把灰钥匙嗷~\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_KTZD_1.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 5);

                }
                // 埋点
                Map<String, Object> ktzd1Params = new HashMap<>();
                ktzd1Params.put("from", getActivityCode());
                ktzd1Params.put("type", Optional.ofNullable(bizParams.get("type")).orElse(""));
                ktzd1Params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                ktzd1Params.put("airdrop_name", bizParams.get("airdropName"));
                ktzd1Params.put("count", Optional.ofNullable(bizParams.get("count")).orElse(-1L));
                ktzd1Params.put("result", ktzd1Flag ? 5 : 0);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "call_airdrop_success_activity", ktzd1Params, ServicesNameEnum.ump_services.getCode());
                break;
            case KTZD_2:
                boolean ktzd2Flag = pairValid();
                if (ktzd2Flag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("蹭蹭你的好运，恭喜抢到空投！为家族带来好运，家族送你 1 把灰钥匙嗷~\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_KTZD_2.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 1);
                }
                // 埋点
                Map<String, Object> ktzd2Params = new HashMap<>();
                ktzd2Params.put("from", getActivityCode());
                ktzd2Params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                ktzd2Params.put("airdrop_name", bizParams.get("airdropName"));
                ktzd2Params.put("result", ktzd2Flag ? 1 : 0);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "scramble_airdrop_KEY2.0_activity", ktzd2Params, ServicesNameEnum.ump_services.getCode());
                break;
            case LQYZ_1:
                // 金币大于 5200 不走概率
                Long coin = (Long) bizParams.get("coin");
                boolean lqyz1Flag;
                if (Objects.nonNull(coin) && coin >= 5200) {
                    lqyz1Flag = true;
                } else {
                    lqyz1Flag = pairValid();
                }
                Integer keyNum = 0;
                bizParams.put("keyNum", keyNum);
                if (lqyz1Flag && updateTaskCompleteTimes(uid, item, bizParams)) {
                    keyNum = (Integer) bizParams.get("keyNum");
                    // 第二次优化改动，动态获取奖励数量
                    npcNotify(uid, String.format("叮，成功送出 %s 礼物，为家族积累财富\n 家族送你 %s 把灰钥匙\n 集齐十把，即可进入藏宝阁抽取宝物哦！%s", bizParams.get("giftName"), keyNum, getJumpText()));
                    sendPrizeComponent.sendGift(null, "HYS_GIFT", uid, new Long(keyNum), 15, getActivityCode() + "_activity");
                    // 埋点
                    Map<String, Object> lqyz1Params = new HashMap<>();
                    lqyz1Params.put("from", getActivityCode());
                    lqyz1Params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                    lqyz1Params.put("gift_name", Optional.ofNullable(bizParams.get("giftName")).orElse(""));
                    lqyz1Params.put("coin", Optional.ofNullable(bizParams.get("coin")).orElse(-1));
                    yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "send_KEY2.0_activity_gift", lqyz1Params, ServicesNameEnum.ump_services.getCode());
                    getHysTrack(uid, getFamilyByUid(uid), 2);
                }
                break;
            case LQYZ_2:
                boolean lqyz2Flag = pairValid();
                if (lqyz2Flag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("叮，成功收到 %s 礼物，为家族积累财富\n 家族送你 2 把灰钥匙\n 集齐十把，即可进入藏宝阁抽取宝物哦！%s", bizParams.get("giftName"), getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_LQYZ_2.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 2);
                    // 埋点
                    Map<String, Object> lqyz2Params = new HashMap<>();
                    lqyz2Params.put("from", getActivityCode());
                    lqyz2Params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                    lqyz2Params.put("gift_name", Optional.ofNullable(bizParams.get("giftName")).orElse(""));
                    lqyz2Params.put("coin", Optional.ofNullable(bizParams.get("coin")).orElse(-1));
                    yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "receive_KEY2.0_activity_gift", lqyz2Params, ServicesNameEnum.ump_services.getCode());
                }
                break;
            case SSYQW_1:
                boolean ssyqw1Flag = pairValid();
                if (ssyqw1Flag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("谢谢你的红包！为家族带来活跃，家族送你 5 把灰钥匙嗷~\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_SSYQW_1.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 5);
                }
                // 埋点
                Map<String, Object> ssyqw1Params = new HashMap<>();
                ssyqw1Params.put("coin", Optional.ofNullable(bizParams.get("coin")).orElse(-1));
                ssyqw1Params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                ssyqw1Params.put("result", ssyqw1Flag ? 5 : 0);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "red_packet_send_KEY2.0_activity", ssyqw1Params, ServicesNameEnum.ump_services.getCode());
                break;
            case SSYQW_2:
                boolean ssyqw2Flag = pairValid();
                if (ssyqw2Flag && updateTaskCompleteTimes(uid, item)) {
                    npcNotify(uid, String.format("恭喜抢到红包啦！你的好手气，为家族带来好运，家族送你 1 把灰钥匙\n集齐十把，即可进入藏宝阁抽取宝物哦！%s", getJumpText()));
                    sendPrizeComponent.sendPrize(getActivityCode(), SceneEnum.TASK_SSYQW_2.getSceneCode(), uid);
                    getHysTrack(uid, getFamilyByUid(uid), 1);
                }
                // 埋点
                Map<String, Object> ssyqw2Params = new HashMap<>();
                ssyqw2Params.put("family_id", Optional.ofNullable(bizParams.get("familyId")).orElse(-1L));
                ssyqw2Params.put("result", ssyqw2Flag ? 1 : 0);
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "red_packet_rush_KEY2.0_activity", ssyqw2Params, ServicesNameEnum.ump_services.getCode());
                break;
        }
        return true;
    }

    private Integer getLqyzPrizeNum() {

        return 0;
    }

    private Boolean chatNotify(Long fromUid, Long toUid, String msg) {
        SystemMessageModel smm = new SystemMessageModel();
        smm.setType(ImMessageType.system_msg.getCode());
        smm.setText(msg);
        feignImService.sendPrivateChatMsgBase64(fromUid, toUid, ServicesAppIdEnum.lanling.getAppId(), null, JSONObject.toJSONString(smm));
        return true;
    }

    private Boolean hasSendCountDownPrize(Long uid) {
        Long ts = (Long) redisManager.hget(String.format(TAKE_COUNT_DOWN_PRIZE, getToday()), uid.toString());
        if (Objects.nonNull(ts)) {
            return true;
        } else {
            return false;
        }
    }

    private Integer getFamilyPersonNum(Long familyId, String date) {
        if (Objects.isNull(familyId) || StringUtils.isBlank(date)) {
            return 0;
        }
        return Optional.ofNullable(feignLanlingService.getFamilySpeakCount(familyId, date).successData()).orElse(0);
    }

    private List<PrizeItem> getPrizeItems() {
        List<PrizeItem> prizeItemList = new ArrayList<>();
        List<ScenePrizeDO> prizeList = scenePrizeService.getListBySceneCode(getActivityCode(), "rank");
        for (ScenePrizeDO item : prizeList) {
            prizeItemList.add(PrizeItem.builder()
                    .prizeKey(item.getPrizeValue())
                    .prizeName(item.getPrizeDesc())
                    .effectiveDays(item.getPrizeEffectiveDay())
                    .valueGold(item.getPrizeValueGold().intValue()).build());
        }
        return prizeItemList;
    }

    private boolean cleanLucky(Long uid) {
        redisManager.hset(PERSONAL_LUCKY, uid.toString(), 0, DateUtil.ONE_DAY_SECOND * 24);
        return true;
    }

    private Integer initDrawPool() {
        // dcl 避免并发初始化
        Integer index = DrawPrizePoolEnum.BZ_GIFT.getMin();
        Lock lock = redissonClient.getLock("activity_gift_pool_init_lock");
        try {
            lock.lock();
            index = (Integer) redisManager.sPop(DRAW_PRIZE_POOL);
            if (Objects.isNull(index)) {
                // 奖池已空
                Integer[] giftPool = new Integer[DrawPrizePoolEnum.totalPoolNum];
                for (int j = 0; j < DrawPrizePoolEnum.totalPoolNum; j++) {
                    giftPool[j] = j;
                }
                redisManager.sSetExpire(DRAW_PRIZE_POOL, RedisManager.ONE_DAY_SECONDS * 90, giftPool);
                index = (Integer) redisManager.sPop(DRAW_PRIZE_POOL);
            }
        } catch (Exception e) {
            log.error("奖池初始化失败", e);
        } finally {
            lock.unlock();
        }
        return index;
    }

    private ScenePrizeDO getDrawPrizeItem() {
        // 随机获取奖品
        Integer giftIndex = (Integer) redisManager.sPop(DRAW_PRIZE_POOL);
        if (Objects.isNull(giftIndex)) {
            giftIndex = initDrawPool();
        }
        DrawPrizePoolEnum instance = DrawPrizePoolEnum.getInstance(giftIndex);
        if (Objects.isNull(instance)) {
            log.error("抽奖异常 index:{}", giftIndex);
            return null;
        }

        // 计算幸运值
        if (DrawPrizePoolEnum.PNKX_GIFT.getCode().equals(instance.getCode())) {
            cleanLucky(SecurityUtils.getCurrentUserIdLong());
        } else {
            incrLucky(SecurityUtils.getCurrentUserIdLong());
        }

        return ScenePrizeDO.builder()
                .activityCode(getActivityCode())
                .prizeDesc(instance.getDesc())
                .prizeType(instance.getType())
                .prizeSubType(instance.getSubType())
                .prizeNum(instance.getNum())
                .prizeEffectiveDay(instance.getEffectiveDays())
                .prizeValue(instance.getValue())
                .extData(instance.getExtData()).build();
    }

    private boolean incrLucky(Long uid) {
        if (redisManager.hasKey(PERSONAL_LUCKY)) {
            redisManager.hincr(PERSONAL_LUCKY, uid.toString(), 1,DateUtil.ONE_MONTH_SECOND);
        } else {
            redisManager.hset(PERSONAL_LUCKY, uid.toString(), 1, DateUtil.ONE_DAY_SECOND * 28);
        }
        return true;
    }

    private List<ScenePrizeDO> doDraw(Long uid, Integer times) {
        Map<String, ScenePrizeDO> giftMap = new HashMap<>(8);
        for (int i = 0; i < times; i++) {
            // 幸运值满
            Integer lucky = Optional.ofNullable((Integer) redisManager.hget(PERSONAL_LUCKY, uid.toString())).orElse(0);
            ScenePrizeDO prize;
            if (i == times - 1 && lucky >= getLuckyLimit()) {
                // 幸运值奖励
                prize = ScenePrizeDO.builder()
                        .activityCode(getActivityCode())
                        .prizeDesc(DrawPrizePoolEnum.PNKX_GIFT.getDesc())
                        .prizeType(DrawPrizePoolEnum.PNKX_GIFT.getType())
                        .prizeSubType(DrawPrizePoolEnum.PNKX_GIFT.getSubType())
                        .prizeNum(DrawPrizePoolEnum.PNKX_GIFT.getNum())
                        .prizeEffectiveDay(DrawPrizePoolEnum.PNKX_GIFT.getEffectiveDays())
                        .prizeValue(DrawPrizePoolEnum.PNKX_GIFT.getValue()).build();
                cleanLucky(SecurityUtils.getCurrentUserIdLong());
                Map<String, Object> params = new HashMap<>();
                params.put("uid", uid);
                params.put("type", times);
                params.put("from", getActivityCode());
                params.put("award", prize.getPrizeDesc());
                yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "activity_carPK_award", params, ServicesNameEnum.ump_services.getCode());
            } else {
                // 正常抽奖
                prize = getDrawPrizeItem();
            }
            putDrawPrize(giftMap, prize);
            log.info("familyKey 抽中奖品 uid:{}, giftKey:{}", uid, prize.getPrizeValue());
            Map<String, Object> params = new HashMap<>();
            params.put("uid", uid);
            params.put("type", times);
            params.put("from", getActivityCode());
            params.put("award", prize.getPrizeDesc());
            yzKafkaProducerManager.dataRangerTrack(MDCUtil.getCurAppIdByMdc(), uid, "activity_draw_family", params,ServicesNameEnum.ump_services.getCode());

            if (Objects.equals(KeyEnum.HYS.getCode(), prize.getPrizeValue())) {
                Long num = StringUtils.isNotBlank(prize.getPrizeSubType()) ? prize.getPrizeNum() * Long.valueOf(prize.getPrizeSubType()) : prize.getPrizeNum().longValue();
                getHysTrack(uid, getFamilyByUid(uid), num.intValue());
            }
        }
        return giftMap.values().stream().collect(Collectors.toList());
    }

    @Resource
    private DoDrawWithPoolStrategy doDrawWithPoolStrategy;
    @Resource
    private DrawPoolService drawPoolService;
    private static final Set LQYZ_1_COIN_SET = Sets.newHashSet(10, 25, 52, 66, 99, 188, 399, 520, 888, 999, 1314, 1999, 3344);

    /**
     * 获取礼轻义重任务完成奖励数量
     * <p>
     * 场景过于特殊且无通用性
     *
     * @return
     */
    public Integer getLqyz1NumPerComplete(Long uid, Integer coin) {
        if (Objects.isNull(coin)) {
            return 0;
        }
        if (LQYZ_1_COIN_SET.contains(coin)) {
            try {
                List<DrawPoolItemDO> prizes = doDrawWithPoolStrategy.draw(DrawParam.builder()
                        .appId(ServicesAppIdEnum.lanling.getAppId())
                        .uid(uid)
                        .activityCode(getActivityCode())
                        .type("lqyz")
                        .poolCode(String.format("lqyz_1_%s", coin))
                        .times(1).build());
                return prizes.get(0).getItemNum();
            } catch (Exception e) {
                log.error("familyKey礼轻义重奖池抽奖失败 uid:{}, coin:{}", uid, coin, e);
                return 0;
            }
        } else if (coin >= 5200) {
            return 24;
        } else {
            return 2;
        }
    }

    private List<PrizeItem> convert2PrizeItem(List<ScenePrizeDO> params) {
        List<PrizeItem> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(params)) {
            for (ScenePrizeDO item : params) {
                list.add(PrizeItem.builder()
                        .prizeKey(item.getPrizeValue())
                        .effectiveDays(item.getPrizeEffectiveDay())
                        .prizeNum(item.getPrizeNum())
                        .prizeName(item.getPrizeDesc()).build());
            }
        }
        return list;
    }

    private List<FamilyItem> getFamilyItems() {
        // 获取家族列表
        List<FamilyItem> familyItemList = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(String.format(FAMILY_GOLD_RANK, getWeekFirstDay()), 0, Double.MAX_VALUE, 0, FAMILY_GOLD_RANK_SHOW_NUM);
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                continue;
            }
            Long familyId = Long.parseLong(typedTuple.getValue().toString());
            Integer goldNum = typedTuple.getScore().intValue();

            FamilyItem familyItem = FamilyItem.builder()
                    .familyId(familyId)
                    .familyGoldKeyNum(goldNum).build();
            familyItemList.add(familyItem);
        }

        // 获取家族信息
        familyItemList.parallelStream().forEach(item -> {
            FamilyVO familyInfo = getFamilyInfo(item.getFamilyId());
            item.setFamilyName(familyInfo.getName());
            item.setFamilyAvatar(familyInfo.getIcon());
            item.setAchievementLevel(getFamilyAchievementLevel(item.getFamilyId()));
            item.setMemberItemList(getMemberItemList(familyInfo.getId()));
        });
        return familyItemList;
    }

    private List<MemberItem> getMemberItemList(Long familyId) {
        List<MemberItem> memberItemList = new ArrayList();
        List<Long> ids = new ArrayList<>();
        // 获取用户 ID
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(String.format(FAMILY_MEMBER_GOLD_RANK, getWeekFirstDay(), familyId), 0, Double.MAX_VALUE, 0, 3);
        for (ZSetOperations.TypedTuple<Object> typedTuple : typedTuples) {
            if (null == typedTuple.getValue() || null == typedTuple.getScore()) {
                continue;
            }
            Long userId = Long.parseLong(typedTuple.getValue().toString());
            ids.add(userId);
            memberItemList.add(MemberItem.builder().userId(userId).build());
        }
        // 批量查询拼装
        if (StringUtils.isNotBlank(ids.toString())) {
            Map<Long, UserVO> userVOMap = userRemoteService.acquireUsersInBulk(ids, true);
            memberItemList.stream().forEach(item -> {
                item.setUserAvatar(userVOMap.get(item.getUserId()).getAvatar());
            });
        }
        return memberItemList;
    }

    private void putDrawPrize(Map<String, ScenePrizeDO> giftMap, ScenePrizeDO item) {
        ScenePrizeDO giftItem = giftMap.get(item.getPrizeValue());
        if (Objects.isNull(giftItem)) {
            giftMap.put(item.getPrizeValue(), item);
        } else {
            giftItem.setPrizeNum(giftItem.getPrizeNum() + item.getPrizeNum());
        }
    }

    private FamilyVO getFamilyInfo(Long familyId) {
        FamilyVO familyVO;
        String cache = (String) redisManager.get(String.format(FAMILY_INFO_CACHE, familyId));
        if (StringUtils.isBlank(cache)) {
            familyVO = feignLanlingService.familyInfo(familyId).successData();
            if (Objects.nonNull(familyVO) && Objects.nonNull(familyVO.getId())) {
                redisManager.set(String.format(FAMILY_INFO_CACHE, familyId), JSONObject.toJSONString(familyVO), DateUtil.ONE_DAY_SECOND);
            }
        } else {
            familyVO = JSONObject.parseObject(cache, FamilyVO.class);
        }
        return familyVO;
    }

    private List<Integer> getPersonalTaskCompleteTimes(Long uid) {
        List<Integer> taskStatus = (List<Integer>) redisManager.hget(String.format(PERSONAL_TASK_COMPLETE_TIMES, getToday()), uid.toString());
        if (CollectionUtils.isEmpty(taskStatus)) {
            taskStatus = Lists.newArrayList(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
            redisManager.hset(String.format(PERSONAL_TASK_COMPLETE_TIMES, getToday()), uid.toString(), taskStatus, DateUtil.ONE_DAY_SECOND * 24);
        }
        return taskStatus;
    }

    private boolean setPersonalTaskCompleteTimes(Long uid, List<Integer> list) {
        redisManager.hset(String.format(PERSONAL_TASK_COMPLETE_TIMES, getToday()), uid.toString(), list, DateUtil.ONE_DAY_SECOND * 24);
        return true;
    }

    private Integer getFamilyRank(Long familyId) {
        Long rank = redisManager.reverseRank(String.format(FAMILY_GOLD_RANK, getWeekFirstDay()), familyId);
        if (Objects.isNull(rank)) {
            return 0;
        }
        return rank.intValue() + 1;
    }

    private Integer getFamilyGoldNum(Long familyId) {
        return Optional.ofNullable(redisManager.score(String.format(FAMILY_GOLD_RANK, getWeekFirstDay()), familyId)).orElse(0d).intValue();
    }

    private Integer getPersonalLucky(Long uid) {
        return Optional.ofNullable((Integer) redisManager.hget(PERSONAL_LUCKY, uid.toString())).orElse(0);
    }

    private List<PrizeItem> getDrawPrizeList() {
        List<ScenePrizeDO> prizeList = scenePrizeService.getListBySceneCode(getActivityCode(), "getMissionList");
        List<PrizeItem> prizeItemList = new ArrayList<>();
        for (ScenePrizeDO item : prizeList) {
            prizeItemList.add(PrizeItem.builder()
                    .prizeKey(item.getPrizeValue())
                    .prizeName(item.getPrizeDesc())
                    .effectiveDays(item.getPrizeEffectiveDay())
                    .valueGold(item.getPrizeValueGold().intValue()).build());
        }
        return prizeItemList;
    }

    private List<AchievementItem> getAchievementItems(Long familyId) {
        List<AchievementItem> achievementItemList = new ArrayList<>();
        for (FamilyAchievementEnum item : FamilyAchievementEnum.values()) {
            achievementItemList.add(AchievementItem.builder()
                    .achievementLevel(item.getAchievementLevel())
                    .airdropName(item.getAirdropName())
                    .airdropNum(item.getAirdropNum())
                    .airdropValueGold(item.getAirdropValueGold())
                    .coin(item.getCoin())
                    .limitDesc(item.getLimitDesc())
                    .status(Objects.isNull(familyId) ? 0 : getStatus(familyId, item.getAchievementLevel())).build());
        }
        return achievementItemList;
    }

    private Integer getFamilyAchievementLevel(Long familyId) {
        return Optional.ofNullable((Integer) redisManager.hget(String.format(FAMILY_ACHIEVEMENT_LEVEL, getWeekFirstDay()), familyId.toString())).orElse(0);
    }

    public boolean setFamilyAchievementLevel(Long familyId, Integer level) {
        redisManager.hset(String.format(FAMILY_ACHIEVEMENT_LEVEL, getWeekFirstDay()), familyId.toString(), level, DateUtil.ONE_DAY_SECOND * 24);
        return true;
    }

    private Integer getStatus(Long familyId, Integer level) {
        Integer currentLevel = getFamilyAchievementLevel(familyId);
        if (currentLevel >= level) {
            return 1;
        } else {
            return 0;
        }
    }

    private List<FamilyTaskItem> getFamilyTaskItems() {
        List<FamilyTaskItem> familyTaskItemList = new ArrayList<>();
        for (FamilyTaskEnum item : FamilyTaskEnum.values()) {
            familyTaskItemList.add(FamilyTaskItem.builder()
                    .personNum(item.getPersonNum())
                    .prizeKey(item.getPrizeKey())
                    .prizeName(item.getPrizeName())
                    .prizeNum(item.getPrizeNum()).build());
        }
        return familyTaskItemList;
    }

    private Integer getLuckyLimit() {
        String limitStr = (String) redisManager.get(PERSONAL_LUCKY_LIMIT);
        if (StringUtils.isBlank(limitStr)) {
            return 1314;
        }
        return Integer.parseInt(limitStr);
    }

    private void deductKeyNum(Long uid, KeyEnum key, Integer num) {
        List<UsePackageDTO> usePackageList = userPackageFeignService.usePackageWithBizId(ServicesAppIdEnum.lanling.getAppId(), ServicesAppIdEnum.lanling.getUnionId(), uid,
                key.getCode(), num.longValue(), PackageUseScene.activity.getCode());
        if (CollectionUtils.isEmpty(usePackageList)) {
            log.error("抽奖扣减金钥匙失败 uid:{}", uid);
            throw new ServiceException(ACTIVITY_ERROR_180018);
        }
    }

    private void checkKeyNum(Long uid, Integer num) {
        Map<KeyEnum, Integer> keyNumMap = getKeyNum(uid);
        Integer greyKeyNum = Optional.ofNullable(keyNumMap.get(KeyEnum.HYS)).orElse(0);
        Integer goldKeyNum = Optional.ofNullable(keyNumMap.get(KeyEnum.JYS)).orElse(0);
        if (goldKeyNum < num) {
            if (greyKeyNum > 0) {
                throw new ServiceException(ACTIVITY_ERROR_180019);
            } else {
                throw new ServiceException(ACTIVITY_ERROR_180018);
            }
        }
    }

    private Map<KeyEnum, Integer> getKeyNum(Long uid) {
        Map<KeyEnum, Integer> defaultMap = new HashMap<>();
        defaultMap.put(KeyEnum.JYS, 0);
        defaultMap.put(KeyEnum.HYS, 0);
        PackageQueryConditionDTO queryParam = PackageQueryConditionDTO.builder().uid(uid).packageScene(UserPackageScene.FAMILY_KEY_ACTIVITY.getCode()).build();
        List<UserPackageDetailDTO> packageList = userPackageFeignService.getPackageDetailByCondition(queryParam).successData();
        if (CollectionUtils.isEmpty(packageList)) {
            return defaultMap;
        }
        Map<String, List<UserPackageDetailDTO>> userPackageMap = packageList.stream().collect(Collectors.groupingBy(UserPackageDetailDTO::getBizId));
        userPackageMap.forEach((key, list) -> {
            Long num = list.stream().mapToLong(UserPackageDetailDTO::getAvailableNum).sum();
            defaultMap.put(KeyEnum.getInstance(key), num.intValue());
        });
        return defaultMap;
    }

    private boolean needNotify() {
        LocalTime localTime = getNow().toLocalTime();
        if (isSunday() && localTime.isAfter(NOTIFY_TIME_RANGE_START) && localTime.isBefore(NOTIFY_TIME_RANGE_ENT)) {
            return true;
        }
        return false;
    }

    private Long getDefaultFamilyId() {
        ThreadLocalRandom threadLocalRandom = ThreadLocalRandom.current();
        // 获取排行榜第 10 名家族信息
        Long targetFamilyId = -1L;
        int time = 0;
        while (Objects.equals(targetFamilyId, -1L)) {
            int rank;
            if (time == 10) {
                rank = 1;
            } else if (time >= 20) {
                return -1L;
            } else {
                rank = threadLocalRandom.nextInt(1, 21);
            }
            Long familyId = -1L;
            Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisManager.reverseRangeByScoreWithScores(String.format(FAMILY_GOLD_RANK, getWeekFirstDay()), 0, Double.MAX_VALUE, rank - 1, 1);
            for (ZSetOperations.TypedTuple<Object> item : typedTuples) {
                if (item.getValue() == null || item.getScore() == null) {
                    continue;
                }
                familyId = Long.parseLong(item.getValue().toString());
            }
            if (Objects.equals(familyId, -1L)) {
                time++;
                continue;
            }
            FamilyVO familyInfo = getFamilyInfo(familyId);
            if (Objects.nonNull(familyInfo) && CommonBooleanStatus.yes.getCode().equals(familyInfo.getOpenCode())) {
                targetFamilyId = familyId;
            }
        }
        return targetFamilyId;
    }

    private FamilyVO getFamilyByUid(Long uid) {
        FamilyVO family = null;
        String familyId = (String) redisManager.hget(String.format(FAMILY_MEMBER_INFO_CACHE, getToday()), uid.toString());
        if (StringUtils.isNotBlank(familyId)) {
            family = getFamilyInfo(Long.valueOf(familyId));
        } else {
            FamilyInfoVO familyInfoVO = feignLanlingService.getFamilyInfoByUid(uid).successData();
            if (Objects.nonNull(familyInfoVO) && Objects.nonNull(familyInfoVO.getId())) {
                UserBaseVO owner = new UserBaseVO();
                owner.setId(familyInfoVO.getOwnerUid());
                family = new FamilyVO();
                family.setId(familyInfoVO.getId());
                family.setName(familyInfoVO.getName());
                family.setIcon(familyInfoVO.getAvatar());
                family.setChatId(familyInfoVO.getChatId());
                family.setImTid(familyInfoVO.getImTid());
                family.setOpenCode(familyInfoVO.getOpenCode());
                family.setOwner(owner);
                redisManager.hset(String.format(FAMILY_MEMBER_INFO_CACHE, getToday()), uid.toString(), familyInfoVO.getId().toString());
            }
        }
        log.info("根据用户查询家族 uid:{}, family:{}", uid, Optional.ofNullable(family).map(item -> item.getId()).orElse(null));
        return family;
    }

    @Override
    protected String getActivityCode() {
        return "family_key";
    }

    @Override
    protected String getActivityUrl(Integer index) {
        return "family-key-game?from=npc_message&active=" + index;
    }
}
