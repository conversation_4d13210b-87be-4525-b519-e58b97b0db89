package cn.yizhoucp.ump.biz.project.biz.manager.activity.plantTrees;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.enums.ActivityCheckListEnum;
import cn.yizhoucp.ump.biz.project.biz.constant.PlantTreesConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolItemService;
import cn.yizhoucp.ump.biz.project.dal.service.DrawPoolService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PlantTreesDrawManager extends AbstractDrawTemplate {

    @Resource
    private DrawPoolService drawPoolService;
    @Resource
    private DrawPoolItemService drawPoolItemService;
    @Resource
    private ProbStrategy probStrategy;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        // 送礼任务初始化不做验证
        if (StringUtils.equals(PlantTreesConstant.TASK_KEY.sendGift1.name(), drawParam.getPoolCode())
                || StringUtils.equals(PlantTreesConstant.TASK_KEY.sendGift1.name(), drawParam.getPoolCode())) {
            return;
        }

    }

    @Override
    protected void draw(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();

        // 获取奖池配置信息
        DrawPoolDO drawPoolDO = drawPoolService.getByActivityCodeAndPoolCodeAndEnable(drawParam.getActivityCode(), drawParam.getPoolCode());
        context.setDrawPoolDO(drawPoolDO);
        // 获取奖品列表
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemService.getByPoolCode(drawPoolDO.getPoolCode());
        context.setDrawPoolItemDOS(drawPoolItemDOList);
        // 获取抽奖结果
        List<DrawPoolItemDTO> prizeItems = probStrategy.getDrawPoolItems(context);
        context.setPrizeItemList(prizeItems);
    }

    @Override
    protected void sendPrize(DrawParam param, List<DrawPoolItemDTO> prizeList) {
    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {

    }
}
