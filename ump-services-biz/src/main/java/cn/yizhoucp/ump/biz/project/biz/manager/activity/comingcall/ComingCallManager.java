package cn.yizhoucp.ump.biz.project.biz.manager.activity.comingcall;

import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.buoyBar.ChatBarVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.AbstractActivityManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.BuoyBarManager;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @date 2025-01-24 11:43
 */
@Slf4j
@Component
public class ComingCallManager extends AbstractActivityManager implements BuoyBarManager {


    @Override
    public ChatBarVO getBuoyBar(BaseParam param, Long toUid) {
        ChatBarVO build = ChatBarVO.builder()
                .isShow(Boolean.TRUE)
                .icon("https://res-cdn.nuan.chat/res/common/image/679482ab6ad9910f.png")
                .href(getActivityUrl(param, "buoyBar") + "&otherUid=" + toUid)
                .value(null)
                .maxValue(null)
                .toUid(toUid)
                .valueName("")
                .build();
        return build;
    }

    @Override
    public String getActivityCode() {
        return "call_me_say_love";
    }
}
