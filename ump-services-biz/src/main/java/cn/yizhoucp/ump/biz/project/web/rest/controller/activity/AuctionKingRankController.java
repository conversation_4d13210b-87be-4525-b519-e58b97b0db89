package cn.yizhoucp.ump.biz.project.web.rest.controller.activity;

import cn.yizhoucp.ms.core.base.Result;
import cn.yizhoucp.ms.core.base.auth.SecurityUser;
import cn.yizhoucp.ms.core.base.auth.SecurityUtils;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.auctionKing.AuctionKingRankManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 拍卖争霸接口
 * @date 2023-10-19 14:42
 */
@RestController
public class AuctionKingRankController {

    @Resource
    private AuctionKingRankManager auctionKingRankManager;

    @RequestMapping("/api/inner/activity/auction-king/get-rank")
    public Result<RankVO> getRank(String type) {
        SecurityUser currentUser = SecurityUtils.getCurrentUser();
        BaseParam build = BaseParam.builder().uid(currentUser.getUserId()).unionId(currentUser.getUnionId()).appId(currentUser.getAppId()).vestChannel(currentUser.getUserVestChannel()).build();
        return Result.successResult(auctionKingRankManager.getRank(RankContext.builder()
                .param(build)
                .activityCode(AuctionKingRankManager.ACTIVITY_CODE)
                .rankKey(type)
                .type(RankContext.RankType.user)
                .build()));
    }


    @RequestMapping("/api/inner/activity/auction-king/add")
    public Result<RankVO> add(Long userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("winnerUserId", userId);
        jsonObject.put("auctionedUserId", 20004691L);
        jsonObject.put("count", 400);
        auctionKingRankManager.auctionSuccess(null,jsonObject);
        return Result.successResult(true);
    }


}
