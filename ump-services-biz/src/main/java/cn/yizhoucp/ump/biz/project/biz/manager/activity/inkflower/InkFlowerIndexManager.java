package cn.yizhoucp.ump.biz.project.biz.manager.activity.inkflower;

import cn.hutool.core.util.StrUtil;
import cn.yizhoucp.depth.api.client.DepthFeignService;
import cn.yizhoucp.depth.api.vo.CardiacRelationVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.annotation.NoRepeatSubmit;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.GraphIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.enums.ServicesNameEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.base.util.RiskUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.productServices.CoinGiftProductVO;
import cn.yizhoucp.ms.core.vo.productServices.HeadFrameProductVO;
import cn.yizhoucp.ms.core.vo.snsservices.relation.WrapUserVO;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.risk.api.client.RiskFeignService;
import cn.yizhoucp.risk.api.constant.RiskParam;
import cn.yizhoucp.risk.api.constant.RiskScene;
import cn.yizhoucp.risk.api.dto.RiskParamDTO;
import cn.yizhoucp.ump.api.vo.activity.Inkflower.InkFlowerIndexVO;
import cn.yizhoucp.ump.api.vo.activity.Inkflower.RollDiceReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.DrawReturn;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.api.vo.jimu.rank.RankVO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.journeyToTheWest.JourneyToTheWestConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.IndexManager;
import cn.yizhoucp.ump.biz.project.biz.manager.jimu.context.RankContext;
import cn.yizhoucp.ump.biz.project.biz.manager.sendPrize.SendPrizeManager;
import cn.yizhoucp.ump.biz.project.biz.remoteService.lanling.FeignLanlingService;
import cn.yizhoucp.ump.biz.project.biz.remoteService.product.FeignProductService;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawPoolItemJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.ScenePrizeJpaDAO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import cn.yizhoucp.user.manager.UserFeignManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 墨花月白长相思
 *
 * <AUTHOR>
 * @since 10:45 2025/1/16
 * @version V1.0
 */
@Service
@Slf4j
public class InkFlowerIndexManager implements IndexManager {

    @Resource
    private InkFlowerConstant inkFlowerConstant;

    @Resource
    private ScenePrizeJpaDAO scenePrizeJpaDAO;

    @Resource
    private SendPrizeManager sendPrizeManager;
    @Resource
    private LogComponent logComponent;
    @Resource
    private InkFlowerDrawManager inkFlowerDrawManager;
    @Resource
    private DrawPoolItemJpaDAO drawPoolItemJpaDAO;

    @Resource
    private FeignLanlingService feignLanlingService;
    @Resource
    private DepthFeignService depthFeignService;
    @Resource
    private UserFeignManager userFeignManager;
    @Resource
    private FeignProductService feignProductService;
    @Resource
    private InkFlowerRankManager inkFlowerRankManager;
    @Resource
    private InkFlowerTrackManager inkFlowerTrackManager;
    @Resource
    private RiskFeignService riskFeignService;

    @Override
    public InkFlowerIndexVO getIndex(BaseParam param, Long toUid, String extData) {
        InkFlowerIndexVO indexVO = new InkFlowerIndexVO();
        indexVO.setSnowSoundContent(buildSnowSoundContent(param));
        indexVO.setLoveStoryContent(buildLoveStoryContent(param, toUid));
        indexVO.setLongingForHomeContent(buildLongingForHomeContent(param));
        return indexVO;
    }

    private InkFlowerIndexVO.LongingForHomeContent buildLongingForHomeContent(BaseParam param) {
        InkFlowerIndexVO.LongingForHomeContent longingForHomeContent = new InkFlowerIndexVO.LongingForHomeContent();
        longingForHomeContent.setTasks(buildTasks(param));
        longingForHomeContent.setDiceRolling(buildDiceRolling(param));
        return longingForHomeContent;
    }

    private InkFlowerIndexVO.DiceRolling buildDiceRolling(BaseParam param) {
        InkFlowerIndexVO.DiceRolling diceRolling = new InkFlowerIndexVO.DiceRolling();
        Long diceNumber = inkFlowerConstant.getDiceNumber(param.getUid());
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), param.getUid());
        diceRolling.setTotalRolls(diceNumber.intValue());
        diceRolling.setNodes(buildNodes(param));
        diceRolling.setCurrentPosition(inkFlowerConstant.getRollDiceIndex(param.getUid()));
        diceRolling.setCanRollDice(diceNumber > 0);
        if (userVO != null) {
            diceRolling.setSex(userVO.getSex().getCode());
        }
        //初始化游戏币
        Integer drawCoin = inkFlowerConstant.getRollDiceCoin(param.getUid());
        Boolean isInitRollDiceCoin = inkFlowerConstant.isInitRollDiceCoin(param.getUid());
        if (drawCoin == 0 && !isInitRollDiceCoin) {
            inkFlowerConstant.initRollDiceCoin(param.getUid(), 20);
        }
        inkFlowerConstant.setInitRollDiceCoin(param.getUid());
        return diceRolling;
    }

    private List<InkFlowerIndexVO.Node> buildNodes(BaseParam param) {
        List<InkFlowerIndexVO.Node> nodes = new ArrayList<>();
        for (InkFlowerConstant.GiftMap giftMap : InkFlowerConstant.GiftMap.values()) {
            InkFlowerIndexVO.Node node = new InkFlowerIndexVO.Node();
            node.setIndex(giftMap.getItemId().intValue());
            node.setStart(giftMap.getItemId().equals(InkFlowerConstant.GiftMap.CELL_0.getItemId()));
            nodes.add(node);
        }
        return nodes;
    }

    private List<InkFlowerIndexVO.Task> buildTasks(BaseParam param) {
        List<InkFlowerIndexVO.Task> tasks = new ArrayList<>();
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), param.getUid());
        for (InkFlowerConstant.LongLingForHomeTaskEnum task : InkFlowerConstant.LongLingForHomeTaskEnum.values()) {
            tasks.add(buildTask(param, task, userVO, Boolean.FALSE));
            tasks.add(buildTask(param, task, userVO, Boolean.TRUE));
        }
        return tasks;
    }

    private InkFlowerIndexVO.Task buildTask(BaseParam param, InkFlowerConstant.LongLingForHomeTaskEnum task, UserVO userVO, Boolean isCollected) {
        String taskDesc;
        String rewardDesc;
        Long currentProgress;
        Long maxProgress;
        Long rewardNum;
        SexType sex = userVO.getSex();
        if (isCollected) {
            taskDesc = SexType.MAN.equals(sex) ? task.getManCollectedTaskDesc() : task.getWomanCollectedTaskDesc();
            rewardDesc = task.getCollectedRewardDesc();
            currentProgress = inkFlowerConstant.getLongLingForHomeTaskCollectedProgress(param.getUid(), task);
            maxProgress = task.getCollectedRequiredNum();
            if (currentProgress >= maxProgress) {
                currentProgress = maxProgress;
            }
            rewardNum = task.getCollectedRewardNum();
        } else {
            taskDesc = SexType.MAN.equals(sex) ? task.getManTaskDesc() : task.getWomanTaskDesc();
            rewardDesc = task.getRewardDesc();
            currentProgress = inkFlowerConstant.getLongLingForHomeTaskProgress(param.getUid(), task);
            maxProgress = task.getRequiredNum();
            if (currentProgress >= maxProgress) {
                currentProgress = maxProgress;
            }
            rewardNum = task.getRewardNum();
        }
        return InkFlowerIndexVO.Task.builder()
                .taskKey(task.name())
                .taskDesc(taskDesc)
                .rewardDesc(rewardDesc)
                .currentProgress(currentProgress)
                .maxProgress(maxProgress)
                .status(getTaskStatus(param, task, isCollected))
                .isCollected(isCollected)
                .rewardCount(rewardNum)
                .build();
    }

    private Integer getTaskStatus(BaseParam param, InkFlowerConstant.LongLingForHomeTaskEnum task, Boolean isCollected) {
        Integer status;
        if (isCollected) {
            Long progress = inkFlowerConstant.getLongLingForHomeTaskCollectedProgress(param.getUid(), task);
            status = progress.compareTo(task.getCollectedRequiredNum()) >= 0 ? 1 : 0;
        } else {
            Long progress = inkFlowerConstant.getLongLingForHomeTaskProgress(param.getUid(), task);
            status = progress.compareTo(task.getRequiredNum()) >= 0 ? 1 : 0;
        }
        if (inkFlowerConstant.isTaskRewarded(param.getUid(), task, isCollected)) {
            status = 2;
        }
        return status;
    }

    private InkFlowerIndexVO.LoveStoryContent buildLoveStoryContent(BaseParam param, Long toUid) {
        InkFlowerIndexVO.LoveStoryContent loveStoryContent = new InkFlowerIndexVO.LoveStoryContent();
        if (toUid == null) {
            toUid = getBindUserId(param);
        }
        loveStoryContent.setUserBindingInfo(buildUserBindingInfo(param, toUid));
        String bindKey = AppUtil.splicUserId(param.getUid(), toUid);
        loveStoryContent.setCollectibleItems(buildCollectibleItems(bindKey));
        loveStoryContent.setTaskItems(buildTaskItems());
        loveStoryContent.setPrayerStatus(buildPrayerStatus(bindKey, param, toUid));
        return loveStoryContent;
    }

    private Long getBindUserId(BaseParam param) {
        List<InkFlowerIndexVO.User> userList = getFriendList(param, 0L, 100);
        for (InkFlowerIndexVO.User user : userList) {
            Boolean isBind = inkFlowerConstant.isBindFriend(param.getUid(), user.getUserId());
            if (isBind) {
                return user.getUserId();
            }
        }
        return null;
    }

    private InkFlowerIndexVO.UserBindingInfo buildUserBindingInfo(BaseParam param, Long toUid) {
        InkFlowerIndexVO.UserBindingInfo userBindingInfo = new InkFlowerIndexVO.UserBindingInfo();
        userBindingInfo.setUser(buildUser(param.getUid()));
        userBindingInfo.setPartner(buildUser(toUid));
        return userBindingInfo;
    }

    private InkFlowerIndexVO.User buildUser(Long uid) {
        InkFlowerIndexVO.User user = new InkFlowerIndexVO.User();
        if (uid == null) {
            return user;
        }
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        if (userVO == null) {
            return user;
        }
        user.setUsername(userVO.getName());
        user.setAvatar(userVO.getAvatar());
        user.setUserId(userVO.getId());
        return user;
    }

    private InkFlowerIndexVO.PrayerStatus buildPrayerStatus(String bindKey, BaseParam param, Long toUid) {
        InkFlowerIndexVO.PrayerStatus prayerStatus = new InkFlowerIndexVO.PrayerStatus();
        prayerStatus.setPrayerContent(buildPrayerContent(bindKey, param.getUid(), toUid));
        Integer buttonStatus = getPrayedStatus(param, toUid);
        prayerStatus.setButtonStatus(buttonStatus);
        prayerStatus.setIsPrayed(buttonStatus < 2 ? Boolean.FALSE : Boolean.TRUE);
        return prayerStatus;
    }


    private Integer getPrayedStatus(BaseParam param, Long toUid) {
        String bindKey = AppUtil.splicUserId(param.getUid(), toUid);
        Boolean isCollected = Boolean.TRUE;
        for (InkFlowerConstant.LoveStoryTaskEnum loveStoryTaskEnum : InkFlowerConstant.LoveStoryTaskEnum.values()) {
            Long count = inkFlowerConstant.getLoveStoryTaskProgress(bindKey, loveStoryTaskEnum.getRewardKey());
            if (count < loveStoryTaskEnum.getRequiredNum()) {
                isCollected = Boolean.FALSE;
            }
        }
        Boolean isPrayed = inkFlowerConstant.isPrayed(param, toUid);
        if (isPrayed) {
            return JourneyToTheWestConstant.ButtonStatusEnum.RECEIVED.getStatus();
        }
        if (isCollected) {
            return JourneyToTheWestConstant.ButtonStatusEnum.LIT.getStatus();
        }
        return JourneyToTheWestConstant.ButtonStatusEnum.UNLIT.getStatus();
    }

    private InkFlowerIndexVO.PrayerContent buildPrayerContent(String bindKey, Long uid, Long toUid) {
        InkFlowerIndexVO.PrayerContent prayerContent = new InkFlowerIndexVO.PrayerContent();
        prayerContent.setMyPrayer(inkFlowerConstant.getPrayerContent(bindKey, uid));
        prayerContent.setPartnerPrayer(inkFlowerConstant.getPrayerContent(bindKey, toUid));
        return prayerContent;
    }

    private List<InkFlowerIndexVO.Gift> buildTaskItems() {
        List<InkFlowerIndexVO.Gift> taskItemList = new ArrayList<>();
        List<ScenePrizeDO> scenePrizeList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), InkFlowerConstant.ACTIVITY_CODE, InkFlowerConstant.LOVE_STORY_TASK_CODE);
        for (ScenePrizeDO scenePrizeDO : scenePrizeList) {
            InkFlowerIndexVO.Gift gift = new InkFlowerIndexVO.Gift();
            gift.setIcon(scenePrizeDO.getPrizeIcon());
            gift.setName(scenePrizeDO.getPrizeDesc());
            gift.setValue(scenePrizeDO.getPrizeValueGold());
            taskItemList.add(gift);
        }
        return taskItemList;
    }

    private List<InkFlowerIndexVO.CollectibleItem> buildCollectibleItems(String bindKey) {
        List<InkFlowerIndexVO.CollectibleItem> collectibleItemList = new ArrayList<>();
        List<ScenePrizeDO> scenePrizeList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), InkFlowerConstant.ACTIVITY_CODE, InkFlowerConstant.LOVE_STORY_TASK_REWARD_CODE);
        Map<String, ScenePrizeDO> scenePrizeDOMap = scenePrizeList.stream().collect(Collectors.toMap(ScenePrizeDO::getPrizeValue, scenePrizeDO -> scenePrizeDO));
        for (InkFlowerConstant.LoveStoryTaskEnum loveStoryTaskEnum : InkFlowerConstant.LoveStoryTaskEnum.values()) {
            InkFlowerIndexVO.CollectibleItem collectibleItem = new InkFlowerIndexVO.CollectibleItem();
            ScenePrizeDO scenePrizeDO = scenePrizeDOMap.get(loveStoryTaskEnum.getRewardKey());
            collectibleItem.setIcon(scenePrizeDO.getPrizeIcon());
            collectibleItem.setName(scenePrizeDO.getPrizeDesc());
            Long currentNum = inkFlowerConstant.getLoveStoryTaskProgress(bindKey, loveStoryTaskEnum.getRewardKey());
            Boolean isCollected = Boolean.FALSE;
            if (currentNum >= loveStoryTaskEnum.getRequiredNum()) {
                //不做最大数量限制
/*
                currentNum = loveStoryTaskEnum.getRequiredNum();
*/
                isCollected = Boolean.TRUE;
            }
            collectibleItem.setCurrentCount(currentNum);
            collectibleItem.setRequiredCount(loveStoryTaskEnum.getRequiredNum());
            collectibleItem.setIsCollected(isCollected);
            collectibleItemList.add(collectibleItem);
        }
        return collectibleItemList;
    }

    private InkFlowerIndexVO.SnowSoundContent buildSnowSoundContent(BaseParam param) {
        InkFlowerIndexVO.SnowSoundContent snowSoundContent = new InkFlowerIndexVO.SnowSoundContent();
        snowSoundContent.setPlumShop(buildPlumShop(param));
        snowSoundContent.setPrizePool(buildPrizePool(param));
        snowSoundContent.setTaskGift(buildTaskGift(param));
        return snowSoundContent;
    }

    private InkFlowerIndexVO.TaskGift buildTaskGift(BaseParam param) {
        List<ScenePrizeDO> scenePrizeList = scenePrizeJpaDAO.getListBySceneCode(param.getAppId(), InkFlowerConstant.ACTIVITY_CODE, InkFlowerConstant.TASK_CODE);
        List<InkFlowerIndexVO.Gift> taskList = new ArrayList<>();
        for (ScenePrizeDO scenePrizeDO : scenePrizeList) {
            InkFlowerIndexVO.Gift gift = new InkFlowerIndexVO.Gift();
            gift.setIcon(scenePrizeDO.getPrizeIcon());
            gift.setName(scenePrizeDO.getPrizeDesc());
            gift.setValue(scenePrizeDO.getPrizeValueGold());
            taskList.add(gift);
        }
        return InkFlowerIndexVO.TaskGift.builder()
                .gifts(taskList)
                .build();
    }

    private InkFlowerIndexVO.PrizePool buildPrizePool(BaseParam param) {
        InkFlowerIndexVO.PrizePool prizePool = new InkFlowerIndexVO.PrizePool();
        prizePool.setPrizes(buildPoolPrizeList());
        prizePool.setPrizeCount(inkFlowerConstant.getInkFlowerNum(param.getUid()));
        return prizePool;
    }

    private List<InkFlowerIndexVO.Gift> buildPoolPrizeList() {
        List<InkFlowerIndexVO.Gift> prizeList = new ArrayList<>();
        List<DrawPoolItemDO> drawPoolItemDOList = drawPoolItemJpaDAO.getByPoolCode(InkFlowerConstant.POOL_CODE);
        drawPoolItemDOList.sort(Comparator.comparing(DrawPoolItemDO::getItemValueGold));
        for (DrawPoolItemDO drawPoolItemDO : drawPoolItemDOList) {
            InkFlowerIndexVO.Gift prize = new InkFlowerIndexVO.Gift();
            prize.setIcon(drawPoolItemDO.getItemIcon());
            prize.setName(drawPoolItemDO.getItemName());
            prize.setValue(drawPoolItemDO.getItemValueGold());
            prizeList.add(prize);
        }
        return prizeList;
    }

    private InkFlowerIndexVO.PlumShop buildPlumShop(BaseParam param) {
        InkFlowerIndexVO.PlumShop plumShop = new InkFlowerIndexVO.PlumShop();
        plumShop.setCurrentPlumValue(inkFlowerConstant.getPlumValue(param.getUid()));
        plumShop.setGifts(buildShopGift(param));
        return plumShop;
    }

    private List<InkFlowerIndexVO.ItemGift> buildShopGift(BaseParam param) {
        Map<Integer, ScenePrizeDO> scenePrizeDOMap = getPlumShopGiftMap(param);
        List<InkFlowerIndexVO.ItemGift> giftList = new ArrayList<>();
        for (Map.Entry<Integer, ScenePrizeDO> entry : scenePrizeDOMap.entrySet()) {
            InkFlowerIndexVO.ItemGift gift = new InkFlowerIndexVO.ItemGift();
            ScenePrizeDO scenePrizeDO = entry.getValue();
            gift.setIcon(scenePrizeDO.getPrizeIcon());
            gift.setName(scenePrizeDO.getPrizeDesc());
            gift.setValue(scenePrizeDO.getPrizeValueGold());
            gift.setItemId(getItemId(scenePrizeDO));
            gift.setRequiredPlumValue(scenePrizeDO.getNeedCount());
            giftList.add(gift);
        }
        return giftList;
    }

    private Integer getItemId(ScenePrizeDO scenePrizeDO) {
        String extData = scenePrizeDO.getExtData();
        Object itemId = JSON.parseObject(extData, Map.class).get("itemId");
        return itemId == null ? 0 : Integer.parseInt(itemId.toString());
    }

    @Override
    public String getTemplateType() {
        return "";
    }

    @Override
    public String getActivityCode() {
        return "";
    }

    public Object getLog(BaseParam param, String activityCode, String poolCode) {
        Long uid = param.getUid();
        return inkFlowerDrawManager.drawLogNew(DrawLogParam.builder()
                .appId(param.getAppId())
                .unionId(param.getUnionId())
                .uid(uid)
                .activityCode(activityCode)
                .build(), poolCode);
    }


    public DrawReturn buyItem(Integer itemId, Integer quantity) {
        BaseParam baseParam = BaseParam.ofMDC();
        // 校验
        if (!checkPlumValue(baseParam, itemId, quantity)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "您的梅花值不够哦～");
        }
        Map<Integer, ScenePrizeDO> scenePrizeDOMap = getPlumShopGiftMap(baseParam);
        ScenePrizeDO scenePrizeDO = scenePrizeDOMap.get(itemId);
        //扣减梅花值
        Integer decrementNum = quantity * scenePrizeDO.getNeedCount();
        inkFlowerConstant.decrementPlumValue(baseParam.getUid(), decrementNum.longValue());
        //下发奖励
        sendPrizeManager.sendPrize(
                baseParam,
                Collections.singletonList(SendPrizeDTO.of(scenePrizeDO, baseParam.getUid()))
        );
        //购买记录
        recordBuyItem(baseParam, scenePrizeDO);
        inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "buy_gift_from_plum", 1);
        return buildDrawReturn(scenePrizeDO);
    }


    private void recordBuyItem(BaseParam baseParam, ScenePrizeDO scenePrizeDO) {
        List<DrawPoolItemDTO> prizeItemList = buildExchangeItemList(scenePrizeDO, InkFlowerConstant.PLUM_SHOP_BUY_RECORD);
        logComponent.putDrawLog(baseParam, InkFlowerConstant.ACTIVITY_CODE, prizeItemList);
    }

    private List<DrawPoolItemDTO> buildExchangeItemList(ScenePrizeDO scenePrizeDO, String recordCode) {
        DrawPoolItemDO drawPoolItemDO = DrawPoolItemDO.builder()
                .itemName(scenePrizeDO.getPrizeDesc())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .itemKey(scenePrizeDO.getPrizeValue())
                .itemNum(1)
                .itemValueGold(scenePrizeDO.getPrizeValueGold())
                .status(1)
                .poolCode(recordCode)
                .build();
        DrawPoolItemDTO drawPoolItemDTO = DrawPoolItemDTO.builder()
                .targetTimes(1)
                .drawPoolItemDO(drawPoolItemDO).build();
        List<DrawPoolItemDTO> prizeItemList = new ArrayList<>();
        prizeItemList.add(drawPoolItemDTO);
        return prizeItemList;
    }

    private DrawReturn buildDrawReturn(ScenePrizeDO scenePrizeDO) {
        DrawReturn drawReturn = new DrawReturn();
        List<PrizeItem> prizeItemList = new ArrayList<>();
        PrizeItem prizeItem = PrizeItem.builder()
                .prizeName(scenePrizeDO.getPrizeDesc())
                .prizeIcon(scenePrizeDO.getPrizeIcon())
                .valueGold(scenePrizeDO.getPrizeValueGold().intValue())
                .build();
        prizeItemList.add(prizeItem);
        drawReturn.setPrizeItemList(prizeItemList);
        return drawReturn;
    }

    private Boolean checkPlumValue(BaseParam baseParam, Integer itemId, Integer quantity) {
        //校验梅花值是否到达指定值
        Long plumValue = inkFlowerConstant.getPlumValue(baseParam.getUid());
        Map<Integer, ScenePrizeDO> scenePrizeDOMap = getPlumShopGiftMap(baseParam);
        Boolean isLock = inkFlowerConstant.isPlumShopLock(baseParam.getUid());
        if (plumValue < InkFlowerConstant.MINIMUM_BUY_PLUM_VALUE && !isLock) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前长安印不够400哦～快去抽奖获取吧～");
        }
        ScenePrizeDO scenePrizeDO = scenePrizeDOMap.get(itemId);
        Long needPlumValue = Optional.ofNullable(scenePrizeDO.getNeedCount()).orElse(Integer.MAX_VALUE).longValue();
        if (plumValue < needPlumValue * quantity) {
            return Boolean.FALSE;
        }
        inkFlowerConstant.setPlumShopLock(baseParam.getUid());
        return Boolean.TRUE;
    }

    private Map<Integer, ScenePrizeDO> getPlumShopGiftMap(BaseParam baseParam) {
        Map<Integer, ScenePrizeDO> scenePrizeDOMap = inkFlowerConstant.getCaChedScenePrizeMap();
        if (scenePrizeDOMap == null) {
            scenePrizeDOMap = new HashMap<>();
        }
        List<ScenePrizeDO> scenePrizeDOList = scenePrizeJpaDAO.getListBySceneCode(baseParam.getAppId(), InkFlowerConstant.ACTIVITY_CODE, InkFlowerConstant.PLUM_SHOP_GIFT);
        for (ScenePrizeDO scenePrizeDO : scenePrizeDOList) {
            String extData = scenePrizeDO.getExtData();
            Integer prizeId = Integer.parseInt(JSON.parseObject(extData).get("itemId").toString());
            scenePrizeDOMap.put(prizeId, scenePrizeDO);
        }
        inkFlowerConstant.setCaCheScenePrizeMap(scenePrizeDOMap);
        return scenePrizeDOMap;
    }

    public List<InkFlowerIndexVO.User> getFriendList(BaseParam baseParam, Long start, Integer num) {
        if (start == null) {
            start = 0L;
        }
        if (num == null) {
            num = 100;
        }
        UserVO userVO = userFeignManager.getBasicWithCache(baseParam.getAppId(), baseParam.getUid());
        PageVO<WrapUserVO> friendList = feignLanlingService.getFriendList(start, num).getData();
        PageVO<WrapUserVO> subFriendList = feignLanlingService.getSubscribeList(start, num).getData();
        PageVO<WrapUserVO> fansList = feignLanlingService.getFansList(start, num).getData();
        Map<Long, CardiacRelationVO> allUserDepth = depthFeignService.getAllUserDepth(MDCUtil.getCurUserIdByMdc()).successData();
        List<InkFlowerIndexVO.User> userList = new ArrayList<>();
        userList.addAll(convertCardiacRelationVOListToUserList(allUserDepth, userVO));
        userList.addAll(convertWrapUserVOListToUserList(subFriendList.getList(), userVO));
        userList.addAll(convertWrapUserVOListToUserList(friendList.getList(), userVO));
        userList.addAll(convertWrapUserVOListToUserList(fansList.getList(), userVO));
        Set<Long> userIdSet = new HashSet<>();
        return userList.stream()
                .filter(user -> userIdSet.add(user.getUserId()))
                .collect(Collectors.toList());
    }

    private List<InkFlowerIndexVO.User> convertWrapUserVOListToUserList(List<WrapUserVO> list, UserVO userVO) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        List<InkFlowerIndexVO.User> userList = new ArrayList<>();
        for (WrapUserVO wrapUserVO : list) {
            if (userVO.getSex().getCode().equals(wrapUserVO.getUser().getSex())) {
                continue;
            }
            InkFlowerIndexVO.User user = InkFlowerIndexVO.User.builder().build();
            user.setAvatar(wrapUserVO.getUser().getAvatar());
            user.setUserId(wrapUserVO.getUser().getId());
            user.setUsername(wrapUserVO.getUser().getName());
            userList.add(user);
        }
        return userList;
    }

    private List<InkFlowerIndexVO.User> convertCardiacRelationVOListToUserList(Map<Long, CardiacRelationVO> allUserDepth, UserVO userVO) {
        if (allUserDepth == null || allUserDepth.isEmpty()) {
            return Collections.emptyList();
        }
        List<InkFlowerIndexVO.User> result = new ArrayList<>();
        for (Long key : allUserDepth.keySet()) {
            UserVO user = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), key);
            if(user == null){
                continue;
            }
            if (userVO.getSex().equals(user.getSex())) {
                continue;
            }
            InkFlowerIndexVO.User temp = InkFlowerIndexVO.User.builder().build();
            temp.setAvatar(user.getAvatar());
            temp.setUserId(user.getId());
            temp.setUsername(user.getName());
            result.add(temp);
        }
        return result;
    }

    public List<InkFlowerIndexVO.Gift> claimWishReward(BaseParam baseParam, Long toUid, String prayer) {
        //检查是否集齐
        checkWishRewardStatus(baseParam, toUid, prayer);
        //下发奖励
        List<ScenePrizeDO> scenePrizeList = scenePrizeJpaDAO.getListBySceneCode(ServicesAppIdEnum.lanling.getAppId(), InkFlowerConstant.ACTIVITY_CODE, InkFlowerConstant.WISH_REWARD);
        if (scenePrizeList == null) {
            log.error("月老奖励未配置");
            return null;
        }
        sendPrizeManager.sendPrize(
                baseParam,
                scenePrizeList.stream().map(scenePrizeDO -> SendPrizeDTO.of(scenePrizeDO, toUid)).collect(Collectors.toList())
        );
        inkFlowerConstant.setPrayerContent(AppUtil.splicUserId(baseParam.getUid(), toUid), baseParam.getUid(), prayer);
        inkFlowerConstant.setPrayed(baseParam, toUid);
        //埋点
        for (ScenePrizeDO scenePrizeDO : scenePrizeList) {
            inkFlowerTrackManager.allActivityReceiveAward("tie_love_knot", scenePrizeDO.getPrizeValue(), scenePrizeDO.getPrizeValueGold(), scenePrizeDO.getPrizeNum(), baseParam.getUid());
        }
        return buildReturn(scenePrizeList);
    }

    private void checkWishRewardStatus(BaseParam baseParam, Long toUid, String prayer) {
        Integer prayedStatus = getPrayedStatus(baseParam, toUid);
        if (prayedStatus.equals(JourneyToTheWestConstant.ButtonStatusEnum.UNLIT.getStatus())) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "道具不够哦～快去收集吧～");
        }
        if (prayedStatus.equals(JourneyToTheWestConstant.ButtonStatusEnum.RECEIVED.getStatus())) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已领取哦～");
        }
        if (StrUtil.isBlank(prayer)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "请输入你的祈愿短语～");
        }
        if (prayer.length() > InkFlowerConstant.MAX_PRAYER_LENGTH) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "祈愿短语过长～");
        }
        Boolean isContainsProhibitedWords = containsProhibitedWords(baseParam, prayer);
        if (Boolean.FALSE.equals(isContainsProhibitedWords)) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "祈愿不合规哦～请重新祈愿吧～");
        }

    }

    private Boolean containsProhibitedWords(BaseParam baseParam, String prayer) {
        GraphIdEnum graphIdEnum = GraphIdEnum.SINGLE_CHAT;
        Map<String, Object> detection = RiskUtil.getDetectionMap(prayer, null, null, null, null, null);
        RiskParamDTO build = RiskParamDTO.builder().scene(RiskScene.TEXT_NET_TEXT_LIVE)
                .services(ServicesNameEnum.lanling_services)
                .baseUrl(RiskParam.baseRiskUrl).basePath(RiskParam.syncScanUrl)
                .appId(ServicesAppIdEnum.lanling.getAppId())
                .userId(baseParam.getUid())
                .graphId(GraphIdEnum.TEXT_NET_TEXT_LIVE)
                .detection(detection)
                .build();
        String riskResultString = null;
        try {
            riskResultString = riskFeignService.sendRisk(build).successData();
        } catch (Exception e) {
            log.error("sendRiskError {} err : ", build, e);
            return Boolean.FALSE;
        }
        log.info("riskResultString {}", riskResultString);
        if (riskResultString != null) {
            if (JSON.isValid(riskResultString)) {
                JSONObject jsonObject = JSON.parseObject(riskResultString);
                if (Boolean.TRUE.equals(jsonObject.getBoolean("success"))) {
                    if ("pass".equals(jsonObject.getJSONObject("data").getString("action"))) {
                        return Boolean.TRUE;
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    private List<InkFlowerIndexVO.Gift> buildReturn(List<ScenePrizeDO> scenePrizeList) {
        List<InkFlowerIndexVO.Gift> result = new ArrayList<>();
        for (ScenePrizeDO scenePrizeDO : scenePrizeList) {
            InkFlowerIndexVO.Gift gift = new InkFlowerIndexVO.Gift();
            gift.setIcon(scenePrizeDO.getPrizeIcon());
            gift.setName(scenePrizeDO.getPrizeDesc());
            gift.setValue(scenePrizeDO.getPrizeValueGold());
            result.add(gift);
        }
        return result;
    }

    public Boolean taskRewarded(BaseParam baseParam, String taskKey, Boolean isCollected) {
        InkFlowerConstant.LongLingForHomeTaskEnum task = InkFlowerConstant.LongLingForHomeTaskEnum.getByName(taskKey);
        if (task == null) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "参数错误");
        }
        //校验
        Boolean check = inkFlowerConstant.isTaskRewarded(baseParam.getUid(), task, isCollected);
        if (check) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "已领取过");
        }
        //领奖
        if (isCollected) {
            inkFlowerConstant.incrementDiceNumber(baseParam.getUid(), task.getCollectedRewardNum());
            inkFlowerTaskTrack(baseParam, task, isCollected);
        } else {
            inkFlowerConstant.incrementDiceNumber(baseParam.getUid(), task.getRewardNum());
            inkFlowerTaskTrack(baseParam, task, isCollected);
        }
        //更改领奖状态
        inkFlowerConstant.setTaskRewarded(baseParam.getUid(), task, isCollected);
        Long incrDiceCoinNum = InkFlowerConstant.DEFAULT_COIN_INCREMENT_NUM * task.getRewardNum();
        log.info("增加隐藏抽奖币  coin {}", incrDiceCoinNum);
        inkFlowerConstant.incrementRollDiceCoin(baseParam.getUid(), incrDiceCoinNum);
        return Boolean.TRUE;
    }

    private void inkFlowerTaskTrack(BaseParam baseParam, InkFlowerConstant.LongLingForHomeTaskEnum task, Boolean isCollected) {
        if (isCollected) {
            if (InkFlowerConstant.LongLingForHomeTaskEnum.TASK_1.equals(task)) {
                inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "task_2", task.getCollectedRewardNum().intValue());
            }
            if (InkFlowerConstant.LongLingForHomeTaskEnum.TASK_2.equals(task)) {
                inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "task_4", task.getCollectedRewardNum().intValue());
            }
            if (InkFlowerConstant.LongLingForHomeTaskEnum.TASK_3.equals(task)) {
                inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "task_6", task.getCollectedRewardNum().intValue());
            }
        } else {
            if (InkFlowerConstant.LongLingForHomeTaskEnum.TASK_1.equals(task)) {
                inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "task_1", task.getRewardNum().intValue());
            }
            if (InkFlowerConstant.LongLingForHomeTaskEnum.TASK_2.equals(task)) {
                inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "task_3", task.getRewardNum().intValue());
            }
            if (InkFlowerConstant.LongLingForHomeTaskEnum.TASK_3.equals(task)) {
                inkFlowerTrackManager.allActivityTaskFinish(baseParam.getUid(), "task_4", task.getRewardNum().intValue());
            }
        }

    }

    @NoRepeatSubmit(time = 3)
    public RollDiceReturn draw(BaseParam param) {
        int timesLeft = inkFlowerConstant.getDiceNumber(param.getUid()).intValue();
        if (timesLeft <= 0) {
            throw new ServiceException(ErrorCode.INVALID_PARAM, "当前玲珑骰子不足够哦～");
        }
        inkFlowerConstant.decrementDiceNumber(param.getUid(), 1L);

        Set<Integer> stepSet = new HashSet<>();
        int step = 0;
        int index = inkFlowerConstant.getRollDiceIndex(param.getUid());
        InkFlowerConstant.GiftMap map = null;
        Integer drawCoin = inkFlowerConstant.getRollDiceCoin(param.getUid());
/*
        if (drawCoin == null) {
            inkFlowerConstant.initRollDiceCoin(param.getUid(), 20);
            drawCoin = 20;
        }
*/
        int minStep = 0;
        long minItemValueGold = Long.MAX_VALUE;
        while (stepSet.size() < 6) {
            step = InkFlowerConstant.RANDOM.nextInt(6) + 1;
            if (stepSet.contains(step)) {
                continue;
            }

            int newIndex = (index + step) % InkFlowerConstant.GiftMap.values().length;
            map = InkFlowerConstant.GiftMap.values()[newIndex];
            if (map.getItemValueGold() < minItemValueGold) {
                minItemValueGold = map.getItemValueGold();
                minStep = step;
            }
            if (map.getItemValueGold() > drawCoin) {
                stepSet.add(step);
            } else {
                break;
            }
        }
        if (stepSet.size() == 6) {
            // 最小
            step = minStep;
        }
        index = (index + step) % InkFlowerConstant.GiftMap.values().length;
        map = InkFlowerConstant.GiftMap.values()[index];

        List<PrizeItem> prizeItemList = new ArrayList<>();
        if ("gift".equals(map.getPrizeType())) {
            CoinGiftProductVO coinGiftProductVO = feignProductService.getGiftByGiftKey(param.getAppId(), map.getItemKey()).successData();
            if (coinGiftProductVO != null) {
                prizeItemList = Collections.singletonList(PrizeItem.builder().prizeName(coinGiftProductVO.getName()).prizeKey(coinGiftProductVO.getGiftKey()).prizeType("gift").prizeIcon(coinGiftProductVO.getIcon()).valueGold(Math.toIntExact(coinGiftProductVO.getNeedCoin())).prizeNum(1).build());
            }
        } else if ("head_frame".equals(map.getPrizeType())) {
            UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), param.getUid());
            String giftKey;
            if (SexType.MAN.equals(userVO.getSex())) {
                giftKey = InkFlowerConstant.GiftMapHeadFrame.MAN_HEAD_FRAME.getItemKey();
            } else {
                giftKey = InkFlowerConstant.GiftMapHeadFrame.WOMAN_HEAD_FRAME.getItemKey();
            }
            HeadFrameProductVO headFrameProductVO = feignProductService.getHfProductByKey(param.getAppId(), giftKey).successData();
            if (headFrameProductVO != null) {
                prizeItemList = Collections.singletonList(PrizeItem.builder().prizeName(headFrameProductVO.getName()).prizeKey(headFrameProductVO.getUniqueKey()).prizeType("dress_up").prizeSubType("head_frame").prizeIcon(headFrameProductVO.getIcon()).valueGold(0).effectiveDays(14).build());
            }
        }

        sendPrizeManager.sendPrize(
                BaseParam.builder().appId(param.getAppId()).unionId(param.getUnionId()).uid(param.getUid()).build(),
                prizeItemList.stream().map(p -> SendPrizeDTO.of(p, InkFlowerConstant.ACTIVITY_CODE)).collect(Collectors.toList())
        );

        inkFlowerConstant.decrementRollDiceCoin(param.getUid(), map.getItemValueGold());
        inkFlowerConstant.setRollDiceIndex(param, index);

        for (PrizeItem prizeItem : prizeItemList) {
            //埋点
            inkFlowerTrackManager.allActivityLottery(param.getUid(),"dice_pool","1",prizeItem.getPrizeKey(),prizeItem.getValueGold().longValue());
            inkFlowerTrackManager.allActivityReceiveAward("traveler's_station", prizeItem.getPrizeKey(), prizeItem.getValueGold().longValue(), prizeItem.getPrizeNum(), param.getUid());
        }

        return RollDiceReturn.builder()
                .diceValue(step)
                .currentPosition(index)
                .gifts(buildGift(prizeItemList))
                .build();
    }

    private List<InkFlowerIndexVO.Gift> buildGift(List<PrizeItem> prizeItemList) {
        List<InkFlowerIndexVO.Gift> giftList = new ArrayList<>();
        for (PrizeItem prizeItem : prizeItemList) {
            InkFlowerIndexVO.Gift gift = new InkFlowerIndexVO.Gift();
            if ("dress_up".equals(prizeItem.getPrizeType())) {
                gift.setValue(300L);
            } else {
                gift.setValue(prizeItem.getValueGold().longValue());
            }
            gift.setIcon(prizeItem.getPrizeIcon());
            gift.setName(prizeItem.getPrizeName());
            giftList.add(gift);
        }
        return giftList;
    }

    public RankVO getRank(BaseParam param) {
        return inkFlowerRankManager.getRank(RankContext.builder()
                .param(BaseParam.ofMDC())
                .activityCode(InkFlowerConstant.ACTIVITY_CODE)
                .rankKey(InkFlowerConstant.RANK_KEY)
                .rankLen(10L)
                .supportDiff(Boolean.TRUE)
                .type(RankContext.RankType.user)
                .build());
    }
}
