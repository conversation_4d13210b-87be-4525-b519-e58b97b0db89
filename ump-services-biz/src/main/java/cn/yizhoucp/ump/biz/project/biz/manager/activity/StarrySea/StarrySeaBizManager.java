package cn.yizhoucp.ump.biz.project.biz.manager.activity.StarrySea;

import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ump.api.vo.jimu.draw.PrizeItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawEvent;
import cn.yizhoucp.ump.biz.project.biz.event.astrologydraw.AstrologyDrawMessage;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.yizhoucp.ump.biz.project.biz.manager.activity.StarrySea.StarrySeaConstant.*;

@Service
@Slf4j
public class StarrySeaBizManager {

    @Resource
    private StarrySeaDataManager starrySeaDataManager;

    @Resource
    private StarrySeaRankManager starrySeaRankManager;

    private static final List<String> NEW_GIFT_KEY = Arrays.asList("XS_GIFT_ASTROLOGY", "DJCA_GIFT_ASTROLOGY",
            "MWLC_GIFT_ASTROLOGY", "YRRJ_GIFT_ASTROLOGY", "HXSH_GIFT_ASTROLOGY", "YNYW_GIFT_ASTROLOGY",
            "TMYY_GIFT_ASTROLOGY", "BB_GIFT_ASTROLOGY", "XZGJ_GIFT_ASTROLOGY", "LSJL_GIFT_ASTROLOGY",
            "CSHY_GIFT_ASTROLOGY", "JXWY_GIFT_ASTROLOGY", "LYZQ_GIFT_ASTROLOGY", "WMYHB_GIFT_ASTROLOGY");

//    @EventListener
    @ActivityCheck(activityCode = "starry-sea", isThrowException = false)
    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    public void onDraw(AstrologyDrawEvent event) {
        log.debug("监听到抽奖完成事件 -> {}", event);
        AstrologyDrawMessage drawMessage = event.getSource();
        // 增加全服抽奖次数
        starrySeaDataManager.addAllDrawTimes(drawMessage);

        // 增加个人抽奖次数
        starrySeaDataManager.addPersonDrawTimes(drawMessage);

        // 增加次数榜单
        starrySeaRankManager.incrTimesRank(drawMessage);

        // 增加新礼物榜单
        for (DrawPoolItemDTO drawPoolItemDTO : drawMessage.getDrawResult()) {
            if (NEW_GIFT_KEY.contains(drawPoolItemDTO.getDrawPoolItemDO().getItemKey())) {
                starrySeaRankManager.incrGiftRank(drawMessage.getUid(), drawPoolItemDTO.getTargetTimes()*drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold());
            }
        }
    }

    @ActivityCheck(activityCode = "starry-sea", isThrowException = false)
    public Map<Long, PrizeItem> getReplacePrize() {
        return starrySeaDataManager.getReplaceGiftMap();
    }
}
