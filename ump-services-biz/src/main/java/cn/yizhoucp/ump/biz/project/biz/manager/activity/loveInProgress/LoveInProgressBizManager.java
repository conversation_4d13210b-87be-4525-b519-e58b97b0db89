package cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress;

import cn.hutool.core.util.ObjectUtil;
import cn.yizhoucp.ms.core.base.config.ThreadPoolConfig;
import cn.yizhoucp.ms.core.base.convert.SexType;
import cn.yizhoucp.ms.core.base.enums.ServicesAppIdEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.landingservices.CoinGiftGivedModel;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressConstant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressEnums;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.loveInProgress.common.LoveInProgressRedisManager;
import cn.yizhoucp.ump.biz.project.common.ActivityComponent;
import cn.yizhoucp.ump.biz.project.common.event.GiftGiveEvent;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.ScenePrizeDO;
import cn.yizhoucp.user.manager.UserFeignManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class LoveInProgressBizManager implements ActivityComponent {
    @Resource
    private LoveInProgressRankManager loveInProgressRankManager;
    @Resource
    private LoveInProgressRedisManager loveInProgressRedisManager;
    @Resource
    private UserFeignManager userFeignManager;

    @Override
    public String getActivityCode() {
        return "";
    }

    @Async(ThreadPoolConfig.ASYNC_EXECUTOR_NAME)
    @EventListener(GiftGiveEvent.class)
    @ActivityCheck(activityCode = LoveInProgressConstant.ACTIVITY_CODE, isThrowException = false)
    public void eventSendGiftHandle(GiftGiveEvent event) {
        log.info("sendGiftHandle event:{}", event);
        sendGiftHandle(event.getBaseParam(), event.getCoinGiftGivedModels());
    }


    public Boolean sendGiftHandle(BaseParam param, List<CoinGiftGivedModel> coinGiftGivedModelList) {
        for (CoinGiftGivedModel coinGiftGivedModel : coinGiftGivedModelList) {
            log.info("sendGiftHandle param:{},coinGiftGivedModel:{}", param, coinGiftGivedModel);
            ScenePrizeDO scenePrizeDO = loveInProgressRedisManager.getTaskPrize(coinGiftGivedModel.getGiftKey());
            if (scenePrizeDO == null) {
                continue;
            }

            //处理任务
            taskHandle(param, coinGiftGivedModel, scenePrizeDO);
            //排行榜
            rankHandle(param, coinGiftGivedModel);
        }
        return Boolean.TRUE;
    }

    private void rankHandle(BaseParam param, CoinGiftGivedModel coinGiftGivedModelList) {
        Long uid = param.getUid();
        Long toUid = coinGiftGivedModelList.getToUid();
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), uid);
        UserVO toUserVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), coinGiftGivedModelList.getToUid());
        if (userVO == null || toUserVO == null) {
            return;
        }
        //同性不统计
        if (ObjectUtil.equals(userVO.getSex().getCode(), toUserVO.getSex().getCode())) {
            return;
        }
        String bindId = AppUtil.splicUserId(uid, toUid);
        Long value = coinGiftGivedModelList.getCoin() * LoveInProgressConstant.BASE_RANK_VALUE;
        loveInProgressRankManager.incrRankValue(bindId, value, loveInProgressRedisManager.getRankKey());
        loveInProgressRedisManager.incrementBindRank(uid, toUid, value);
        loveInProgressRedisManager.incrementBindRank(toUid, uid, value);
    }

    private void taskHandle(BaseParam param, CoinGiftGivedModel coinGiftGivedModelList, ScenePrizeDO scenePrizeDO) {
        String taskCode = scenePrizeDO.getSceneCode();
        LoveInProgressEnums.TaskEnum taskEnum = LoveInProgressEnums.TaskEnum.getByTaskCode(taskCode);
        if (taskEnum == null) {
            return;
        }
        UserVO userVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), param.getUid());
        UserVO toUserVO = userFeignManager.getBasicWithCache(ServicesAppIdEnum.lanling.getAppId(), coinGiftGivedModelList.getToUid());
        if (loveInProgressRedisManager.getTaskClaimed(param.getUid(), taskCode) && userVO != null && SexType.MAN.getCode().equals(userVO.getSex().getCode())) {
            loveInProgressRedisManager.incrementTaskProgress(param.getUid(), taskCode, coinGiftGivedModelList.getProductCount());
        }


        if (loveInProgressRedisManager.getTaskClaimed(coinGiftGivedModelList.getToUid(), taskCode) && toUserVO != null && SexType.WOMAN.getCode().equals(toUserVO.getSex().getCode())) {
            loveInProgressRedisManager.incrementTaskProgress(coinGiftGivedModelList.getToUid(), taskCode, coinGiftGivedModelList.getProductCount());
        }


    }

}
