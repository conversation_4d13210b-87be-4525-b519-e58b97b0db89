package cn.yizhoucp.ump.biz.project.biz.manager.activity.activity520;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ump.biz.project.biz.constant.activity.Activity520Constant;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawConfig;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dao.DrawLogJpaDAO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 1. 奇遇抽奖奖池
 * 2. 男生 3 级奖励奖池
 * 3. 女生 3 级奖励奖池
 * 4. 心动爆灯玫瑰奖池
 * 5. 告白巧克力玫瑰奖池
 * 6. 表白情书玫瑰奖池
 * 7. 七彩玫瑰玫瑰奖池
 * 8. 花仙子玫瑰奖池
 * 9. 旷世恋人玫瑰奖池
 *
 * @author: lianghu
 */
@Service
public class Activity520DrawManager extends AbstractDrawTemplate {

    @Resource
    private Activity520BizManager activity520BizManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    protected LogComponent logComponent;

    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        // todo: 并发控制
        // 剩余抽奖次数验证
        Integer left = Collections.min(activity520BizManager.getRoseNumList(drawParam.getBaseParam()));
        if (left < drawParam.getTimes()) {
            throw new ServiceException(ErrorCode.MISS_PARAM, "剩余奇遇次数不足");
        }

        // 扣减奇遇次数
        activity520BizManager.decrRoseNum(drawParam.getBaseParam(), Activity520Constant.Activity520Rose.HMGDJ_GIFT, drawParam.getTimes());
        activity520BizManager.decrRoseNum(drawParam.getBaseParam(), Activity520Constant.Activity520Rose.FMGDJ_GIFT, drawParam.getTimes());
        activity520BizManager.decrRoseNum(drawParam.getBaseParam(), Activity520Constant.Activity520Rose.LMGDJ_GIFT, drawParam.getTimes());
        activity520BizManager.decrRoseNum(drawParam.getBaseParam(), Activity520Constant.Activity520Rose.UMGDJ_GIFT, drawParam.getTimes());
        activity520BizManager.decrRoseNum(drawParam.getBaseParam(), Activity520Constant.Activity520Rose.ZMGDJ_GIFT, drawParam.getTimes());
        activity520BizManager.decrRoseNum(drawParam.getBaseParam(), Activity520Constant.Activity520Rose.BMGDJ_GIFT, drawParam.getTimes());

        // 设置埋点信息
        context.setDrawConfig(DrawConfig.builder()
                .useTemplateTrace(Boolean.TRUE)
                .activityCode("2023_rose_manor").build());
    }

    @Override
    protected void draw(DrawContext context) {
        // 获取抽奖结果
        List<DrawPoolItemDTO> drawPoolItems = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(), context.getDrawParam().getTimes(), Boolean.TRUE);
        context.setPrizeItemList(drawPoolItems);
    }

    @Override
    protected void deductResource(DrawContext context) {

    }

    @Override
    protected void doCallback(DrawContext context) {
        // 添加记录
        DrawParam drawParam = context.getDrawParam();
        logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());
    }
}
