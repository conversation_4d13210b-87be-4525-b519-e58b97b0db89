package cn.yizhoucp.ump.biz.project.biz.manager.activity.eternalLove;

import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.enums.coin.TreasurePoolEnum;
import cn.yizhoucp.ms.core.base.util.AppUtil;
import cn.yizhoucp.ms.core.param.BaseParam;
import cn.yizhoucp.ms.core.vo.userservices.UserVO;
import cn.yizhoucp.starter.redis.manager.RedisManager;
import cn.yizhoucp.ump.api.vo.jimu.draw.inner.DrawLogItem;
import cn.yizhoucp.ump.biz.framework.aop.ActivityCheck;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.log.LogComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.component.notify.NotifyComponent;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.AbstractDrawTemplate;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawContext;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawLogParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.context.DrawParam;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.doDrawStrategy.common.ProbStrategy;
import cn.yizhoucp.ump.biz.project.biz.manager.activity.implement.draw.dto.DrawPoolItemDTO;
import cn.yizhoucp.ump.biz.project.biz.remoteService.user.FeignUserService;
import cn.yizhoucp.ump.biz.project.biz.util.DateUtil;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawLogDO;
import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.DrawPoolItemDO;
import cn.yizhoucp.ump.biz.project.dto.SendPrizeDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.ACTIVITY_CODE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.BROADCAST;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.CUR_FLOWER_GOD;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_DRAW;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_TAKE_PRIZE;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.FLOWER_GOD_TOKEN;
import static cn.yizhoucp.ump.biz.project.biz.constant.activity.EternalLoveConstant.GROWTH_VALUE;

@Service
@Slf4j
public class EternalLoveDrawManager extends AbstractDrawTemplate {

    @Resource
    private RedisManager redisManager;
    @Resource
    private ProbStrategy probStrategy;
    @Resource
    private LogComponent logComponent;
    @Resource
    private FeignUserService feignUserService;
    @Resource
    private NotifyComponent notifyComponent;
    @Resource
    private EternalLoveIndexManager eternalLoveIndexManager;
    @Resource
    private EternalLoveTrackManager eternalLoveTrackManager;

    @ActivityCheck(activityCode = ACTIVITY_CODE, isThrowException = false)
    @Override
    protected void resourceCheck(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();

        if (TreasurePoolEnum.ZHZ_GIFT.name().equals(poolCode) || TreasurePoolEnum.CXS1_GIFT.name().equals(poolCode) || TreasurePoolEnum.MXL_GIFT.name().equals(poolCode)) {
            drawParam.setNoSendPrize(Boolean.TRUE);
            return;
        }

        if ("BZJC".equals(poolCode)) {
            int flowerGodToken = Optional.ofNullable(redisManager.getInteger(String.format(FLOWER_GOD_TOKEN, drawParam.getUid()))).orElse(0);
            if (flowerGodToken < drawParam.getTimes()) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "当前花神令个数不够哦~");
            }
            redisManager.decrLong(String.format(FLOWER_GOD_TOKEN, drawParam.getUid()), drawParam.getTimes(), DateUtil.ONE_MONTH_SECOND);
            return;
        }

        if ("FLOWER_GOD_1_2".equals(poolCode) || "FLOWER_GOD_3_4".equals(poolCode) || "FLOWER_GOD_5_6".equals(poolCode)) {
            drawParam.setTimes(1);
            // TODO
            int curFlowerGod = Optional.ofNullable(redisManager.getInteger(String.format(CUR_FLOWER_GOD, AppUtil.splicUserId(drawParam.getUid(), Long.valueOf(drawParam.getExtValue()))))).orElse(0);
            if (("FLOWER_GOD_1_2".equals(poolCode) && curFlowerGod < 2) || ("FLOWER_GOD_3_4".equals(poolCode) && curFlowerGod < 4) || ("FLOWER_GOD_5_6".equals(poolCode) && curFlowerGod < 6)) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
            Set<Object> flowerGodDraw = Optional.ofNullable(redisManager.sGet(String.format(FLOWER_GOD_DRAW, AppUtil.splicUserId(drawParam.getUid(), Long.valueOf(drawParam.getExtValue()))))).orElse(Collections.emptySet());
            if (flowerGodDraw.contains(poolCode)) {
                throw new ServiceException(ErrorCode.INVALID_PARAM);
            }
            redisManager.sSetExpire(String.format(FLOWER_GOD_DRAW, AppUtil.splicUserId(drawParam.getUid(), Long.valueOf(drawParam.getExtValue()))), DateUtil.ONE_MONTH_SECOND, poolCode);
            return;
        }

        throw new ServiceException(ErrorCode.INVALID_PARAM);
    }

    @Override
    protected void draw(DrawContext context) {
        List<DrawPoolItemDTO> drawPoolItemDTOList = probStrategy.getDrawPoolItems(context.getDrawPoolItemDOS(),
                context.getDrawParam().getTimes(),
                Boolean.TRUE);
        context.setPrizeItemList(drawPoolItemDTOList);
    }

    @Override
    protected void doCallback(DrawContext context) {
        DrawParam drawParam = context.getDrawParam();
        String poolCode = drawParam.getPoolCode();

        if ("BZJC".equals(poolCode)) {
            logComponent.putDrawLog(drawParam.getBaseParam(), drawParam.getActivityCode(), context.getPrizeItemList());

            UserVO userVO = feignUserService.getBasic(context.getDrawParam().getUid(), context.getDrawParam().getAppId()).successData();
            if (userVO != null) {
                for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                    if (drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold() >= 52) {
                        redisManager.listLpush(BROADCAST, String.format("恭喜 %s 抽出【%s】", userVO.getName(), drawPoolItemDTO.getDrawPoolItemDO().getItemName()), DateUtil.ONE_MONTH_SECOND);
                    }
                }
                redisManager.listTrim(BROADCAST, 0, 60);
            }

            if (context.getPrizeItemList().size() == 1) {
                notifyComponent.npcNotify(drawParam.getUnionId(), drawParam.getUid(), String.format("亲爱的花神，恭喜你在“白泽奖池”活动中，抽中%s一个，礼物已经下发至您的背包，请注意查收噢。", context.getPrizeItemList().get(0).getDrawPoolItemDO().getItemName()));
            } else {
                StringBuilder msg = new StringBuilder("亲爱的花神，恭喜你在“白泽奖池”活动中，抽中");
                for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                    msg.append(drawPoolItemDTO.getDrawPoolItemDO().getItemName());
                    msg.append("、");
                }
                msg.deleteCharAt(msg.length() - 1);
                msg.append("礼物，礼物已经下发至您的背包，请注意查收噢。");
                notifyComponent.npcNotify(drawParam.getUnionId(), drawParam.getUid(), msg.toString());
            }

            context.setExtData(Optional.ofNullable(redisManager.getInteger(String.format(FLOWER_GOD_TOKEN, drawParam.getUid()))).orElse(0));
            for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                eternalLoveTrackManager.allActivityReceiveAward("baize_lottery", drawPoolItemDTO.getDrawPoolItemDO().getItemKey(), drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold(), drawPoolItemDTO.getDrawPoolItemDO().getItemNum() * drawPoolItemDTO.getTargetTimes(), drawParam.getUid());
            }
        }

        if ("FLOWER_GOD_1_2".equals(poolCode) || "FLOWER_GOD_3_4".equals(poolCode) || "FLOWER_GOD_5_6".equals(poolCode)) {
            Long uid = drawParam.getUid();
            Long toUid = Long.valueOf(drawParam.getExtValue());
            Long curFlowerGod = Optional.ofNullable(redisManager.getLong(String.format(CUR_FLOWER_GOD, AppUtil.splicUserId(uid, toUid)))).orElse(0L);
            Set<Object> flowerGodDraw = Optional.ofNullable(redisManager.sGet(String.format(FLOWER_GOD_DRAW, AppUtil.splicUserId(uid, toUid)))).orElse(Collections.emptySet());
            Set<Object> flowerGodTakePrize = Optional.ofNullable(redisManager.sGet(String.format(FLOWER_GOD_TAKE_PRIZE, AppUtil.splicUserId(uid, toUid)))).orElse(Collections.emptySet());
            if (flowerGodTakePrize.size() == 3 && curFlowerGod >= 6 && flowerGodDraw.size() == 3) {
                redisManager.delete(String.format(CUR_FLOWER_GOD, AppUtil.splicUserId(uid, toUid)));
                redisManager.delete(String.format(FLOWER_GOD_DRAW, AppUtil.splicUserId(uid, toUid)));
                redisManager.delete(String.format(FLOWER_GOD_TAKE_PRIZE, AppUtil.splicUserId(uid, toUid)));
                redisManager.delete(String.format(GROWTH_VALUE, AppUtil.splicUserId(uid, toUid)));
            }
            context.setExtData(eternalLoveIndexManager.confirmFriend(drawParam.getBaseParam(), toUid));
        }

        if ("FLOWER_GOD_1_2".equals(poolCode) || "FLOWER_GOD_3_4".equals(poolCode) || "FLOWER_GOD_5_6".equals(poolCode)) {
            if (Boolean.TRUE.equals(drawParam.getNoSendPrize())) {
                return;
            }
            BaseParam baseParam = drawParam.getBaseParam();
            Long toUid = Long.valueOf(drawParam.getExtValue());
            sendPrizeManager.sendPrize(BaseParam.builder().appId(baseParam.getAppId()).unionId(baseParam.getUnionId()).uid(toUid).build(), context.getPrizeItemList().stream().map(p -> SendPrizeDTO.of(p)).collect(Collectors.toList()));
            for (DrawPoolItemDTO drawPoolItemDTO : context.getPrizeItemList()) {
                eternalLoveTrackManager.allActivityReceiveAward(poolCode, drawPoolItemDTO.getDrawPoolItemDO().getItemKey(), drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold(), drawPoolItemDTO.getDrawPoolItemDO().getItemNum() * drawPoolItemDTO.getTargetTimes(), baseParam.getUid());
                eternalLoveTrackManager.allActivityReceiveAward(poolCode, drawPoolItemDTO.getDrawPoolItemDO().getItemKey(), drawPoolItemDTO.getDrawPoolItemDO().getItemValueGold(), drawPoolItemDTO.getDrawPoolItemDO().getItemNum() * drawPoolItemDTO.getTargetTimes(), toUid);
                notifyComponent.npcNotify(baseParam.getUnionId(), baseParam.getUid(), String.format("恭喜亲爱的花神，在”花神培育“中获得%s一个。礼物已经下发至您的背包，请注意查收噢。", drawPoolItemDTO.getDrawPoolItemDO().getItemName()));
                notifyComponent.npcNotify(baseParam.getUnionId(), toUid, String.format("恭喜亲爱的花神，在”花神培育“中获得%s一个。礼物已经下发至您的背包，请注意查收噢。", drawPoolItemDTO.getDrawPoolItemDO().getItemName()));
            }
        }
    }

    @Override
    protected List<DrawLogItem> drawLogWrapper(DrawLogParam param, List<DrawLogDO> drawLogDOList) {
//        drawLogDOList.sort(Comparator.comparingLong(d -> d.getCreateTime().toInstant(ZoneOffset.UTC).toEpochMilli()));
        List<DrawLogItem> drawLogItemList = drawLogDOList.stream().map(drawLogDO -> {
            DrawPoolItemDTO drawPoolItemDTO = JSON.parseObject(drawLogDO.getLogJson(), DrawPoolItemDTO.class);
            DrawPoolItemDO drawPoolItemDO = drawPoolItemDTO.getDrawPoolItemDO();
            return DrawLogItem.builder()
                    .icon(drawPoolItemDO.getItemIcon())
                    .valueGold(drawPoolItemDO.getItemValueGold())
                    .text(drawPoolItemDO.getItemName())
                    .itemNum(drawPoolItemDO.getItemNum() * drawPoolItemDTO.getTargetTimes())
                    .time(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(drawLogDO.getCreateTime())).build();
        }).collect(Collectors.toList());
        log.debug("drawLogItemList {}", JSON.toJSONString(drawLogItemList));
        Collections.reverse(drawLogItemList);

        return drawLogItemList;
    }

    @Override
    protected void deductResource(DrawContext context) {
    }

}
