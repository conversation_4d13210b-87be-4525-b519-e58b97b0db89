package cn.yizhoucp.ump.biz.project.biz.enums.activity.richMan;


import cn.yizhoucp.ms.core.base.util.RandomUtil;

import java.util.*;

/**
 * <AUTHOR> pepper
 * @Classname DiceWayEnum
 * @Description 获取骰子的点数
 * @Date 2022/5/12 17:44
 */
public class DiceWayEnum {

    private static List<List<Integer>> way = new ArrayList<>(100);

    static {
        //数组里面是骰子的点数
        way.add(Arrays.asList(6, 2, 1, 2, 4));
        way.add(Arrays.asList(6, 2, 1, 2, 5));
        way.add(Arrays.asList(6, 2, 1, 2, 1, 6));
        way.add(Arrays.asList(6, 2, 1, 2, 1, 5, 6, 6));
        way.add(Arrays.asList(6, 2, 1, 1, 5));
        way.add(Arrays.asList(6, 1, 2, 5, 3, 3, 2, 6));
        way.add(Arrays.asList(4, 1, 1, 3, 6, 2, 4));
        way.add(Arrays.asList(3, 3, 2, 1, 6));
        way.add(Arrays.asList(2, 2, 2, 1, 6));
        way.add(Arrays.asList(5, 3, 1, 2, 4));
        way.add(Arrays.asList(5, 2, 2, 2, 4));
        way.add(Arrays.asList(3, 2, 4, 2, 1, 6));
        way.add(Arrays.asList(2, 6, 1, 2, 4));
        way.add(Arrays.asList(4, 2, 1, 1, 5));
        way.add(Arrays.asList(1, 3, 2, 1, 6));
        way.add(Arrays.asList(2, 3, 4, 2, 4));
        way.add(Arrays.asList(4, 1, 1, 1, 2, 4));
        way.add(Arrays.asList(3, 3, 2, 1, 6));
        way.add(Arrays.asList(3, 2, 1, 5, 2, 4));
        way.add(Arrays.asList(2, 3, 1, 3, 2, 4));
        way.add(Arrays.asList(4, 1, 1, 3, 6, 2, 4));
        way.add(Arrays.asList(3, 3, 2, 1, 2, 4));
        way.add(Arrays.asList(4, 2, 1, 1, 5));
        way.add(Arrays.asList(5, 2, 2, 2, 1, 6));
        way.add(Arrays.asList(3, 2, 1, 3, 2, 4));
        way.add(Arrays.asList(1, 3, 1, 1, 4));
        way.add(Arrays.asList(4, 2, 1, 2, 4));
        way.add(Arrays.asList(1, 3, 3, 2, 5));
        way.add(Arrays.asList(1, 5, 5, 3, 5, 6));
        way.add(Arrays.asList(1, 5, 1, 1, 4));
        way.add(Arrays.asList(2, 4, 2, 3, 3, 5, 4));
        way.add(Arrays.asList(2, 4, 2, 3, 3, 5, 5));
        way.add(Arrays.asList(2, 4, 2, 3, 3, 5, 6));
        way.add(Arrays.asList(2, 4, 3, 2, 3, 6, 2, 4));
        way.add(Arrays.asList(2, 4, 3, 2, 3, 6, 2, 5));
        way.add(Arrays.asList(2, 4, 3, 2, 3, 6, 2, 6));
        way.add(Arrays.asList(2, 6, 1, 5, 4, 2, 4));
        way.add(Arrays.asList(2, 6, 1, 5, 4, 2, 5));
        way.add(Arrays.asList(2, 6, 1, 5, 4, 2, 6));
        way.add(Arrays.asList(2, 6, 1, 5, 3, 5, 4));
        way.add(Arrays.asList(2, 6, 1, 5, 3, 5, 5));
        way.add(Arrays.asList(2, 6, 1, 5, 3, 5, 6));
        way.add(Arrays.asList(2, 6, 1, 5, 3, 3, 6));
    }

    public static List<Integer> getDiceWay() {
        int num = getWayIndex();
        return getDiceWay(num);
    }

    public static List<Integer> getDiceWay(int index) {
        return way.get(index);
    }

    public static Integer getWayIndex() {
        int num = RandomUtil.getInstance().getNumByLimit(way.size());
        return num;
    }
}
