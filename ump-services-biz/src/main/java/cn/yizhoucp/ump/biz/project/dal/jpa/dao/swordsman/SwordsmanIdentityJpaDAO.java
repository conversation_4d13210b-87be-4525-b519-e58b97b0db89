package cn.yizhoucp.ump.biz.project.dal.jpa.dao.swordsman;

import cn.yizhoucp.ump.biz.project.dal.jpa.dataobject.swordsman.SwordsmanIdentityDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @description 剑客身份 DAO
 * @createDate 2024/11/16
 */
@Repository
public interface SwordsmanIdentityJpaDAO extends JpaRepository<SwordsmanIdentityDO, Long>, JpaSpecificationExecutor<SwordsmanIdentityDO>, CrudRepository<SwordsmanIdentityDO, Long> {


    SwordsmanIdentityDO findTop1ByUid(Long userId);

    @Query(value = "SELECT * FROM swordsman_identity WHERE id > ?1 ORDER BY id ASC LIMIT ?2", nativeQuery = true)
    List<SwordsmanIdentityDO> findNextBatchIds(Long lastId, int limit);

    @Query(value = "SELECT * FROM swordsman_identity WHERE id > ?1 and auction_ingot > 0 ORDER BY id ASC LIMIT ?2", nativeQuery = true)
    List<SwordsmanIdentityDO> findNextBatchIdsByAuctionIngot(Long lastId, int limit);

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set identity = ?2, physical = ?3, physical_update_time = ?4, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updateIdentityAndPhysical(Long userId, String identity, Integer physical, Long time);

    List<SwordsmanIdentityDO> findByUidIn(List<Long> userId);

    @Query(value = "select * from swordsman_identity order by level desc, level_update_time limit 21", nativeQuery = true)
    List<SwordsmanIdentityDO> findOrderByLevel();

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set ingot = ingot + ?2, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updateIngotByUid(Long uid, Long ingot);

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set ingot = ingot - ?2, auction_ingot = auction_ingot + ?2, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updateIngotAndAuctionIngotByUid(Long uid, Long auctionIngot);


    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set guard_end_time = ?2, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updateGuardTimeByUid(Long uid, Long time);

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set level = ?2, current_experience = ?3, next_experience = ?4, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updateExperienceAndLevel(Long uid, Integer level, Integer experience, Integer nextExperience);

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set physical = ?2, physical_update_time = ?3, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updatePhysicalAndTimeByUid(Long userId, Integer physical, Long physicalTime);

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set physical = physical + ?2, update_time = now() where uid = ?1", nativeQuery = true)
    Integer updatePhysicalByUid(Long userId, Integer physical);

    // 更新未出拍用户的金币
    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set ingot = ingot + auction_ingot * 0.85, auction_ingot = 0 where id in (?1)", nativeQuery = true)
    Integer updateNotAuctionUserIngot(List<Long> ids);

    @Transactional
    @Modifying
    @Query(value = "update swordsman_identity set auction_ingot = ?2 where uid = ?1", nativeQuery = true)
    Integer updateAuctionIngotByUid(Long uid, Long auctionIngot);

}
