common.error.code.000001=Server exception
common.error.code.1000=token has expired
common.error.code.1001=Re-login
common.error.code.987654=During peak traffic period, please visit later
common.error.code.100000=Server exception
common.error.code.100001=invalid param
common.error.code.100002=miss param:
common.error.code.100003=appKey does not match
common.error.code.100004=Wrong signature
common.error.code.100005=Need to log in
common.error.code.100006=message failed to send
common.error.code.188888=No permission
common.error.code.100007=MQ sending failed
common.error.code.100008=Has been blocked by the system
common.error.code.100009=The function is under maintenance, please try again later!
common.error.code.100010=open feign call failed
common.error.code.100011=Failed to send mq message
common.error.code.100014=Withdrawal ban
common.error.code.100015=Your function is restricted and cannot be used temporarily
common.error.code.100016=The operation is too fast, please try again later~
common.error.code.200000=The user failed to register with the IM server
common.error.code.200001=Login information error
common.error.code.200002=Already registered with the IM server
common.error.code.200003=User not registered or failed to register
common.error.code.200004=AppID/appKey/appSecret does not exist
common.error.code.200005=Callback has no content
common.error.code.200006=Illegal callback
common.error.code.200007=Dialog creation failed
common.error.code.200008=Failed to request NIM service
common.error.code.200009=Sessions other than this user cannot be deleted
common.error.code.200010=The user ID has been registered with Yunxin
common.error.code.200011=User dialogue does not exist
common.error.code.200012=There is a blacklist relationship, unable to start a conversation
common.error.code.200013=Risk control review request parameters cannot be empty
common.error.code.200014=The request parameter for obtaining the risk control review result cannot be empty
common.error.code.200015=Wind control system control
common.error.code.200016=The system pop-up request parameter cannot be empty
common.error.code.200017=UID equal to toUid cannot create a session
common.error.code.200018=app does not exist
common.error.code.300000=User does not exist or has been blocked
common.error.code.300001=JWT verification failed
common.error.code.300002=incorrect verification code
common.error.code.300004=Token has expired
common.error.code.300005=Token has expired
common.error.code.300006=RefreshToken and AccessToken do not match
common.error.code.300007=Incorrect user attribute format
common.error.code.300008=The following user data is abnormal: the unfollowed user is not in the current user's watch list
common.error.code.300009=Blocked user data is abnormal: The banned user is not in the current user's blacklist
common.error.code.300010=Blocked user data is abnormal: The banned user is not in the current user's blacklist
common.error.code.300011=Failed to get the result of real person authentication
common.error.code.300012=When blocking a user, the uid of the other party cannot be empty
common.error.code.300013=Failed to obtain IP address
common.error.code.300014=Failed to get device id
common.error.code.300015=WeChat access_token failed to obtain
common.error.code.300016=Wechat openid acquisition failed
common.error.code.300017=Failed to obtain WeChat user information
common.error.code.300018=Calculate user distance request parameter cannot be empty
common.error.code.300019=Wrong login type
common.error.code.300020=The user has been logged out
common.error.code.300021=Basic information is missing, please go back to the previous step and try again
common.error.code.300022=Upload failed, please try again!
common.error.code.300023=Login failed, please try to enter your mobile phone number to log in
common.error.code.300024=3 accounts have been registered today, come back tomorrow~
common.error.code.300025=User profile review failed
common.error.code.300026=Cannot register a new account within 3 months after canceling the account
common.error.code.4000001=APPID/KEY error
common.error.code.4000002=APP does not exist
common.error.code.4000003=Can't send SMS temporarily
common.error.code.4000004=Send SMS parameter error
common.error.code.4000005=SMS failed to be sent
common.error.code.4000006=Redis cache failed
common.error.code.4000007=Failed to get upload credentials
common.error.code.4000008=The request parameter for uploading or revoking push token is empty
common.error.code.4000009=Failed to upload push token
common.error.code.4000010=Failed to revoke push token
common.error.code.4000011=Send push request parameter is empty
common.error.code.4000012=Failed to send push
common.error.code.4000013=The parameter of the buried point cannot be empty
common.error.code.4000014=Dynamic does not exist or has been deleted
common.error.code.4000015=The comment does not exist or has been deleted
common.error.code.4000016=There is a black relationship and cannot be commented
common.error.code.4000017=Comment failed, dynamic review
common.error.code.4000018=DataRangers grouping is abnormal
common.error.code.4000019=Like failed, dynamic review is in progress
common.error.code.4000020=Dynamic audit failed
common.error.code.500001=Incomplete prepayment request parameters
common.error.code.500002=Verification request parameter cannot be empty
common.error.code.500003=Failed to call Alipay pre-payment interface
common.error.code.500004=Order exception: The order number returned by Alipay is not recorded locally
common.error.code.500005=Order exception: Alipay failed to return data to verify the signature
common.error.code.500006=Order exception: Alipay\u2019s returned order information does not match the local information
common.error.code.500007=Failed to call Alipay query interface
common.error.code.500008=Calling the Alipay query interface returns empty
common.error.code.500009=Operation database exception
common.error.code.500010=Failed to call the WeChat pre-payment interface
common.error.code.500011=Failed to call WeChat sdk signature
common.error.code.500012=Prepaid WeChat return content is empty
common.error.code.500013=Verify that the request content is empty
common.error.code.500014=Verification request uid cannot be empty
common.error.code.500015=Verification request productId cannot be empty
common.error.code.500016=Verification request orderNo cannot be empty
common.error.code.500017=The verification request receipt cannot be empty
common.error.code.500018=There is no corresponding product for productId, or the product is unavailable
common.error.code.500019=When paying with JSAPI, openId cannot be empty
common.error.code.500020=The callback of the current payment type is not supported
common.error.code.500021=The payment amount cannot be empty
common.error.code.500022=The current user has no account information
common.error.code.500023=Insufficient balance
common.error.code.500024=When order query, id cannot be empty
common.error.code.500025=Order id does not exist
common.error.code.500027=Failed to decode Alipay callback parameters
common.error.code.500028=Failed to initialize Alipay client
common.error.code.600001=feedId cannot be empty
common.error.code.600002=The feedId of the deleted feed does not exist in the database
common.error.code.600003=The commentId of the deleted comment does not exist in the database
common.error.code.600004=likeId cannot be empty
common.error.code.600005=The unliked likeId does not exist in the database
common.error.code.700001=Failed to generate coin order
common.error.code.700002=coin balance deduction failed
common.error.code.700003=Duplicate recharge order
common.error.code.700004=coin order storage failed
common.error.code.700005=coin increase failed
common.error.code.700006=Account does not exist
common.error.code.700007=Insufficient balance
common.error.code.700008=app account does not exist
common.error.code.700009=Insufficient paid points
common.error.code.700010=coin gift does not exist
common.error.code.700011=Failed gift delivery
common.error.code.700012=Insufficient coins, please recharge
common.error.code.700013=Request parameter error
common.error.code.700014=Failed to modify the remoteConfig configuration class, please contact the developer
common.error.code.700015=The gift key that needs to be sent to the backpack cannot be empty
common.error.code.700016=Can only be given during the Mid-Autumn Festival
common.error.code.700017=Silver Rabbit and Golden Rabbit cannot be given away
common.error.code.800001=Sign in already exists
common.error.code.800002=Failed to strike up a conversation
common.error.code.800003=Failed to follow, has been blocked by the other party
common.error.code.800004=When following users, the uid of the other party cannot be empty
common.error.code.800005=Get user homepage, uid cannot be empty
common.error.code.800006=Real person authentication failed
common.error.code.800007=Failed to get the result of real person authentication
common.error.code.800008=Failed to obtain user basic information
common.error.code.800009=Failed to obtain user preference information
common.error.code.800010=Failed to follow, the other party has been blocked
common.error.code.800011=Failed to obtain coin balance
common.error.code.800012=Failed to get recharged product
common.error.code.800013=The request parameter for uploading push token is empty
common.error.code.800014=Failed to upload push token
common.error.code.800015=Reports of the same type to the same user are limited to once a day
common.error.code.800016=When obtaining the number of draws, the request parameter cannot be empty
common.error.code.800017=Insufficient draws
common.error.code.8000190=Alipay information does not match the binding information
common.error.code.8000200=The order does not exist or has been processed
common.error.code.800021=Lottery chanceId error
common.error.code.800022=Member does not exist or has insufficient level
common.error.code.8000230=User information does not exist in es
common.error.code.8000240=When getting my guardian, the request parameter cannot be empty
common.error.code.8000250=The request parameter cannot be empty when getting the guardian me user information
common.error.code.8000260=Failed to get task list
common.error.code.8000270=Voice matching has not started, please go to the app store to upgrade to the latest version
common.error.code.8000280=Video matching has not started, please go to the app store to upgrade to the latest version
common.error.code.8000290=The current user is not in the matching queue
common.error.code.8000300=Sign-in failed, please try again later
common.error.code.8000310=Failed to verify intimacy
common.error.code.8000320=You have checked in
common.error.code.8000330=Failed to obtain inspection-free users
common.error.code.8000340=This gift does not support giving to multiple people at the same time
common.error.code.8000350=Failed to obtain user video call duration
common.error.code.8000360=Failed to obtain user video call reward points
common.error.code.8000370=System configuration not configured
common.error.code.8000380=The user's free or paid part does not meet the requirements
common.error.code.8000390=Sender decoding exception
common.error.code.8000400=The current status of the goddess cannot be modified
common.error.code.8000410=Failed to manually activate Noble
common.error.code.8000420=Goddess data does not exist
common.error.code.8000430=Failed to modify the family limit to 500
common.error.code.8000440=Real person certification is still in certification
common.error.code.8000450=I have talked up, you can talk up again at 5:00 the next day after hitting up
common.error.code.8000010=Failed to call the text risk control detection interface
common.error.code.8000011=Picture risk control detection interface call failed
common.error.code.8000012=Face contrast detection interface call failed
common.error.code.8000013=The entered name does not match the real-name authentication
common.error.code.8000014=The difference between the photo and the real person authentication is too large, and the submission fails
common.error.code.8000015=Failed to call the real-name authentication interface
common.error.code.8000016=Replacement failed, review is already in progress
common.error.code.8000017=Text violation
common.error.code.800018=Real-name authentication failed
common.error.code.800019=Failed to obtain real-name authentication result
common.error.code.800020=Insufficient points balance
common.error.code.8000210=Order does not exist
common.error.code.8000220=The Excel format is incorrect
common.error.code.800023=This product is a one-time redemption and cannot be redeemed repeatedly
common.error.code.800024=The number of words has exceeded the limit
common.error.code.800025=Request parameter error
common.error.code.800026=The family name already exists, please try another name
common.error.code.800027=Failed to create family
common.error.code.800028=Creating a family requires Charisma/Hero value to reach level 3
common.error.code.800029=Family nickname or picture is suspected of violation, please try again
common.error.code.800030=The family applying for joining does not exist
common.error.code.800031=The family has been disbanded
common.error.code.800032=Successfully applied
common.error.code.800033=Cannot apply to join within 7 days of being kicked out of the family
common.error.code.800034=Has joined the family currently and cannot be created
common.error.code.800035=Currently you have joined another family, please log out and apply to join
common.error.code.800036=Application processed
common.error.code.800037=The number of family members has reached the upper limit
common.error.code.800038=Review failed, please try again later
common.error.code.800039=Failed to get chat list
common.error.code.800040=Failed to join the family as a tourist
common.error.code.800041=Insufficient permissions
common.error.code.800042=The user has left the family
common.error.code.800043=Kick failed
common.error.code.800044=Failed to quit, you are no longer in the family
common.error.code.800045=Failed to leave the family
common.error.code.800046=Failed to give gift
common.error.code.800047=The number of deputy chiefs has reached the upper limit
common.error.code.800048=This user has been banned
common.error.code.800049=The user already has a family and cannot be approved
common.error.code.800050=The family is not yet open to the public
common.error.code.800051=Failed to dissolve the family
common.error.code.800052=Failed to edit family name or profile picture
common.error.code.800053=Cannot appoint visitors
common.error.code.800054=The number of family tourists has reached the upper limit
common.error.code.800055=The current version is too low, please go to the app store to upgrade to the latest version
common.error.code.800056=User registration audio and video match free time gift/non-send configuration does not exist
common.error.code.800057=Red envelope at least 50 coins
common.error.code.800058=Send at least 3 red packets
common.error.code.800059=Up to 50 red envelopes
common.error.code.800060=Recharge nobles exceeds the upper limit
common.error.code.800061=The current user is already in the goddess application list, please do not repeat the operation
common.error.code.800062=It is currently muted and cannot be sent
common.error.code.800063=You have left the family and cannot send speakers
common.error.code.800064=Please re-operate the application by mistake
common.error.code.800065=You currently do not have operation authority, please contact the administrator
common.error.code.800066=The current system is under maintenance, please log in later
common.error.code.800067=Search ID requires real person authentication
common.error.code.800068=This user is a king and cannot be banned or kicked out~
common.error.code.800069=The current version is too low and the dress up cannot be displayed normally, please upgrade to version 2.26 and above~
common.error.code.800070=The current version is lower and does not support withdrawals, please upgrade to the latest version (v2.36.0 and above)
common.error.code.800071=Account contains Chinese or Chinese punctuation
common.error.code.810001=The other party exits the video match
common.error.code.810002=The other party\u2019s video is matched successfully
common.error.code.900010=The other party\u2019s intimacy has not reached 2, so I can\u2019t make voice calls temporarily
common.error.code.900011=There is a black relationship, unable to call
common.error.code.900012=Insufficient coins, unable to call
common.error.code.900013=Could not create new voice session
common.error.code.900014=Order deduction failed
common.error.code.900015=Failed to initiate a voice call
common.error.code.900016=The other party\u2019s intimacy has not reached 2, so I can\u2019t make video calls temporarily
common.error.code.900017=Unable to create new video session
common.error.code.900018=The current version is too low, please go to the app store to upgrade to the latest version
common.error.code.900019=The other party has set up not to accept calls, please send a gift to tease him~
common.error.code.900020=The other party has set up not to accept calls, please send a gift to tease him~
common.error.code.900021=Audio call order does not exist
common.error.code.900022=Video call order does not exist
common.error.code.900023=Today, the video hitch-up times are all used up~
common.error.code.900024=The user is livestreaming~
common.error.code.900025=The user is video~
common.error.code.110001=Insufficient coins (voice matching)
common.error.code.110002=Insufficient coins (video matching)
common.error.code.110003=Insufficient coins (voice call)
common.error.code.110004=Insufficient coins (video call)
common.error.code.110005=Insufficient coins (gift from personal homepage)
common.error.code.110006=Insufficient coins (video match to send gifts)
common.error.code.110007=Insufficient coins (gift for video call)
common.error.code.110008=Insufficient coins (chat to give gifts)
common.error.code.110009=Insufficient coins (Guardian list to send gifts)
common.error.code.110010=Insufficient coins (to strike up a conversation)
common.error.code.110011=Insufficient coins (chat)
common.error.code.110012=Insufficient coins (a family gift)
common.error.code.110013=Insufficient coins (chat with family tourists)
common.error.code.1100134=Insufficient coins (chat room gift-giving adaptation client)
common.error.code.110015=Insufficient coins (gift in single live broadcast room)
common.error.code.110016=Insufficient coins (gift in three-person live room)
common.error.code.110017=Insufficient coins (gift in auction room)
common.error.code.120001=Insufficient coins-first charge (voice matching)
common.error.code.120002=Insufficient coins-first charge (video matching)
common.error.code.120003=Insufficient coins-first charge (voice call)
common.error.code.120004=Insufficient coins-first charge (video call)
common.error.code.120005=Insufficient coins-first charge (gift from personal homepage)
common.error.code.120006=Insufficient coins-first charge (video match to send gifts)
common.error.code.120007=Insufficient coins-first charge (gift for video call)
common.error.code.120008=Insufficient coins-first charge (chat to send gifts)
common.error.code.120009=Insufficient coins-First Charge (Gift from Guardian List)
common.error.code.120010=Insufficient coins-first charge (to strike up a chat)
common.error.code.120011=Insufficient coins-first charge (chat)
common.error.code.120012=Insufficient coins-First Charge (Family Gift)
common.error.code.120013=Insufficient coins-first charge (chat with family visitors)
common.error.code.120014=Insufficient coins-first charge (gift in chat room)
common.error.code.120015=Insufficient coins-First Charge (Gift in Single Live Room)
common.error.code.120016=Insufficient coins-first charge (gift in three-person live room)
common.error.code.120017=Insufficient coins-first charge (gift in auction room)
common.error.code.130001=The configuration key already exists, please rename
common.error.code.130002=JSON format error
common.error.code.140001=Insufficient coins (family speakers)
common.error.code.150001=The desired result was not found
common.error.code.150002=The seat is already occupied, please change the seat
common.error.code.1500020=The user is already in the room's microphone position and cannot be switched directly
common.error.code.150003=No matching user information found
common.error.code.150005=Insufficient user rights
common.error.code.150004=Mai failed
common.error.code.1500050=Failed to download wheat
common.error.code.150006=operation failed
common.error.code.150007=Administrator setup failed
common.error.code.150008=Failed to edit room
common.error.code.150009=Soundnet failed to kick users out
common.error.code.150010=Failed to request Yunxin interface
common.error.code.150011=Error creating live room
common.error.code.160001=Failed to claim the task reward
common.error.code.160002=No task record found
common.error.code.160003=Task not completed
common.error.code.160004=Reward has been collected
common.error.code.160005=Task has been completed
common.error.code.160006=Task processing failed
common.error.code.160007=The new round of red envelopes has been updated, please try again~
common.error.code.170001=Application push configuration not found
common.error.code.170002=User client ID not found
common.error.code.170003=Error parsing push configuration data
common.error.code.170004=Failed to request push data
common.error.code.170005=User CID not found
common.error.code.170006=User group not found
common.error.code.170007=Parameter setting error
common.error.code.170008=Byte user group not found
common.error.code.170009=Push title title is too long
common.error.code.170010=Push content body is too long
common.error.code.170011=A single push group message can be sent up to 200 messages at a time
common.error.code.170012=Up to 1000 group messages at a time

# RedPackageManager
simple.red.packet.no.exist=Red envelope does not exist\uFF01
splice.send.red.packet={0} red envelope
splice.send.red.packet.in.family=Send red envelopes in family {0}
splice.send.red.packet.in.chatroom=Send red envelopes in family {0}
splice.send.red.packet.in.voice.room=Send a red envelope in the speech room {0}
splice.get.coin=Grab {0} coins
splice.get.user.red.packet=Grab the red envelope of {0} {1}
splice.red.packet.expired=Red envelope {0} expired and returned
simple.red.packet.expired.desc=More than 24 hours\r\nthe red packets has expired
simple.red.packet.loot.all.desc=Sorry, no coins left
simple.red.packet.already.grabbed.desc=The user has already grabbed the red envelope
simple.red.packet.not.family.member.desc=You have not joined the family\r\nvisitors cannot draw 
simple.red.packet.coin.not.enough.desc=Coins shortage
simple.red.packet.not.can.grab.time.desc=No time available

splice.balance.not.enough=Your current coins are insufficient, please recharge
splice.family.draw.barrage=Congrats to {0} for getting {1}*{2}
splice.family.draw.html.barrage=Congrats to {0} for getting <img src="{1}?width=42&height=42"> * {2}({3} coin) in Round draw
##\u9ED8\u8BA4\u5F39\u5E55\u6587\u6848
splice.family.draw.default.barrage=Lucky draw to win great prizes\uFF01

##\u9ED8\u8BA4\u6263\u94B1\u6587\u6848
splice.family.draw.deduct.coin=Lucky Lottery

## \u793C\u7269\u540D\u79F0\u56FD\u9645\u5316
splice.draw.gift.name.rich_yacht=Fantasy yacht
splice.draw.gift.name.WNZX_GIFT=Pick the stars
splice.draw.gift.name.gift_lifetime=Love you forever
splice.draw.gift.name.NSHG_GIFT=goddess crown
splice.draw.gift.name.GZX_GIFT=Princess shoes
splice.draw.gift.name.BBX_GIFT=Teddy bear
splice.draw.gift.name.BQL_GIFT=Ice cream
splice.draw.gift.name.eat_melon=Eat melon
splice.draw.gift.name.CQHH_GIFT=wreath
splice.draw.gift.name.XYXX_GIFT=Wish star
splice.draw.gift.name.CBHL_GIFT=Castle wedding
splice.draw.gift.name.moon_goodness=Moon goodness
splice.draw.gift.name.dream_train=Dream train
splice.draw.gift.name.Star_rabbit=Star rabbit
splice.draw.gift.name.with_you=with you
splice.draw.gift.name.fast_my_love=fast my love
splice.draw.gift.name.Rolls_Royce=Rolls Royce
splice.draw.gift.name.Life_guard=Life guard
splice.draw.gift.name.XYC_GIFT=Rumput Keberuntungand
splice.draw.gift.name.ZZNC_GIFT=Teh Susu Mutiara
splice.draw.gift.name.SJX_GIFT= Sepatu Kristal
splice.draw.gift.name.FHSJ_GIFT=Bunga-bunga yang berlimpah
splice.getMissionList.gift.name.Rolls_Royce=Rolls Royce
splice.getMissionList.gift.name.fast_my_love=fast my love
splice.getMissionList.gift.name.MYALS_GIFT=Wonderland Alice
splice.getMissionList.gift.name.FHSJ_GIFT=Prosperous flowers
splice.getMissionList.gift.name.ZNDGZ_GIFT=Be your princess
splice.getMissionList.gift.name.ADSYJ_GIFT=Love radio
splice.getMissionList.gift.name.ZZNC_GIFT=Pearl Milk Tea
splice.getMissionList.gift.name.XYC_GIFT=Lucky Grass

splice.family.draw.tag.rate=rate
splice.family.draw.tag.luck=luck

## \u798F\u888B\u6D3B\u52A8
simple.lucky.bag.activity.tips=10 draws \uFF1D 1 diamond
splice.lucky.bag.board.self.tips=need \uD83D\uDC8E {0} to surpass the previous
simple.lucky.bag.name=Round draw activity
simple.lucky.bag.gift.name=heart clash
simple.lucky.bag.frame.name=frame
simple.lucky.bag.point=20 point
simple.lucky.bag.coin=30 coin
simple.lucky.bag.gift.desc=heart clash\n666 coin
simple.lucky.bag.frame.desc=frame\n x 3 day
simple.lucky.bag.point.desc=20 point\n90 coin
simple.lucky.bag.coin.desc=30 coin\n30 coin
simple.coin.send.user.tips=Distribute coin
simple.point.send.user.tips=Distribute coin point
splice.lucky.bag.reward.first.tips=\uD83D\uDC90Congratulations! You won the top 5 in last week's Round draw Hunter rank, and got gifts: 800 coin\u3001Romance train\u3001Unicorns, which have been automatically distributed, please pay attention to check
splice.lucky.bag.reward.second.tips=\uD83D\uDC90Congratulations! You won the top 5 in last week's Round draw Hunter rank, and got gifts: 500 coin\u3001Unicorns, which have been automatically distributed, please pay attention to check
splice.lucky.bag.reward.third.tips=\uD83D\uDC90Congratulations! You won the top 5 in last week's Round draw Hunter rank, and got gifts: 300 coin\u3001Unicorns, which have been automatically distributed, please pay attention to check

simple.lucky.bag.first.task=Coin x10
simple.lucky.bag.second.task=Chocolate\n52 coin
simple.lucky.bag.third.task=angle\n3 day
simple.lucky.bag.fourth.task=Surprise box\n299coin
simple.lucky.bag.fifth.task=kongmin light\n999coin


# saka \u51B2\u4E0A\u4E91\u9704\u6587\u6848
simple.activity.saka.thesky.resource.check=Not enough draw tickets
simple.activity.saka.thesky.50.coupon=Like, you have contributed 50 medals to the honor of the family! The family will send you an honor ticket to unlock the air blind box<a href="{0}">\uD83D\uDC49 Click to unlock the air blind box\uD83D\uDC49 ~</a>
simple.activity.saka.thesky.pk.coin.log=Daily PK Rewards
simple.activity.saka.thesky.complete.task=You have welcomed a new member once and won 3 medals, go chat with the new member<a href="{0}">\uD83D\uDC49 Click to enter the family\uD83D\uDC48~</a>
simple.activity.saka.thesky.family.limit.desc=The victor must have a minimum number of medals {0}
simple.activity.saka.thesky.draw.item1=Feather fan
simple.activity.saka.thesky.draw.item2=Love on the Cloud
simple.activity.saka.thesky.draw.item3=Come on Duck
simple.activity.saka.thesky.draw.item4=Wish Charm
simple.activity.saka.thesky.draw.item5=Confession card
simple.activity.saka.thesky.draw.item6=Phoenix Yufei
simple.activity.saka.thesky.draw.item7=Feather Feather Fan
simple.activity.saka.thesky.draw.item8=Romantic Galaxy
simple.activity.saka.thesky.draw.item9=Family Honor Certificate
simple.activity.saka.thesky.draw.barrage={0} Unlocked in the air blind box to the {1}
simple.activity.saka.thesky.task.title=Welcome newcomers to the family
simple.activity.saka.thesky.task.desc=(3 medals available)
simple.activity.saka.thesky.medals={0} contributed {1} medals to the {2} family
simple.activity.saka.thesky.airbox={0} Unlocked in the air blind box to the Supreme Phoenix Flying Car, Diamond Portrait Frame, 5 Gold, Supersonic Approach Effect, Confession Card Gift, 1 Gold Coin, Flower Rain Approach Effect
simple.activity.saka.thesky.collect=Collect medals and fight for family honor Collect {0} medals this week and have {1} honor coupons
simple.activity.saka.thesky.go=go
simple.activity.saka.thesky.gift=Ding, receive/send heart burst lights/invincible lucky stars/love circles, and win {0} medals for the family! Help the family to win, the more contributions, the more PK victory gold will be shared\uD83D\uDCB0
simple.activity.saka.thesky.50medals=Like, you have contributed 50 medals to the honor of the family! The family will send you an honor ticket to unlock the air blind box \uD83D\uDC49<a href={0}>\uD83D\uDC49 Click to unlock the air blind box \uD83D\uDC48~ </a>
simple.activity.saka.thesky.backward=Come on! Your family is about to lose! Win medals for the family and share the PK victory gold! \uD83D\uDC49 <a href={0}>\uD83D\uDC49Click to help the family! </a>
simple.activity.saka.thesky.ahead=Your family was far ahead in yesterday's honor battle, the family is honored because of you, and the PK reward has been issued to your account \uD83D\uDC49<a href={0}>\uD83D\uDC49 Click to continue to help! \uD83D\uDC48</a>
simple.activity.saka.thesky.1 =Congrats to your family on winning this week's Honors Tournament! 3 Starry Night Boats will be issued to your backpack (you can freely choose the active member to give it), keep it up!
simple.activity.saka.thesky.2 =Congrats to your family on winning this week's Honors runner-up! 3 good night dear will be distributed to your backpack (you can freely choose the active member to give it), continue to work hard!
simple.activity.saka.thesky.3 =Congratulations to your family for this week's honorary runner-up!\u00A0 3 love lanterns will be issued to your backpack (you can freely choose active members to give away), continue to rush!
simple.activity.saka.thesky.guardian=Congrats for being the best family guardian yesterday~ The family shines because of you, the dragon in the cloud avatar frame and the king's return car have been issued for you, go and wear it~ \uD83D\uDC49<a href={0}>\uD83D\uDC49 Click to enter my dress up center \uD83D\uDC48</a>



